'use client'
import styles from '../../styles/ActivateButton.module.css'

type Props = {
  status: boolean
  onRequestToggle: () => void
}

const ActivateButton = ({ status, onRequestToggle }: Props) => {
  return (
    <button
      className={`${styles.button} ${status ? styles.deactivate : styles.activate}`}
      onClick={onRequestToggle}
    >
      {status ? 'Desactivar' : 'Activar'}
    </button>
  )
}

export default ActivateButton
