import { Transactions } from '@/types/types'
import DispersionTable from './DispersionTable'
import Pagination from '../../Pagination'
import ClarificationTableStyles from '../../styles/ClarificationTable.module.css'
import { useState, useEffect, useCallback } from 'react'
import DateFilter from '../../DateFilter'
// import SearchBar from '../../SearchBar'
import styles from '../../styles/Transaction.module.css'
// import FilterButton from './FilterButton'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import apiClient from '@/api/client'
import { AdminByUserIdClient } from '@/types/admin/types'
import { getThreeMonthRange } from '../../../../hooks/useListTransactions'
// import FilterButton from './FilterButton'

type Props = {
  data?: Transactions[]
  adminsByUserId?: AdminByUserIdClient[] // Usa el tipo correcto si lo tienes (por ejemplo: Admin[])
}

interface TransactionResponse {
  transactions: Transactions[]
  total: number
}

const Dispersions: React.FC<Props> = ({}) => {
  const [currentPage, setCurrentPage] = useState(1)
  const [limit] = useState(20)
  const [total, setTotal] = useState(0)
  // const [searchTerm, setSearchTerm] = useState('')
  const [selectedDates, setSelectedDates] = useState<string[]>([])
  const [transactions, setTransactions] = useState<Transactions[]>([])
  const { user } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()

  const fetchTransactions = useCallback(async () => {
    try {
      const managerId = user?.relUserRoleAdmins[selectedAdminIndex]?.admin?.managerId || null
      if (!managerId) return

      const { initialDate, endDate } = getThreeMonthRange(selectedDates[0], selectedDates[1])

      const response = await apiClient.get<TransactionResponse>(
        `/user-account/${managerId}/transactions`,
        {
          params: {
            page: currentPage,
            limit,
            initDate: selectedDates[0] ? initialDate : null,
            finalDate: selectedDates[1] ? endDate : null,
          },
        }
      )

      setTransactions(response.data.transactions)
      setTotal(response.data.total)
    } catch (error) {
      console.error('Error fetching transactions:', error)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id, currentPage, limit, selectedDates, selectedAdminIndex])

  useEffect(() => {
    fetchTransactions()
  }, [fetchTransactions])

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1)
  }

  const handleNextPage = () => {
    const totalPages = Math.ceil(total / limit)
    if (currentPage < totalPages) setCurrentPage(currentPage + 1)
  }

  // const handleSearch = () => {
  //   // setSearchTerm(value)
  //   setCurrentPage(1)
  // }

  const handleDateFilter = (dates: string[]) => {
    setSelectedDates(dates)
    setCurrentPage(1)
  }

  return (
    <div style={{ marginTop: '56px' }}>
      <div className={styles.newClientContainer}>
        {/* <FilterButton /> */}
        <DateFilter onDateChange={handleDateFilter} mode="range" />
        {/* <SearchBar placeholder="Buscar" onSearch={handleSearch} /> */}
      </div>
      <p className={ClarificationTableStyles.tableTitle}>Transferencias realizadas</p>
      <DispersionTable data={transactions} />
      <Pagination
        currentPage={currentPage}
        totalPages={Math.ceil(total / limit)}
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        onNavigatePage={setCurrentPage}
      />
    </div>
  )
}

export default Dispersions
