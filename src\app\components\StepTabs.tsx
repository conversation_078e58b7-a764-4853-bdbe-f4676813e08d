import { ReactNode, isValidElement, ReactElement } from 'react'
import styles from './styles/StepTabs.module.css'
import { FaArrowLeft } from 'react-icons/fa'
import Button from '@/app/components/Button'

type StepTabsProps = {
  steps: readonly string[]
  stepTitles: string | readonly string[]
  activeStep: number
  onStepChange: (step: number) => void
  onBack?: () => void
  onNext?: () => void
  children: ReactElement<StepTabsPanelProps> | ReactElement<StepTabsPanelProps>[]
  buttonText?: string
  disableNextButton?: boolean
  topButton?: ReactNode
  isOverrideStep?: boolean
}

type StepTabsPanelProps = {
  index: number
  children: ReactNode
}

function Panel({ children }: StepTabsPanelProps) {
  return <>{children}</>
}

export function StepTabs({
  steps,
  stepTitles,
  activeStep,
  onStepChange,
  onBack,
  onNext,
  children,
  disableNextButton,
  buttonText = 'Siguiente',
  isOverrideStep = false,
  topButton,
}: StepTabsProps) {
  const childrenArray = Array.isArray(children) ? children : [children]

  return (
    <div className={styles.container}>
      {onBack && (
        <button className={styles.backButton} onClick={onBack}>
          <FaArrowLeft size={16} className={styles.icon} /> Atrás
        </button>
      )}
      <h1 className={styles.title}>
        {Array.isArray(stepTitles) ? stepTitles[activeStep] : stepTitles}
      </h1>
      {topButton}
      <div className={styles.tabContainer}>
        {steps.map((label, index) => (
          <button
            key={label}
            className={index === activeStep ? styles.activeTab : styles.tab}
            onClick={() => onStepChange(index)}
            disabled={!isOverrideStep ? index > activeStep : false}
          >
            {label}
          </button>
        ))}
      </div>
      {childrenArray.map(child => {
        if (!isValidElement(child)) return null
        return child.props.index === activeStep ? child : null
      })}
      {onNext && (
        <Button text={buttonText} onClick={onNext} fullWidth disabled={disableNextButton} />
      )}
    </div>
  )
}

StepTabs.Panel = Panel
