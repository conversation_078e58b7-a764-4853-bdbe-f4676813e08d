'use client'
import React from 'react'
import styles from './styles/Button.module.css'

type ButtonProps = {
  text: string
  icon?: React.ReactNode // Permite añadir un icono al botón
  onClick?: () => void
  className?: string // Permite añadir clases adicionales
  fullWidth?: boolean // Permite hacer el botón de ancho completo
  disabled?: boolean // Permite deshabilitar el botón
  type?: 'button' | 'submit' // Tipo de botón, por defecto es "button"
}

const ButtonOutlined: React.FC<ButtonProps> = ({
  text,
  icon,
  onClick,
  className,
  fullWidth,
  disabled,
  type,
}) => {
  return (
    <button
      className={`${styles.button} ${styles.buttonOutlined} ${fullWidth ? styles.fullWidth : ''} ${className || ''}`}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      {icon}
      {text}
    </button>
  )
}

export default ButtonOutlined
