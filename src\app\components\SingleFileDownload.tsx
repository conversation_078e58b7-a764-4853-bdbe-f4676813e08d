/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react'
import Image from 'next/image'
import styles from './styles/FileUpload.module.css'

type SingleFileDownloadProps = {
  label: string
  fileUrl: string
  iconSrc?: string
}

const SingleFileDownload = ({
  label,
  fileUrl,
  iconSrc = '/images.svg',
}: SingleFileDownloadProps) => {
  const isValidImage = fileUrl.includes('finberry-convenia-media.s3.us-east-2.amazonaws.com')
  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = fileUrl

    link.setAttribute('download', fileUrl.split('/').pop() || 'archivo')
    link.setAttribute('target', '_blank')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className={styles.inputContainer} onClick={handleDownload}>
      <label className={styles.uploadedFile}>
        <div className={styles.uploadedContainer}>
          <Image src={isValidImage ? fileUrl : iconSrc} alt="Icono" width={60} height={60} />
          <p>{label}</p>
        </div>
      </label>
    </div>
  )
}

export default SingleFileDownload
