import { create } from 'zustand'
import { Clarification, ClarificationStatus } from '@/types/clarification/types'
import { getAllClarifications, getClarificationById } from '@/api/endpoints/clarification'
import { updateClarificationStatus as apiUpdateClarificationStatus } from '@/api/endpoints/clarification'

type State = {
  rawClarifications: Clarification[]
  clarifications: Clarification[]
  selectedClarification: Clarification | null
  isLoading: boolean
  error: string | null
  currentPage: number
  limit: number
  totalPages: number
  search: string
}

type Actions = {
  handleSearch: (term: string) => void
  setCurrentPage: (page: number) => void
  applyFilters: () => void
  fetchClarifications: (params: { page: number; limit: number }) => Promise<void>
  fetchClarificationById: (id: string) => Promise<void>
  selectClarification: (id: string) => Clarification | null
  reset: () => void
  updateClarificationStatus: (trackingNumber: string, newStatus: ClarificationStatus) => void
}

export const useClarificationStore = create<State & Actions>((set, get) => ({
  rawClarifications: [],
  clarifications: [],
  selectedClarification: null,
  isLoading: false,
  error: null,
  currentPage: 1,
  limit: 5,
  totalPages: 0,
  search: '',

  handleSearch: (term: string) => {
    set({ search: term, currentPage: 1 })
    get().applyFilters()
  },

  setCurrentPage: (page: number) => {
    set({ currentPage: page })
    get().fetchClarifications({ page, limit: get().limit })
  },

  applyFilters: () => {
    const { rawClarifications, search } = get()
    const filtered = rawClarifications.filter(c =>
      [c.user.email, c.user.name, c.type, c.status, c.trackingNumber].some(field =>
        field.toLowerCase().includes(search.toLowerCase())
      )
    )
    set({ clarifications: filtered })
  },

  fetchClarifications: async ({ page, limit }) => {
    set({ isLoading: true, error: null })
    try {
      const { clarifications, totalRows } = await getAllClarifications({ page, limit })
      const totalPages = Math.ceil(totalRows / limit)
      set({
        rawClarifications: clarifications,
        totalPages,
        currentPage: page,
      })
      get().applyFilters()
    } catch (error) {
      set({ error: (error as Error).message })
    } finally {
      set({ isLoading: false })
    }
  },

  fetchClarificationById: async (id: string) => {
    const local = get().rawClarifications.find(c => c.trackingNumber === id)
    if (local) {
      set({ selectedClarification: local })
      return
    }
    set({ isLoading: true, error: null })
    try {
      const clarification = await getClarificationById(id)
      set({ selectedClarification: clarification })
    } catch (error) {
      set({ error: (error as Error).message })
    } finally {
      set({ isLoading: false })
    }
  },

  selectClarification: (id: string) => {
    const clarification = get().rawClarifications.find(c => c.trackingNumber === id) || null
    set({ selectedClarification: clarification })
    return clarification
  },

  updateClarificationStatus: async (trackingNumber: string, newStatus: ClarificationStatus) => {
    const previousClarifications = get().rawClarifications
    const previousSelected = get().selectedClarification

    // 🟢 Actualización optimista inmediata
    const updatedList = previousClarifications.map(c =>
      c.trackingNumber === trackingNumber ? { ...c, status: newStatus } : c
    )

    const updatedSelected =
      previousSelected?.trackingNumber === trackingNumber
        ? { ...previousSelected, status: newStatus }
        : previousSelected

    set({
      rawClarifications: updatedList,
      selectedClarification: updatedSelected,
    })

    get().applyFilters()

    try {
      await apiUpdateClarificationStatus(trackingNumber, newStatus)
    } catch (error) {
      // 🔴 Reversión en caso de error
      console.error('❌ Error al actualizar el estado de la aclaración:', error)
      set({
        rawClarifications: previousClarifications,
        selectedClarification: previousSelected,
        error: (error as Error).message,
      })
      throw error
    }
  },
  reset: () =>
    set({
      selectedClarification: null,
      error: null,
      currentPage: 1,
      search: '',
      rawClarifications: [],
      clarifications: [],
      totalPages: 0,
    }),
}))
