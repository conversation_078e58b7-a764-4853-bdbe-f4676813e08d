import Cookies from 'js-cookie'

const TOKEN_KEY = 'token'

// Funciones para manejar el token en localStorage y cookies
export const getToken = (): string | null => {
  if (typeof window === 'undefined') return null

  const localToken = localStorage.getItem(TOKEN_KEY)
  if (localToken) return localToken

  const cookieToken = Cookies.get(TOKEN_KEY)
  if (cookieToken) {
    // sincroniza para futuras lecturas rápidas
    localStorage.setItem(TOKEN_KEY, cookieToken)
    return cookieToken
  }

  return null
}

export const setToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token)
  Cookies.set(TOKEN_KEY, token, { path: '/' }) // 1 día
}

export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY)
  Cookies.remove(TOKEN_KEY)
}

export const setLastActivity = () => {
  sessionStorage.setItem('lastActivity', String(Date.now()))
}

export const getLastActivity = () => {
  return Number(sessionStorage.getItem('lastActivity') || '0')
}

export const removeLastActivity = () => {
  sessionStorage.removeItem('lastActivity')
}
