/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Bank,
  TransferToDockData,
  TransferWithDockData,
  TransferWithDockResponse,
  TransferFormData,
  TransferDockResponse,
  TransferOrderResponse,
  BulkTransferResponse,
  BulkTransferError,
  BulkTransferData,
} from '@/types/transfers/types'
import * as transferApi from '@/api/endpoints/transfers'
import { create } from 'zustand'
import { isAxiosError } from 'axios'

interface TransferState {
  status: string
  loading: boolean
  error: string | null
  transferResponse: TransferDockResponse | null
  banks: Bank[] | null
  transferOrderDataById: TransferOrderResponse | null
  transferFormData: TransferFormData | null

  setTransferFormData: (data: TransferFormData) => void
  transferWithDock: (data: TransferWithDockData) => Promise<TransferDockResponse | undefined>
  getBanks: () => Promise<void>
  fetchTransferOrderById: (id: string) => Promise<TransferOrderResponse | undefined>

  // Massive transfer state
  bulkFileId: string | null
  bulkResult: BulkTransferResponse | null
  bulkTransferErrors: BulkTransferError[] | null

  uploadBulkFile: (file: File) => Promise<string>
  createBulkTransfer: (
    fileId: string,
    data: BulkTransferData[]
  ) => Promise<BulkTransferResponse & { httpStatus: number }>
  checkBulkTransferStatus: (bulkId: string) => Promise<BulkTransferResponse>

  fetchBulkTransferErrors: (bulkId: string) => Promise<void>
  resetBulkState: () => void
}

export const useTransfersStore = create<TransferState>()(set => ({
  status: '',
  loading: false,
  error: null,
  transferResponse: null,
  banks: null,
  transferFormData: null,
  transferOrderDataById: null,
  bulkFileId: null,
  bulkResult: null,
  bulkTransferErrors: null,

  setTransferFormData: (data: TransferFormData) => {
    set({ transferFormData: data })
  },

  transferWithDock: async (
    data: TransferWithDockData
  ): Promise<TransferDockResponse | undefined> => {
    set({ loading: true, error: null })

    try {
      const response = await transferApi.transfer(data)
      if (response.statusCode !== 201 && response.statusCode !== 102) {
        const backendMessage = response.message ?? 'Error al realizar la transferencia'
        throw new Error(backendMessage)
      }

      set({ transferResponse: response.data })
      return response.data as TransferDockResponse
    } catch (err: unknown) {
      let errorMessage = 'Error al realizar la transferencia'
      if (isAxiosError(err)) {
        const axiosMsg = err.response?.data?.message as string | undefined
        errorMessage = axiosMsg ?? errorMessage
      }
      set({ error: errorMessage })
      throw new Error(errorMessage)
    } finally {
      set({ loading: false })
    }
  },

  getBanks: async () => {
    set({ loading: true, error: null })
    try {
      const response = await transferApi.getBanks()
      set({ banks: response.banks })
    } catch {
      set({ error: 'Error al obtener los bancos' })
    } finally {
      set({ loading: false })
    }
  },

  fetchTransferOrderById: async (id: string): Promise<TransferOrderResponse | undefined> => {
    set({ loading: true, error: null })
    try {
      const response = await transferApi.getTransferOrderById(id)
      set({ transferOrderDataById: response.data as unknown as TransferOrderResponse })
      return response.data as unknown as TransferOrderResponse
    } catch (err: unknown) {
      let errorMessage = 'Error al obtener la orden de transferencia'
      if (isAxiosError(err)) {
        const axiosMsg = err.response?.data?.message as string | undefined
        errorMessage = axiosMsg ?? errorMessage
      }
      set({ error: errorMessage })
      throw new Error(errorMessage)
    } finally {
      set({ loading: false })
    }
  },

  // Massive Transfer
  uploadBulkFile: async (file: File): Promise<string> => {
    set({ loading: true, error: null })
    try {
      const { fileId } = await transferApi.uploadFile(file)
      set({ bulkFileId: fileId })
      return fileId
    } catch (err: unknown) {
      let errorMessage = 'Error al subir el archivo'
      if (isAxiosError(err)) {
        errorMessage = err.response?.data?.message || errorMessage
      }
      set({ error: errorMessage })
      throw new Error(errorMessage)
    } finally {
      set({ loading: false })
    }
  },

  createBulkTransfer: async (fileId: string, data: BulkTransferData[]) => {
    set({ loading: true, error: null })
    try {
      const result = await transferApi.createBulkTransfer(fileId, data)
      set({ bulkResult: result })
      return result
    } catch (err: unknown) {
      let errorMessage = 'Error al crear la transferencia masiva'
      if (isAxiosError(err)) {
        errorMessage = err.response?.data?.message || errorMessage
      }
      set({ error: errorMessage })
      throw new Error(errorMessage)
    } finally {
      set({ loading: false })
    }
  },

  checkBulkTransferStatus: async (bulkId: string) => {
    set({ loading: true, error: null })
    try {
      const result = await transferApi.getBulkTransferById(bulkId)
      return result
    } catch (err: unknown) {
      let errorMessage = 'Error al consultar el estado de la transferencia'
      if (isAxiosError(err)) {
        errorMessage = err.response?.data?.message || errorMessage
      }
      set({ error: errorMessage })
      throw new Error(errorMessage)
    } finally {
      set({ loading: false })
    }
  },

  fetchBulkTransferErrors: async (bulkId: string) => {
    set({ loading: true, error: null })
    try {
      const errors = await transferApi.getBulkTransferErrors(bulkId)
      set({ bulkTransferErrors: errors })
    } catch (err: unknown) {
      let errorMessage = 'Error al obtener errores de la transferencia masiva'
      if (isAxiosError(err)) {
        errorMessage = err.response?.data?.message || errorMessage
      }
      set({ error: errorMessage })
      throw new Error(errorMessage)
    } finally {
      set({ loading: false })
    }
  },

  resetBulkState: () => {
    set({
      bulkFileId: null,
      bulkResult: null,
      bulkTransferErrors: null,
      error: null,
    })
  },
}))
