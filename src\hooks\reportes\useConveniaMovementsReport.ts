'use client'
import { useState } from 'react'
import { ConveniaMovement } from '@/types/reports/convenia-movements'
import { getConveniaMovementsReport } from '@/api/endpoints/reports'
import { useDownload } from '../useDownload'
import { ConveniaMovementsReportParams } from '@/types/reports/types'

export const useConveniaMovementsReport = () => {
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<ConveniaMovement[]>([])
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: (err) => setError(err.message),
    loadingMessage: 'Preparando descarga del reporte de movimientos CONVENIA...'
  })

  const fetchReport = async (params: ConveniaMovementsReportParams) => {
    try {
      const response = await getConveniaMovementsReport(params)
      setData(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al obtener el reporte')
    }
  }

  const downloadReport = async (params: ConveniaMovementsReportParams) => {
    const filename = `reporte-movimientos-convenia-${new Date().toISOString().split('T')[0]}.xlsx`
    await downloadExcel(
      () => getConveniaMovementsReport(params),
      filename
    )
  }

  return {
    isLoading,
    loadingMessage,
    error,
    data,
    fetchReport,
    downloadReport
  }
}
