export enum CardStatus {
  NORMAL = 'NORMAL',
  BLOCKED = 'BLOCKED',
  CANCELED = 'CANCELED',
}

export interface ReassignCardDto {
  email: string
  oldCardId: string
  pan: string
  reason: string
}

export interface DataPhysical {
  physical_card_dock_id: string
  physical_card_id: string
  physical_status: string
}

export interface DataVirtual {
  virtual_card_dock_id: string
  virtual_card_id: string
  virtual_status: string
}

export interface DataAssignmentCardDto {
  person_dock_id: string
  account_dock_id: string
  card_physical?: DataPhysical
  card_virtual?: DataVirtual
}

export interface ReassignCardResponse {
  code: number
  message: string
  error: string
  data?: DataAssignmentCardDto
}

export interface ParamsChangeCardStatus {
  card_dock_id: string
  card_status: CardStatus
}

export interface ChangeCardStatusResponse {
  statusCode: number
  message: string
  error: string
  card_status?: string
}