import React from 'react'
import styles from '../../../components/styles/Transaction.module.css'
import InfoModalContentBase from '@/app/components/modals/InfoModal/InfoModalContentBase'
import { useRouter } from 'next/navigation'
// import ButtonOutlined from '../../ButtonOutlined'

const ScreenSuccessMassiveTransfer: React.FC = () => {
  const router = useRouter()
  return (
    <div>
      <h1 className={styles.title}>Transferencias</h1>
      <div className={styles.successEditedtContactWrapper}>
        <InfoModalContentBase
          title="¡La transferencia de saldo masiva se realizo con éxito!"
          message="Hemos recibido y almacenado los datos correctamente. "
          onPressPrimaryBtn={() => router.push('/transferencias')}
          onClose={() => null}
        />
        <div
          style={{
            marginTop: '24px',
            width: '100%',
            maxWidth: '360px',
          }}
        >
          {/* <ButtonOutlined text="Descargar comprobante" fullWidth onClick={() => null} /> */}
        </div>
      </div>
    </div>
  )
}

export default ScreenSuccessMassiveTransfer
