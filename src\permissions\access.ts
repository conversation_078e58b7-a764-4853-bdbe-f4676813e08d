import { hasAccess, RoleName, ROLES } from '@/constants/roles'

export const canAccessNewTransfer = (roleName: RoleName | null): boolean => {
  if (!roleName) return false

  const allowed: RoleName[] = [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
    ROLES.CLIENTE_TARJETAHABIENTES,
  ]

  return allowed.includes(roleName)
}

export const canSubmitTransfer = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

/* Ver transferencias realizadas */
export const canViewTransfers = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
    ROLES.CLIENTE_TARJETAHABIENTES,
  ])

/* Descargar comprobantes */
export const canDownloadReceipts = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

/* Transferencia a un contacto */
export const canTransferToContact = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

/* Transferencias masivas */
export const canMassTransfer = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

export const canCreateUser = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.CLIENTE_EMPRESA_ADMIN])

export const showActionsInClientTable = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA])

// Envio adminId para cuentas de rol Cliente
export const shouldSendAdminId = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_EMPRESA_TESORERO,
    ROLES.CLIENTE_TARJETAHABIENTES,
    ROLES.CLIENTE_LECTOR,
  ])

// ***************** REPORTES ¿Who can access reports? *************************/

// 1 Reporte de Transacciones Diarias/Mensuales
export const canAccessDailyMonthlyTransactionsReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_TARJETAHABIENTES,
    ROLES.ADMIN_CONVENIA_CONSULTOR,
  ])

// 2 Reporte de Transacciones por Tarjeta
export const canAccessCardTransactionsReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.ADMIN_CONVENIA_CONSULTOR,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_LECTOR,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

// 3 Reporte de Saldos por Cuenta
export const canAccessAccountBalancesReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.ADMIN_CONVENIA_CONSULTOR,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_TARJETAHABIENTES,
    ROLES.CLIENTE_LECTOR,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

// 4 Reporte de Estado de Tarjetas
export const canAccessCardStatusReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.ADMIN_CONVENIA_CONSULTOR,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_LECTOR,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

// 5 Reporte de Nuevas Tarjetas Emitidas
export const canAccessNewCardsIssuedReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.ADMIN_CONVENIA_CONSULTOR,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_LECTOR,
    ROLES.CLIENTE_EMPRESA_TESORERO,
  ])

// 6 Reporte de Lotes de Embossing
export const canAccessEmbossingBatchesReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR])

// 7 Reporte de Costos y Comisiones
export const canAccessCostsCommissionsReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR])

// 8 Reporte de Creación y Desactivación de Alias Empresas
export const canAccessCompanyAliasReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR])

// Reporte de liquidaciones
export const canAccessSettlementsReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR])

// Reporte de Movimientos CONVENIA
export const canAccessConveniaMovementsReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [
    ROLES.ADMIN_CONVENIA,
    ROLES.CLIENTE_EMPRESA_ADMIN,
    ROLES.CLIENTE_TARJETAHABIENTES,
    ROLES.ADMIN_CONVENIA_CONSULTOR,
  ])

// Reporte de Cierres contables
export const canAccessPeriodClosingsReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR])

// Reporte de uso de servicios
export const canAccessServiceUsageReport = (roleName: RoleName | null): boolean =>
  hasAccess(roleName, [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR])
