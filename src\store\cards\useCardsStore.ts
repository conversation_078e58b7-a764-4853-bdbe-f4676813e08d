/* eslint-disable @typescript-eslint/no-unused-vars */
import { create } from 'zustand'

import {
  ReassignCardDto,
  ChangeCardStatusResponse,
  ReassignCardResponse,
  ParamsChangeCardStatus,
} from '@/types/cards/types'
import { GetCardResponse } from '@/types/account/types'
import { getCardsById } from '@/api/endpoints/account'
import { deleteCard, reassignCard } from '@/api/endpoints/cards'

interface CardSelection {
  card?: { id: string; status: string; last4: string }
  deleteId: string
  reassignId: string
  reassignPan: string
  reason: string
  error: string
}

type CardsModals = {
  lockCard: boolean
  deleteCard: boolean
  reassignCard: boolean
  successReassign: boolean
  successDownload: boolean
}

export interface CardsStoreState {
  loading: boolean
  error: string | null
  cards: GetCardResponse[]
  eliminatedCardResponse?: ChangeCardStatusResponse | null
  reassignResponse: ReassignCardResponse | null
  cardSelection: CardSelection
  modals: CardsModals

  fetchCardsByAccount: (accountId: string) => Promise<void>
  setCards: (cards: GetCardResponse[]) => void
  updateCardStatusLocally: (cardId: string, newStatus: string) => void
  reassignCard: (data: ReassignCardDto) => Promise<ReassignCardResponse>
  deleteCard: (data: ParamsChangeCardStatus) => Promise<void>
  setCardSelection: (selection: Partial<CardSelection>) => void
  resetCardSelection: () => void
  setModal: (key: keyof CardsStoreState['modals'], value: boolean) => void
}

export const useCardsStore = create<CardsStoreState>()((set, get) => ({
  loading: false,
  error: null,
  cards: [],
  eliminatedCardResponse: null,
  reassignResponse: null,

  cardSelection: {
    card: undefined,
    deleteId: '',
    reassignId: '',
    reassignPan: '',
    reason: '',
    error: '',
  },

  modals: {
    lockCard: false,
    deleteCard: false,
    reassignCard: false,
    successReassign: false,
    successDownload: false,
  },

  fetchCardsByAccount: async (accountId: string) => {
    set({ loading: true, error: null, cards: [] })
    try {
      const response = await getCardsById(accountId)
      set({ cards: response })
    } catch (err) {
      console.error(err)
      set({ error: 'Error al obtener las tarjetas' })
    } finally {
      set({ loading: false })
    }
  },

  setCards: cards => set({ cards: cards }),

  updateCardStatusLocally: (cardId, newStatus) => {
    const updated = get().cards.map(c =>
      c.card.id === cardId ? { ...c, card: { ...c.card, status: newStatus } } : c
    ) as GetCardResponse[]
    set({ cards: updated })
  },

  reassignCard: async data => {
    set({ loading: true, error: null })
    try {
      const response = await reassignCard(data)
      set({ reassignResponse: response })
      return response
    } catch (err: unknown) {
      const error = err as ApiError
      const message = error?.response?.data?.message || 'Error al reasignar la tarjeta'
      set({ error: message })
      throw err
    } finally {
      set({ loading: false })
    }
  },

  deleteCard: async data => {
    set({ loading: true, error: null })
    try {
      const response = await deleteCard(data)
      set({ eliminatedCardResponse: response })
    } catch (err: unknown) {
      const error = err as ApiError
      const message = error?.response?.data?.message || 'Error al cambiar el estado de la tarjeta'
      set({ error: message })
    } finally {
      set({ loading: false })
    }
  },

  setCardSelection: partial =>
    set(state => ({ cardSelection: { ...state.cardSelection, ...partial } })),

  resetCardSelection: () =>
    set({
      cardSelection: {
        card: undefined,
        deleteId: '',
        reassignId: '',
        reassignPan: '',
        reason: '',
        error: '',
      },
    }),

  setModal: (key, value) => set(state => ({ modals: { ...state.modals, [key]: value } })),
}))
