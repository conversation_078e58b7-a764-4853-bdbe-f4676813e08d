export type Contact = {
  id: string
  name: string
  num_clabe: string
  bank_institution: string
  rfc?: string
  email: string
  userId: string
  alias?: string
  company_name?: string
}

export type ContactState = {
  contacts: Contact[]
  selectedContact: Contact | null
  loading: boolean
  error: string | null
}

export type ContactActions = {
  addContact: (data: Omit<Contact, 'id'>) => Promise<void>
  loadContacts: (userId: string, email: string) => Promise<void>
  setSelectedContact: (contact: Contact | null) => void
  editContact: (id: string, data: Partial<Contact>) => Promise<void>
  removeContact: (id: string) => Promise<void>
}

export type ContactFormData = {
  name: string
  num_clabe: string
  bank_institution: string
  email?: string
  userId: string
  rfc?: string
}
