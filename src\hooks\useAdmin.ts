import { useAdminStore } from '@/store/admin/useAdminStore'
import { AdminData } from '@/types/admin/types'

export const useAdmin = () => {
  const {
    admins,
    adminSelected,
    basicAdmins,
    amount,
    loading,
    error,
    fetchAdmins,
    fetchAdminById,
    createAdmin,
    updateAdmin,
    deleteAdmin,
    fetchBasicAdmins,
    fetchAmount,
    clearSelected,
  } = useAdminStore()

  const loadAdmins = async (groupId: number) => {
    await fetchAdmins({
      page: 1,
      limit: 10,
      groupId,
      orderBy: 'createdAt',
    })
  }

  const loadAdminDetails = async (id: string) => {
    await fetchAdminById(id)
  }

  const submitNewAdmin = async (data: AdminData) => {
    await createAdmin(data)
  }

  const updateExistingAdmin = async (id: string, data: Partial<AdminData>) => {
    await updateAdmin(id, data)
  }

  const removeAdmin = async (id: string) => {
    await deleteAdmin(id)
  }

  return {
    admins,
    adminSelected,
    basicAdmins,
    amount,
    loading,
    error,
    loadAdmins,
    loadAdminDetails,
    submitNewAdmin,
    updateExistingAdmin,
    removeAdmin,
    fetchBasicAdmins,
    fetchAmount,
    clearSelected,
  }
}
