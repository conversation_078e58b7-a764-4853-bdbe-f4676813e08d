'use client'
import { useState } from 'react'
import { MdOutlineEdit } from 'react-icons/md'
import { LuTrash2, <PERSON><PERSON>he<PERSON>ron<PERSON><PERSON>, LuChevronDown } from 'react-icons/lu'
import styles from '../../styles/AccountsTable.module.css'
import { AccountsTableProps } from '@/types/types'
import ToggleBlock from './ToggleBlock'
import ActivateButton from './ActivateButton'
import Image from 'next/image'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { RoleName, ROLES } from '@/constants/roles'
import { AccountResponse } from '@/types/account/types'
import Input from '../../Input'

type TableColumn = {
  key: string
  label: string
  allowedRoles?: RoleName[]
  sortable?: boolean
  render: (account: AccountResponse) => React.ReactNode
}

const AccountsTable: React.FC<AccountsTableProps> = ({
  accounts,
  onViewAccount,
  onActiveAccount,
  onEditAccount,
  onDeleteAccount,
  onOpenSpeiModal,
}) => {
  const { getUserRoleName } = useAuthStore()
  const [sortConfig, setSortConfig] = useState<{column: string; order: 'asc' | 'desc'} | null>(null)
  const [filters, setFilters] = useState({
    clabe: '',
    alias: ''
  })

  const handleSort = (columnKey: string) => {
    if (!sortConfig || sortConfig.column !== columnKey) {
      setSortConfig({ column: columnKey, order: 'asc' })
    } else if (sortConfig.order === 'asc') {
      setSortConfig({ column: columnKey, order: 'desc' })
    } else {
      setSortConfig(null)
    }
  }

  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // const clearFilters = () => {
  //   setFilters({
  //     clabe: '',
  //     alias: ''
  //   })
  // }

  const filteredAccounts = [...(accounts || [])].filter(account => {
    const matchesClabe = !filters.clabe || 
      (account.clabe && account.clabe.toLowerCase().includes(filters.clabe.toLowerCase()))
    
    const matchesAlias = !filters.alias || 
      (account.adminAlias && account.adminAlias.toLowerCase().includes(filters.alias.toLowerCase()))
    
    return matchesClabe && matchesAlias
  })

  const sortedAccounts = filteredAccounts.sort((a, b) => {
    if (!sortConfig) return 0
    
    let aValue, bValue
    
    switch (sortConfig.column) {
      case 'user':
        aValue = (a.name || '').toLowerCase()
        bValue = (b.name || '').toLowerCase()
        if (sortConfig.order === 'asc') {
          return aValue.localeCompare(bValue, 'es', { sensitivity: 'base' })
        } else {
          return bValue.localeCompare(aValue, 'es', { sensitivity: 'base' })
        }
      case 'saldo':
        aValue = a.amount || 0
        bValue = b.amount || 0
        if (sortConfig.order === 'asc') {
          return aValue - bValue
        } else {
          return bValue - aValue
        }
      default:
        return 0
    }
  })

  const columns: TableColumn[] = [
    {
      key: 'user',
      label: 'Usuario',
      sortable: true,
      render: (account: AccountResponse) => (
        <div className={styles.accountCell}>
          <Image
            src="/logo-accounts-convenia.svg"
            alt="Convenia logo"
            width={40}
            height={40}
            className={styles.accountAvatar}
          />
          <div className={styles.accountInfo}>
            <strong>{account?.name || 'Sin nombre'}</strong>
            {account?.email && <span>{account.email}</span>}
          </div>
        </div>
      ),
    },
    {
      key: 'clabe',
      label: 'Numero de cuenta',
      render: (account: AccountResponse) => account.clabe,
    },
    {
      key: 'alias',
      label: 'Alias empresa',
      render: (account: AccountResponse) => account.adminAlias,
    },
    {
      key: 'saldo',
      label: 'Saldo de cuenta',
      sortable: true,
      render: (account: AccountResponse) =>
        typeof account.amount === 'number'
          ? `$${account.amount.toLocaleString('es-MX', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}`
          : 'Sin saldo',
    },
    {
      key: 'speiIn',
      label: 'SPEI (IN)',
      allowedRoles: [ROLES.ADMIN_CONVENIA],
      render: (account: AccountResponse) => (
        <ToggleBlock
          value={account.speiIn ?? false}
          onToggle={() =>
            onOpenSpeiModal?.(
              account.clabe,
              { speiIn: !(account.speiIn ?? false), speiOut: account.speiOut ?? false },
              'in',
              account.id
            )
          }
        />
      ),
    },
    {
      key: 'speiOut',
      label: 'SPEI (OUT)',
      allowedRoles: [ROLES.ADMIN_CONVENIA],
      render: (account: AccountResponse) => (
        <ToggleBlock
          value={account.speiOut ?? false}
          onToggle={() =>
            onOpenSpeiModal?.(
              account.clabe,
              { speiIn: account.speiIn ?? false, speiOut: !(account.speiOut ?? false) },
              'out',
              account.id
            )
          }
        />
      ),
    },
    {
      key: 'actions',
      label: 'Acciones',
      allowedRoles: [ROLES.ADMIN_CONVENIA, ROLES.CLIENTE_EMPRESA_ADMIN, ROLES.CLIENTE_TARJETAHABIENTES, ROLES.CLIENTE_EMPRESA_TESORERO],
      render: (account: AccountResponse) => (
        <div className={styles.actions}>
          {!account.enabled && (
            <ActivateButton status={false} onRequestToggle={() => onActiveAccount?.(account)} />
          )}
          {getUserRoleName() !== ROLES.CLIENTE_EMPRESA_ADMIN && (
            <button className={styles.actionButton} onClick={() => onEditAccount(account)}>
              <MdOutlineEdit size={18} />
            </button>
          )}
          {account.isDeleteable &&
            <button className={styles.actionButton} onClick={() => onDeleteAccount(account)}>
              <LuTrash2 size={18} />
            </button>
          }
        </div>
      ),
    },
  ]

  const visibleColumns = columns.filter(
    column => !column.allowedRoles || column.allowedRoles.includes(getUserRoleName()!)
  )

  return (
    <div>
      <div style={{ marginBottom: '2rem', display: 'flex', gap: '12px', alignItems: 'center', flexWrap: 'wrap', justifyContent: 'flex-end' }}>
        <Input
          type="text"
          placeholder="Filtrar por número de cuenta..."
          value={filters.clabe}
          onChange={(e) => handleFilterChange('clabe', e.target.value)}
        />
        <Input
          type="text"
          placeholder="Filtrar por alias empresa..."
          value={filters.alias}
          onChange={(e) => handleFilterChange('alias', e.target.value)}
        />
      </div>
      <table className={styles.table}>
      <thead>
        <tr>
          {visibleColumns.map(column => (
            <th 
              key={column.key}
              onClick={column.sortable ? () => handleSort(column.key) : undefined}
              style={{ cursor: column.sortable ? 'pointer' : 'default' }}
            >
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                {column.label}
                {column.sortable && (
                  <>
                    {sortConfig?.column === column.key && sortConfig.order === 'asc' && <LuChevronUp size={16} strokeWidth={3} />}
                    {sortConfig?.column === column.key && sortConfig.order === 'desc' && <LuChevronDown size={16} strokeWidth={3} />}
                    {(!sortConfig || sortConfig.column !== column.key) && <LuChevronUp size={16} strokeWidth={3} style={{ opacity: 0.3 }} />}
                  </>
                )}
              </div>
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {sortedAccounts?.length > 0 ? (
          sortedAccounts.map((account, index) => (
            <tr key={index}>
              {visibleColumns.map(column => (
                <td
                  key={column.key}
                  onClick={column.key === 'user' ? () => onViewAccount?.(account) : undefined}
                >
                  {column.render(account)}
                </td>
              ))}
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className={styles.noResults}>
              No se encontraron resultados
            </td>
          </tr>
        )}
      </tbody>
    </table>
    </div>
  )
}

export default AccountsTable
