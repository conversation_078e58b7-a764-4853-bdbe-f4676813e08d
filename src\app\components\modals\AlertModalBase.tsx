import Modal from './Modal'
import Image from 'next/image'
import styles from '../styles/AlertModal.module.css'
import { AlertModalBaseProps } from '@/types/types'

const AlertModalBase: React.FC<AlertModalBaseProps> = ({
  type,
  title,
  message,
  open,
  children,
  onClose,
}) => {
  const getImageSrc = () => {
    switch (type) {
      case 'success':
        return '/alert-success.svg'
      case 'error':
        return '/alert-error.svg'
      case 'warning':
        return '/alert-warning.svg'
      default:
        return '/alert-warning.svg'
    }
  }
  return (
    <Modal open={open}>
      {onClose && (
        <Image
          src="/x-close.svg"
          alt="icon"
          width={24}
          height={24}
          className={styles.closeButton}
          onClick={onClose}
          draggable={false}
        />
      )}
      <div className={styles.image}>
        <Image
          src={getImageSrc()}
          alt={type}
          width={336}
          height={336}
          className={styles.bgIcon}
          draggable={false}
        />
      </div>
      <div className={styles.modal}>
        <div className={styles.content}>
          <h2 className={styles.title}>{title}</h2>
          {message && <p className={styles.description}>{message}</p>}
          {/* renderizar una descripcion si existe , aparte del mensaje */}
        </div>
      </div>
      <div style={{ position: 'relative', zIndex: 10 }}>{children}</div>
    </Modal>
  )
}

export default AlertModalBase
