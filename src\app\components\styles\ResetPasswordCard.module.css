.heading {
  margin: 0;
  position: relative;
  font-size: 32px;
}
.header,
.heading,
.text {
  align-self: stretch;
}
.text {
  position: relative;
  font-size: 16px;
  color: #4a4b55;
}
.header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.headerParent {
  gap: 24px;
}
.headerParent,
.navigation,
.navigationInner {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.navigation {
  gap: 24px;
  text-align: left;
  font-size: 32px;
  color: #232429;
  font-family: Inter;
}
.card {
  box-sizing: border-box;
}
.card {
  height: 380px;
  box-shadow: 0 2px 10px rgba(76, 78, 100, 0.22);
  border-radius: 10px;
  background-color: #fff;
  max-width: 480px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 24px 28px;
  gap: 24px;
  line-height: normal;
  letter-spacing: normal;
}

.inputGroup {
  text-align: left;
}

.inputGroup label {
  display: block;
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 16.94px;
  color: #000;
  background-color: white;
  margin-bottom: 6px;
}

.inputGroup input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #d0d5dd;
  border-radius: 10px;
  font-weight: 400;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  color: #000000;
  background-color: #fff;
}

.inputGroup input:focus {
  outline: none;
  border-color: #e9d688; /* Color dorado */
  box-shadow: 0 0 5px rgba(180, 145, 95, 0.5);
}

.invalidInput {
  border: 1px solid red;
  outline: none;
}

.errorText {
  color: red;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}
