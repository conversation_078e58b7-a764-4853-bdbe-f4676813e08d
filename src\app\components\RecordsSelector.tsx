'use client'
import React from 'react'
import styles from './styles/RecordsSelector.module.css'

interface RecordsSelectorProps {
  currentLimit: number
  onLimitChange: (limit: number) => void
  className?: string
}

const LIMIT_OPTIONS = [5, 10, 20, 50, 100]

const RecordsSelector: React.FC<RecordsSelectorProps> = ({
  currentLimit,
  onLimitChange,
  className = ''
}) => {
  return (
    <div className={`${styles.recordsSelector} ${className}`}>
      <select
        value={currentLimit}
        onChange={(e) => onLimitChange(Number(e.target.value))}
        className={styles.select}
      >
        {LIMIT_OPTIONS.map((limit) => (
          <option key={limit} value={limit}>
            Mostrar {limit} registros
          </option>
        ))}
      </select>
    </div>
  )
}

export default RecordsSelector