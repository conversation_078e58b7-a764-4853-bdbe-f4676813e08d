import React, { ChangeEvent, useState } from 'react'
import Image from 'next/image'
import styles from '../../styles/FileUpload.module.css'
import { Errors, FormData } from '@/hooks/useFormValidationClient'
import { uploadFile, deleteFile } from '@/api/endpoints/files'

type FileUploadProps = {
  fileInputs: { key: keyof FormData['files']; label: string; shortLabel?: string }[]
  files: FormData['files']
  setFiles: (e: ChangeEvent<HTMLInputElement>, fileType: keyof FormData['files']) => void
  errors: Errors
  onSuccess?: () => void
  setFormatFiles?: React.Dispatch<
    React.SetStateAction<{ key: keyof FormData['files']; file: string; type: string }[]>
  >
  adminId?: string
}

const FileUpload = ({
  fileInputs,
  files,
  setFiles,
  errors,
  onSuccess,
  setFormatFiles,
  adminId,
}: FileUploadProps) => {
  const [fileURLs, setFileURLs] = useState<{ [key: string]: string | null }>({})

  const fileTypeMap = {
    act: 'ARTICLES_OF_INCORPORATION',
    constancy: 'CONSTANCY',
    file: 'OTHER',
    other: 'OTHER',
  }

  const handleFileUpload = async (
    e: ChangeEvent<HTMLInputElement>,
    fileType: keyof FormData['files']
  ) => {
    const file = e.target.files?.[0] || null
    setFiles(e, fileType)

    if (file) {
      const fileURL = URL.createObjectURL(file)
      try {
        const { fileId } = await uploadFile(file)
        if (setFormatFiles) {
          setFormatFiles((prev: { key: keyof FormData['files']; file: string; type: string }[]) => {
            const filtered = prev.filter(f => f.key !== fileType)
            return [
              ...filtered,
              {
                key: fileType, // <--- clave única
                file: fileId,
                type: fileTypeMap[fileType],
              },
            ]
          })
        }
      } catch (error) {
        console.error('Error uploading file:', error)
      }
      setFileURLs(prev => ({ ...prev, [fileType]: fileURL }))
    }

    setTimeout(() => {
      const noError = !errors.files?.[fileType]
      if (noError && onSuccess) {
        onSuccess()
      }
    }, 100)
  }

  // const handleFileClick = (fileType: keyof FormData['files']) => {
  //   if (fileURLs[fileType]) {
  //     window.open(fileURLs[fileType] as string, '_blank')
  //   }
  // }

  const handleRemoveFile = async (fileType: keyof FormData['files']) => {
    const file = files[fileType]
    // Si es un archivo del backend (tiene id), llama al endpoint
    if (file && 'id' in file && file.id) {
      try {
        if (adminId) {
          await deleteFile(adminId, file.id)
        } else {
          console.error('adminId is undefined. Cannot delete file.')
        }
      } catch (error) {
        console.error('Error deleting file:', error)
        // Puedes mostrar un mensaje de error si quieres
      }
    }
    // Elimina el archivo del estado local (esto ya lo tienes)
    setFiles({ target: { files: [null] } } as unknown as ChangeEvent<HTMLInputElement>, fileType)
    setFileURLs(prev => ({ ...prev, [fileType]: null }))
    if (setFormatFiles) {
      setFormatFiles((prev: { key: keyof FormData['files']; file: string; type: string }[]) =>
        prev.filter(f => f.key !== fileType)
      )
    }
  }

  return (
    <div className={styles.fileInputs}>
      {fileInputs.map(({ key, label, shortLabel }) => {
        const file = files?.[key] || null
        const isUploaded = file !== null
        const fileUrl = file && 'url' in file ? file.url : fileURLs[key] || null

        return (
          <div key={key} className={styles.inputContainer}>
            {/* Si hay un archivo, el label se comporta como botón para abrirlo */}
            <label
              className={isUploaded ? styles.uploadedFile : styles.fileLabel}
              onClick={() => isUploaded && fileUrl && window.open(String(fileUrl), '_blank')}
            >
              {isUploaded ? (
                <div className={styles.uploadedContainer}>
                  <Image src="/images.svg" alt="Icono" width={60} height={60} />
                  <p title={shortLabel}>
                    {shortLabel} {file && 'name' in file ? `${file.name}` : ''}
                  </p>
                </div>
              ) : (
                <>
                  <Image src="/images.svg" alt="Icono" width={24} height={24} />
                  <p>{label}</p>
                </>
              )}
              {/* Deshabilitar el input si ya hay un archivo cargado */}
              {!isUploaded && (
                <input
                  type="file"
                  accept=".pdf, .jpg, .jpeg, .png"
                  onChange={e => handleFileUpload(e, key as keyof FormData['files'])}
                />
              )}
            </label>

            {isUploaded && (
              <button className={styles.deleteButton} onClick={() => handleRemoveFile(key)}>
                Eliminar archivo
              </button>
            )}

            {errors.files?.[key] && <span className={styles.error}>{errors.files[key]}</span>}
          </div>
        )
      })}
    </div>
  )
}

export default FileUpload
