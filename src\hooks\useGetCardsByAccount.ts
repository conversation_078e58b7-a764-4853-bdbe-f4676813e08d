import { useState, useEffect, useCallback } from 'react'
import { getCardsById } from '@/api/endpoints/account'
import { GetCardResponse } from '@/types/account/types' // Asegúrate de importar el tipo correcto

export const useGetCardsByAccount = (accountId: string | null) => {
  const [cardsData, setCards] = useState<GetCardResponse[]>([]) // Cambia el tipo del estado
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCards = useCallback(async () => {
    if (!accountId) {
      setError('No se proporcionó un ID de cuenta')
      return
    }

    setLoading(true)
    try {
      const response = await getCardsById(accountId) // Llama a la API
      setCards(response) // Almacena la respuesta completa en el estado
    } catch (err) {
      console.error('Error al obtener las tarjetas:', err)
      setError('Error al obtener las tarjetas')
    } finally {
      setLoading(false)
    }
  }, [accountId])

  useEffect(() => {
    fetchCards()
  }, [fetchCards])

  return { cardsData, setCards, loading, error, refetch: fetchCards }
}
