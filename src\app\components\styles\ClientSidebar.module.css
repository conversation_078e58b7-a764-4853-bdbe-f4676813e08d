.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  height: 100vh; /* Asegura que ocupe toda la pantalla */
  overflow: hidden; /* Evita que el contenedor general tenga scroll */
  background-color: #f4f5f7;
}

.main {
  flex: 1;
  padding: 2rem 2rem 3rem;
  display: flex;
  flex-direction: column;
  color: #000;
  gap: 2rem; /* Espaciado entre los bloques */
  background-color: #ffffff; /* Fondo blanco */
  border: 2px solid #c3c6d8; /* Borde gris claro */
  border-radius: 15px 15px 0px 0px; /* Bordes redondeados */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Sombra ligera */
  margin: 2rem; /* Espaciado del contenedor */
  overflow-y: scroll; /* Agrega scroll vertical */
}

.containerLogin {
  display: flex;
  min-height: 100vh;
}

.mainLogin {
  flex: 1;
}

@media screen and (min-width: 1150px) {
  .container {
    flex-direction: row;
  }
}
