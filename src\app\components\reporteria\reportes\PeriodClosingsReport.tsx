/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useState, useEffect } from 'react'
import { BsCalendarDate } from 'react-icons/bs'
import { usePeriodClosingsReport } from '@/hooks/reportes'
import ReportFilters from '@/app/components/ReportFilters'
import DateFilter from '@/app/components/DateFilter'

interface PeriodClosingsReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
}

// 📌 Fecha inicial predeterminada (la que tenías en el código anterior)
const DEFAULT_DATE = '2025-07-16'

const PeriodClosingsReport = ({ onLoadingChange }: PeriodClosingsReportProps) => {
  const { isLoading, loadingMessage, downloadReport } = usePeriodClosingsReport()

  // Estado de fecha
  const [selectedDate, setSelectedDate] = useState<string>(DEFAULT_DATE)
  const [activeFilters, setActiveFilters] = useState<string[]>([
    // mostramos la burbuja desde el inicio
    `Fecha: ${DEFAULT_DATE.split('-')[2]}/${DEFAULT_DATE.split('-')[1]}/${DEFAULT_DATE.split('-')[0]}`
  ])
  const [dateClearKey, setDateClearKey] = useState(0)

  /** Maneja la fecha que se selecciona */
  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      // Si limpian todo, quitamos la fecha seleccionada y burbuja
      setSelectedDate('')
      setActiveFilters([])
      return
    }

    const [date] = dates
    setSelectedDate(date)

    // Formateamos la burbuja con la fecha seleccionada
    const [year, month, day] = date.split('-')
    setActiveFilters([`Fecha: ${day}/${month}/${year}`])
  }

  /** Reset vuelve a la fecha inicial */
  const handleReset = () => {
    setSelectedDate(DEFAULT_DATE)
    const [year, month, day] = DEFAULT_DATE.split('-')
    setActiveFilters([`Fecha: ${day}/${month}/${year}`])
    setDateClearKey(k => k + 1) // fuerza limpiar el datepicker visualmente y recargar la fecha por defecto
  }

  /** Descarga reporte con la fecha actual (o la default si está vacía) */
  const handleDownload = async () => {
    if (!selectedDate) return
    const [year, month, day] = selectedDate.split('-')
    await downloadReport({
      year: parseInt(year),
      month,
      day,
    })
  }

  /** loading global */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onSubmit={(e) => e.preventDefault()}
      onReset={handleReset}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Cierres de Período"
      icon={<BsCalendarDate />}
      downloadOptions={{
        onlyExcel: true,
        onExcelDownload: handleDownload,
      }}
    >
      <DateFilter
        mode="single"
        onDateChange={handleDateChange}
        label="Selecciona la fecha"
        placeholder="Elegir fecha"
        clearTrigger={dateClearKey}
        defaultDate={selectedDate || DEFAULT_DATE}
      />
    </ReportFilters>
  )
}

export default PeriodClosingsReport
