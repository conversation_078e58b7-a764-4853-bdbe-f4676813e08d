import api from '../client'
import {
  Clarification,
  ClarificationProps,
  ClarificationResponse,
} from '@/types/clarification/types'

// GET ALL CLARIFICATIONS
export const getAllClarifications = async (
  params: ClarificationProps
): Promise<ClarificationResponse> => {
  const offset = (params.page - 1) * params.limit
  const response = await api.get('/clarification', {
    params: {
      limit: params.limit,
      offset,
    },
  })
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener las aclaraciones')
  }
  return {
    clarifications: response.data as Clarification[],
    totalRows: 8,
  }
}

export const getClarificationById = async (id: string): Promise<Clarification> => {
  const response = await api.get(`/clarification/${id}`)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener la aclaración')
  }
  return response.data
}

export const updateClarificationStatus = async (trackingNumber: string, status: string) => {
  const response = await api.patch(`/clarification/update/${trackingNumber}`, { status })

  if (response.status !== 200) {
    throw new Error('No se pudo actualizar el estado de la aclaración')
  }

  return response.data
}
