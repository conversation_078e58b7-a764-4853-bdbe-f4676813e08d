.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContainer {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 544px;
  height: auto;
  max-height: 100dvh;
  padding: 2% 0 24px;
  flex-direction: column;
  justify-content: flex-start; /* Cambiado de center a flex-start */
  align-items: center;
  gap: 24px;
  flex-shrink: 0;
  border-radius: 12px;
  background: #fff;
  box-shadow:
    0px 20px 24px -4px rgba(16, 24, 40, 0.08),
    0px 8px 8px -4px rgba(16, 24, 40, 0.03);
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.closeButton {
  position: absolute;
  top: 5px;
  right: 16px;
  padding-top: 2%;
  cursor: pointer;
  font-size: 20px;
  background: none;
  border: none;
  color: #98a2b3;
  z-index: 1;
}

.header {
  display: flex;
  padding: 0px 60px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
  flex-shrink: 0;
  align-self: stretch;
}

.title {
  align-self: stretch;
  color: var(--Primary, #000);
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
}

.description {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-self: stretch;
  color: #6f7280;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.description li {
  margin-left: 5%;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-self: stretch;
  padding: 0px 60px;
}

.downloadButton {
  display: flex;
  height: 48px;
  padding: 12px 24px;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex: 1 0 0;
  border-radius: 8px;
  border: 1px solid #000;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  cursor: pointer;
  background-color: #fff;

  color: #000;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.downloadButton:hover {
  background: #f5f5f5;
}

.uploadContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 80px;
  padding: 4px 42px;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px dashed #000;
  background: #fff;
  cursor: pointer;
}

.uploadedContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100px;
  padding: 4px 42px;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 4px;
  border: 1px dashed #000;
  background: #000;
  cursor: pointer;
}

.uploadIcon {
  display: flex;
  align-items: center;
  font-size: 24px;
  color: #8b6539;
}

.uploadedIcon {
  display: flex;
  align-items: center;
  font-size: 24px;
  color: #fff;
}

.uploadText {
  text-align: center;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 166%;
  letter-spacing: 0.4px;
  background: var(
    --Primary-1-Convenia,
    linear-gradient(
      90deg,
      #75532f 0.41%,
      #8b6539 23.03%,
      #9f7a49 39.45%,
      #af8b55 50.56%,
      #dcb992 74.08%,
      #dab890 86.93%,
      #d5b289 91.56%,
      #cca87c 94.86%,
      #bf9b6b 97.51%,
      #af8b56 99.77%,
      #af8b55 99.85%,
      #e9d6b8 100%
    )
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
}

.uploadContainer:hover {
  background: #f5f5f5;
  border: 1px dashed #8b6539;
}

.errorText {
  color: #ff0000;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.deleteButton {
  width: 100%;
  padding: 8px 10px;
  border: none;
  background: none;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: opacity 0.3s ease-in-out;
  text-align: center;

  /* 🎨 Aplica el degradado SOLO al texto */
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.deleteButton:hover {
  opacity: 0.8;
}
