.stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.stat {
	display: flex;
	flex-direction: column-reverse;
	align-items: center;
	justify-content: center;
	gap: 1rem;
  background-color: #000;
  color: #f4f5fb;
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  font-weight: 600;
  font-family: Inter;
  font-size: 17px;
  line-height: 26.63px;
  height: 100px;
}

.statContent {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  width: 100%;
}

.btnLink {
	display: flex;
	gap: 0.5rem;
}

.btnLink button {
	border: none;
	background: none;
	cursor: pointer;
	color: #b4915f;
}

.aloneStat {
	background-color: #000;
  color: #f4f5fb;
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  font-weight: 600;
  font-family: Inter;
  font-size: 17px;
  line-height: 26.63px;
  height: 100px;
  width: 350px;
}

.aloneStat span {
  display: block;
  margin-top: 20px;
}

.loaderWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
}

@media screen and (min-width: 700px) {
  .stat {
    flex-direction: row-reverse;
    justify-content: space-between;
    width: 100%;
  }

  .statContent {
    width: 50%;
  }
}

@media screen and (min-width: 900px) {
  .stats {
    flex-direction: row;
    justify-content: flex-end;
  }

  .stat {
    flex-direction: column-reverse;
    width: 100%;
  }

  .statContent {
    width: 100%;
  }
}
