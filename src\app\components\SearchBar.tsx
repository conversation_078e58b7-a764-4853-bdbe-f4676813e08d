/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect } from 'react'
import styles from './styles/SearchBar.module.css'
import { FiSearch } from 'react-icons/fi'
import { SearchBarProps } from '@/types/types'
import debounce from 'lodash.debounce'

const SearchBar: React.FC<SearchBarProps> = ({ placeholder = 'Buscar', onSearch, className }) => {
  // Crear una versión debounced de la función de búsqueda
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      onSearch(searchValue)
    }, 500),
    []
  )

  // Limpiar el debounce cuando el componente se desmonte
  useEffect(() => {
    return () => {
      debouncedSearch.cancel()
    }
  }, [debouncedSearch])

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(event.target.value)
  }

  return (
    <div className={`${styles.searchBar} ${className}`}>
      <FiSearch className={styles.icon} />
      <input
        type="text"
        placeholder={placeholder}
        onChange={handleInputChange}
        className={styles.input}
      />
    </div>
  )
}

export default SearchBar
