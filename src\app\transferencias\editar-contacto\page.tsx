/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import useFormValidationEditContactTransaction from '@/hooks/useFormValidationEditContactTransaction'
import EditContactForm from '../../components/transferencias/components/EditContactForm'
import ScreenSuccessEditedContact from '../../components/transferencias/components/ScreenSuccessEditedContact'
import { useContactStore } from '@/store/contact/useContactStore'
import { toast } from 'react-toastify'

const Page = () => {
  const router = useRouter()
  const [successEdited, setSuccessEdited] = useState(false)

  const { selectedContact, editContact } = useContactStore()

  const { formData, errors, touched, handleInputChange, handleBlur, setFormData, isFormValid } =
    useFormValidationEditContactTransaction()

  useEffect(() => {
    if (!selectedContact) {
      // Si el usuario entra directo sin seleccionar contacto, redirige
      router.replace('/transferencias')
      return
    }

    // Cargar datos del contacto en el formulario
    setFormData({
      cuentaDestino: selectedContact.num_clabe.toString(),
      nombreDelBeneficiario: selectedContact.name,
      rfcBeneficiario: selectedContact.rfc || '',
      correo: selectedContact.email || '',
      aliasName: selectedContact.alias || '',
    })
  }, [selectedContact, setFormData])

  const handleEditContact = async () => {
    if (!selectedContact) return

    if (isFormValid()) {
      try {
        await editContact(selectedContact.id, {
          name: formData.nombreDelBeneficiario,
          num_clabe: String(formData.cuentaDestino),
          bank_institution: selectedContact.bank_institution,
          rfc: formData.rfcBeneficiario,
          email: formData.correo,
          alias: formData.aliasName,
        })

        setSuccessEdited(true)
      } catch (err: any) {
        toast.error(err?.response?.data?.message || 'Ocurrió un error al editar el contacto', {
          position: 'top-right',
          autoClose: 3000,
          pauseOnHover: true,
          draggable: true,
        })
        console.error('Error al editar contacto', err)
      }
    } else {
      toast.warning('Revisa los campos del formulario', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    }
  }

  const handleBack = () => {
    setSuccessEdited(false)
    setFormData({
      cuentaDestino: '',
      nombreDelBeneficiario: '',
      rfcBeneficiario: '',
      correo: '',
      aliasName: '',
    })
    router.push('/transferencias')
  }

  return (
    <>
      {successEdited ? (
        <ScreenSuccessEditedContact onPressBtnBack={handleBack} />
      ) : (
        <EditContactForm
          formData={formData}
          errors={errors}
          touched={touched}
          isFormValid={isFormValid()}
          handleInputChange={handleInputChange}
          handleBlur={handleBlur}
          onPressBtnEditContact={handleEditContact}
          nameContact={selectedContact?.name || ''}
        />
      )}
    </>
  )
}

export default Page
