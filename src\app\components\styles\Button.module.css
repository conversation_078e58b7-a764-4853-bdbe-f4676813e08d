.button {
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  max-width: 327px;
  height: 48px;
  width: 100%;
  color: white;
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  transition:
    all 0.4s ease,
    background 0.4s ease;
}

.button:hover {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.button:active {
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Sombra más pequeña */
  transform: translateY(2px); /* Simula la presión */
}

.fullWidth {
  width: 100%;
  max-width: none; /* Elimina la restricción de max-width */
}

.button:disabled {
  background: #acafc2;
  color: #ffffff;
  cursor: not-allowed;
  border: none;
}
.button:disabled:hover {
  box-shadow: none;
  transform: none;
}

.buttonOutlined {
  background: #ffffff;
  border: 1px solid #000000;
  color: #000000;
  border-radius: 8px;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  margin: 0;
}
