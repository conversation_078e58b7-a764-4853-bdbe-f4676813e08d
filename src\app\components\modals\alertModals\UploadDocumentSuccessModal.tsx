import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

const UploadDocumentSuccessModal: React.FC<BaseModalProps> = ({ open, onClose }) => {
  return (
    <AlertModal
      open={open}
      type="success"
      title="La carga del documento ha sido exitosa"
      textBtn="Aceptar" // ✅ Esto activa que se muestre correctamente
      onClose={onClose}
      onPressBtn={onClose} // ✅ Esto asegura que se cierre
    />
  )
}

export default UploadDocumentSuccessModal
