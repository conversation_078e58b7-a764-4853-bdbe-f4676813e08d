import { HiOutlineClipboard, HiOutlineEye } from 'react-icons/hi2'
import { toast } from 'react-toastify'
import Loader from '../loader/Loader'
import styles from '../styles/StatsLink.module.css'

interface Stat {
  value: string | number
  label: string
  loading?: boolean
}

const StatsLink = ({ stats = [] }: { stats: Stat[] }) => {
  const secureStates: Record<number, boolean> = {};
  const isSingle = stats.length === 1

  const setClipboard = async (text : string) => {
		try {
			const type = 'text/plain';
			const blob = new Blob([text], { type });
			const data = [new ClipboardItem({ [type]: blob })];
			await navigator.clipboard.write(data);

			toast.success('Copiado con éxito', {
        position: 'top-right',
        autoClose: 3000,
      })
		} catch {
			toast.error('Hubo un error al copiar', {
        position: 'top-right',
        autoClose: 3000,
      })
		}
	};

  const setSecure = (value: string, index: number, elementId?: string) => {
    const secure = secureStates[index] ?? true; // Default: secured (true)

    // Apply masking if secure=true
    const displayedValue = secure
      ? (value.length <= 4 ? value : '*'.repeat(value.length - 4) + value.slice(-4))
      : value;

    // If an elementId is provided, update the DOM directly
    if (elementId) {
      const element = document.getElementById(elementId);
      if (element) element.textContent = displayedValue;
    }

    return displayedValue;
  };

  const toggleSecurity = (index: number, elementId: string) => {
    // Toggle secure state for this index
    secureStates[index] = !(secureStates[index] ?? true);

    // Force update the DOM
    const stat = stats[index];
    setSecure(String(stat.value), index, elementId);
  };

  return (
    <div className={styles.stats}>
      {stats.map((stat, index) => (
        <div
          key={index}
          className={isSingle ? styles.aloneStat : styles.stat}
        >
          <div className={styles.statContent}>
            {stat.loading ? (
              <div className={styles.loaderWrapper}>
                <Loader size={28} />
              </div>
            ) : (
              <p id={`stat-value-${index}`}>{setSecure(String(stat.value), index)}</p>
            )}
            
            <div className={styles.btnLink}>
              <button onClick={() => toggleSecurity(index, `stat-value-${index}`)}>
                <HiOutlineEye size={18}  />
              </button>
              <button onClick={() => setClipboard(String(stat.value))}>
                <HiOutlineClipboard size={18}/>
              </button>
            </div>

          </div>
            <span>{stat.label}</span>
        </div>
      ))}
    </div>
  )
}


export default StatsLink
