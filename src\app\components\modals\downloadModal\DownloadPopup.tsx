import React, { useEffect, useRef } from 'react'
import styles from '../../styles/DownloadPopup.module.css'
import Image from 'next/image'

interface Props {
  visible: boolean
  onClose: () => void
  position?: { top: number; left: number }
  onDownloadPDF?: () => void
  onDownloadExcel?: () => void
}

const DownloadPopup: React.FC<Props> = ({ visible, onClose, position, onDownloadPDF, onDownloadExcel }) => {
  const popupRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [visible, onClose])

  if (!visible || !position) return null

  return (
    <div
      className={styles.popupContainer}
      ref={popupRef}
      style={{
        top: position.top,
        left: position.left,
        position: 'absolute',
      }}
    >
      <button
        className={styles.fileOption}
        onClick={() => {
          onDownloadPDF?.()
          onClose()
        }}
      >
        <Image src="/icon-pdf.svg" alt="PDF Icon" width={40} height={40} />
        <span className={styles.fileLabel}>PDF</span>
      </button>

      <hr />

      <button
        className={styles.fileOption}
        onClick={() => {
          onDownloadExcel?.()
          onClose()
        }}
      >
        <Image src="/icon-xls.svg" alt="XLS Icon" width={40} height={40} />
        <span className={styles.fileLabel}>Excel</span>
      </button>
    </div>
  )
}

export default DownloadPopup
