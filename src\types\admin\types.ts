/* eslint-disable @typescript-eslint/no-explicit-any */
export interface AdminAddress {
  state: string
  city: string
  colonia: string
  street: string
  num_ext: string
  zip_code: string
}

export interface AdminUser {
  id: string
  name: string
  phone: number
  email: string
  clabe: string
  dockId: string
  password: string | null
  created_by: string
  enterprises: AdminEnterprise[]
}

export interface AdminEnterprise {
  name: string
  id: string
  roleId: string
  roleName: string
}

export interface file {
  file: string
  type: string
}

export interface AdminFileResponse extends Omit<file, 'file'> {
  id: number
  url: string
}

// Tipo para POST /admin
export interface AdminData {
  company_name: string
  alias: string
  rfc: string
  num_asigned_cards: number
  is_subaccount: boolean
  is_sucursal: boolean
  membership_number: number
  manager: string
  address: AdminAddress
  spei_in?: number
  spei_out?: number
  target_refound?: number
  ambassador?: string
  group_id: number
  enterprises: unknown
  user?: Partial<AdminUser>
  files: file[]
}

// Tipo para GET /admin (lista)
export interface AdminClient {
  id: string
  companyName: string
  alias: string
  rfc: string
  numAsignedCards: number
  amount: number
  groupId: number
  createdAt: string
  membershipNumber: number
  manager: AdminUser
}

interface user {
  created_at: string
  created_by: string
  email: string
  enabled: boolean
  id: string
  isSpeiInEnabled: boolean
  isSpeiOutEnabled: boolean
  name: string
  password: string
  phone: string
  rfc: string
}

interface RelUserRoleAdmins {
  id: string
  user: user
}

export interface AdminByUserIdClient {
  id: string
  company_name: string
  alias: string
  rfc: string
  numAsignedCards: number
  groupId: number
  createdAt: string
  membershipNumber: number
  amount: number
  manager: user
  relUserRoleAdmins: RelUserRoleAdmins[]
}

// Tipo para GET /admin/:id (detalle)
export interface AdminDetail {
  id: string
  companyName: string
  alias: string
  rfc: string
  createdAt: string
  amount: string
  numAsignedCards: number
  speiIn: number
  speiOut: number
  targetRefound: number
  ambassador: string
  groupId: number
  isSucursal?: boolean
  manager: AdminUser
  address: {
    id: string
    state: string
    city: string
    colonia: string
    street: string
    numExt: number
    zipCode: string
  }
  files?: AdminFileResponse[]
}

// Respuesta de creación
export interface AdminResponse extends AdminData {
  id: string
  createdAt: string
  user?: AdminUser
}

// Básico para combos
export interface AdminBasic {
  id: string
  companyName: string
  alias: string
  rfc: string
  membershipNumber: number
  numAsignedCards: number
  amount: number
  group_id: number
  createdAt: string
}

export interface AdminAmount {
  groupId?: number | null
  adminId?: string | null
}

export interface AdminListParams {
  page: number
  limit: number
  q?: string
  groupId?: number
  orderBy: string
}

export interface MassiveAdminResponse {
  successCount: number
  failCount: number
  successes: any[] // puedes tiparlo mejor si lo necesitas
  errors: {
    index: number
    error: {
      response?: {
        message?: string
        error?: string
        statusCode?: number
      }
      message?: string
      name?: string
    }
    dto: any
  }[]
}
