import React from 'react'
import styles from '../../../components/styles/Transaction.module.css'
import InfoModalContentBase from '@/app/components/modals/InfoModal/InfoModalContentBase'

type Props = {
  onPressBtnBack: () => void
}

const ScreenSuccessEditedContact: React.FC<Props> = ({ onPressBtnBack }) => {
  return (
    <div>
      <h1 className={styles.title}>Transferencias</h1>
      <div className={styles.successEditedtContactWrapper}>
        <InfoModalContentBase
          title="¡El contacto fue editado correctamente!"
          message="Hemos recibido y almacenado los datos correctamente."
          onPressPrimaryBtn={onPressBtnBack}
          onClose={() => null}
        />
      </div>
    </div>
  )
}

export default ScreenSuccessEditedContact
