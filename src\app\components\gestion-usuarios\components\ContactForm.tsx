/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react'
import styles from '../../styles/NewUserAdmin.module.css'
import { GoEye, GoEyeClosed } from 'react-icons/go'
import { Errors, FormData } from '@/hooks/useFormValidationUser'
import { handleTextOnlyInput } from '@/utils/inputRestrictions'
import { useRoleStore } from '@/store/role/useRoleStore'
import { useAuthStore } from '@/store/auth/useAuthStore'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'

type ContactFormProps = {
  formData: FormData
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
  errors: Errors
  isEditing?: boolean
  emailFound?: boolean
}

const ContactForm = ({ formData, onChange, errors, isEditing, emailFound }: ContactFormProps) => {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const { roles, fetchRoles } = useRoleStore()
  const { getRoleType, user } = useAuthStore()

  const isEditingOwnEmail = user?.email === formData.email
  const isProfileTypeDisabled = isEditing && isEditingOwnEmail

  useEffect(() => {
    fetchRoles(getRoleType()!)
  }, [])

  return (
    <div className={styles.formContainer}>
      <div className={styles.inputGroup}>
        <label>Correo</label>
        <input
          type="email"
          name="email"
          placeholder="Correo electrónico"
          value={formData.email}
          onChange={onChange}
          className={errors.email ? styles.error : ''}
          autoComplete="off"
        />
        {errors.email && <span className={styles.error}>{errors.email}</span>}
      </div>
      <div className={styles.inputGroup}>
        <label>Nombre</label>
        <input
          type="text"
          name="name"
          placeholder="Escribir nombre"
          value={formData.name}
          onChange={onChange}
          onKeyDown={handleTextOnlyInput}
          className={errors.name ? styles.error : ''}
          autoComplete="off"
          disabled={emailFound}
          style={{
            cursor: emailFound ? 'not-allowed' : 'auto',
            opacity: emailFound ? 0.5 : 1,
          }}
        />
        {errors.name && <span className={styles.error}>{errors.name}</span>}
      </div>
      <div className={styles.inputGroup}>
        <label>Tipo de perfil</label>
        <select
          name="profileType"
          value={formData.profileType}
          onChange={onChange}
          className={errors.profileType ? styles.error : ''}
          disabled={isProfileTypeDisabled}
        >
          <option value="">Selecciona un tipo de perfil</option>
          {roles.map(role => (
            <option key={role.id} value={role.id}>
              {role.name}
            </option>
          ))}
        </select>
        {errors.profileType && <span className={styles.error}>{errors.profileType}</span>}
      </div>
      <div className={styles.inputGroup}>
        <label>Teléfono</label>
        <PhoneInput
          country={'mx'}
          value={formData.phoneAdmin || ''}
          onChange={phone => {
            onChange({
              target: {
                name: 'phoneAdmin',
                value: phone,
              }
           } as React.ChangeEvent<HTMLInputElement>)
          }}
          enableSearch
          countryCodeEditable={false}
          // 1. deshabilita todo el input
          disabled={emailFound}
          // 2. oculta el dropdown de países
          disableDropdown={emailFound}
          inputProps={{
            name: 'phoneAdmin',
            autoComplete: 'tel',
            disabled: emailFound,
          }}
          containerStyle={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            opacity: emailFound ? 0.5 : 1,
          }}
          inputStyle={{
            width: '100%',
            padding: emailFound ? '10px 14px' : '10px 14px 10px 58px',
            borderRadius: '8px',
            border: '1px solid #d0d5dd',
            background: 'white',
            fontFamily: 'Inter, sans-serif',
            color: '#000',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          buttonStyle={{
            border: 'none',
            background: 'transparent',
            position: 'absolute',
            left: '10px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 2,
            display: emailFound ? 'none' : 'flex',
            alignItems: 'center',
            gap: '6px',
            fontSize: '14px',
            minWidth: emailFound ? '0' :'48px',
            color: '#000',
          }}
          inputClass={errors.phoneAdmin ? styles.error : ''}
        />
        { !emailFound && errors.phoneAdmin && <span className={styles.error}>{errors.phoneAdmin}</span>}
      </div>

      {!isEditing && !emailFound && (
        <>
          <div className={styles.inputGroup}>
            <label>Contraseña</label>
            <div className={styles.passwordWrapper}>
              <input
                tabIndex={0}
                type={showPassword ? 'text' : 'password'}
                name="adminPassword"
                placeholder="********"
                maxLength={12}
                value={formData.adminPassword}
                onChange={onChange}
                className={errors.adminPassword ? styles.error : ''}
                autoComplete="new-password"
              />
              <button
                type="button"
                tabIndex={-1}
                className={styles.eyeButton}
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <GoEye size={15} /> : <GoEyeClosed size={15} />}
              </button>
            </div>
            {errors.adminPassword && <span className={styles.error}>{errors.adminPassword}</span>}
          </div>
          <div className={styles.inputGroup}>
            <label>Confirmar contraseña</label>
            <div className={styles.passwordWrapper}>
              <input
                tabIndex={0}
                type={showConfirmPassword ? 'text' : 'password'}
                name="confirmPassword"
                placeholder="********"
                maxLength={12}
                value={formData.confirmPassword}
                onChange={onChange}
                className={errors.confirmPassword ? styles.error : ''}
              />
              <button
                type="button"
                tabIndex={-1}
                className={styles.eyeButton}
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <GoEye size={15} /> : <GoEyeClosed size={15} />}
              </button>
            </div>
            {errors.confirmPassword && (
              <span className={styles.error}>{errors.confirmPassword}</span>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default ContactForm
