import React from 'react'
import styles from '../../styles/NewClientAdmin.module.css'
import { Errors, FormData } from '@/hooks/useFormValidationClient'
import { handleNumberOnlyInput, handleTextOnlyInput, handleDecimalInput } from '@/utils/inputRestrictions'

type CommissionFormProps = {
  formData: FormData
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  errors: Errors
}

const CommissionForm = ({ formData, onChange, errors }: CommissionFormProps) => {
  return (
    <div className={styles.formContainer}>
      <div className={styles.inputGroup}>
        <label>Spei in comision (%)</label>
        <div className={styles.inputWrapper}>
          <input
            type="text"
            name="commissionPercentage"
            placeholder="Ej: 2"
            value={formData.commissionPercentage}
            onChange={onChange}
            onKeyDown={handleDecimalInput}
            // maxLength={3}
            className={`${styles.input} ${errors.commissionPercentage ? styles.error : ''}`}
          />
          <span className={styles.suffix}>%</span>
        </div>
        {errors.commissionPercentage && (
          <span className={styles.error}>{errors.commissionPercentage}</span>
        )}
      </div>

      <div className={styles.inputGroup}>
        <label>Spei out comision ($)</label>
        <div className={styles.inputWrapper}>
          <span className={styles.prefix}>$</span>
          <input
            type="text"
            name="commissionAmount"
            placeholder="Ej: 100"
            value={formData.commissionAmount}
            onChange={onChange}
            onKeyDown={handleNumberOnlyInput}
            className={`${styles.input} ${errors.commissionAmount ? styles.error : ''}`}
          />
        </div>
        {errors.commissionAmount && <span className={styles.error}>{errors.commissionAmount}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Fondeo de tarjetas (%)</label>
        <div className={styles.inputWrapper}>
          <input
            type="text"
            name="commissionCardFunding"
            placeholder="Ej: 3%"
            value={formData.commissionCardFunding}
            onChange={onChange}
            onKeyDown={handleDecimalInput}
            // maxLength={3}
            className={errors.commissionCardFunding ? styles.error : ''}
          />
          <span className={styles.suffix}>%</span>
        </div>
        {errors.commissionCardFunding && (
          <span className={styles.error}>{errors.commissionCardFunding}</span>
        )}
      </div>

      <div className={styles.inputGroup}>
        <label>Embajador</label>
        <input
          type="text"
          name="commissionAmbassador"
          placeholder="Juan Perez"
          value={formData.commissionAmbassador}
          onChange={onChange}
          onKeyDown={handleTextOnlyInput}
          className={errors.commissionAmbassador ? styles.error : ''}
        />
        {errors.commissionAmbassador && (
          <span className={styles.error}>{errors.commissionAmbassador}</span>
        )}
      </div>
    </div>
  )
}

export default CommissionForm
