'use client'
import React, { Suspense, useState } from 'react'
import Header from '@/app/components/Header'
import EditAliasClient from './EditAliasClient'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { FaArrowLeft } from 'react-icons/fa'
import styles from '../../../styles/Clientes.module.css'
import SaveEditedAliasDataModal from '@/app/components/modals/informationModals/SaveEditedAliasDataModal'
import Loader from '@/app/components/loader/Loader'

const HomePage = () => {
  return (
    <Suspense fallback={<Loader />}>
      <Content />
    </Suspense>
  )
}

const Content = () => {
  const { adminSelected: client } = useAdminStore()
  const [openModal, setOpenModal] = useState(false)

  if (!client) return <p>Cliente no encontrado.</p>

  const handleConfirm = () => window.history.back()
  const handleCloseModal = () => setOpenModal(false)

  return (
    <>
      <Header title={`Editar ${client.alias}`} />
      <button className={styles.backButton} onClick={() => window.history.back()}>
        <FaArrowLeft size={16} className={styles.icon} /> Atrás
      </button>
      <EditAliasClient client={client} setOpenModal={setOpenModal} />
      <SaveEditedAliasDataModal
        open={openModal}
        onConfirm={handleConfirm}
        onClose={handleCloseModal}
      />
    </>
  )
}

export default HomePage
