'use client'
import { Clarification } from '@/types/clarification/types'
import { useClarificationStore } from './store/useClarificationStore'
import ClarificationTable from './ClarificationTable'
import Pagination from '../Pagination'

type Props = {
  onOpenDeleteClientModal: (clarificationId: string) => void
}

const Clarifications: React.FC<Props> = ({ onOpenDeleteClientModal }) => {
  const { currentPage, setCurrentPage, totalPages } = useClarificationStore()

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1)
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1)
  }

  const handleDeleteClient = (clarification: Clarification) => {
    onOpenDeleteClientModal(clarification.trackingNumber)
  }

  return (
    <div>
      <ClarificationTable onDelete={handleDeleteClient} />
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        onNavigatePage={setCurrentPage}
      />
    </div>
  )
}

export default Clarifications
