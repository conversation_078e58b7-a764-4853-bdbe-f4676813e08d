'use client'

import { PaginationProps } from '@/types/types'
import styles from './styles/Pagination.module.css'
import { FaChevronRight, FaChevronLeft } from 'react-icons/fa'
import Select from './Select'

interface ExtendedPaginationProps extends PaginationProps {
  itemsPerPage?: number
  onItemsPerPageChange?: (itemsPerPage: number) => void
}

const Pagination: React.FC<ExtendedPaginationProps> = ({
  currentPage,
  totalPages,
  onPrevPage,
  onNextPage,
  onNavigatePage,
  itemsPerPage = 5,
  onItemsPerPageChange
}) => {
  const maxVisiblePages = 4 // Máximo número de páginas visibles en el rango
  const half = Math.floor(maxVisiblePages / 2)
  let startPage = Math.max(1, currentPage - half)
  let endPage = startPage + maxVisiblePages - 1

  if (endPage > totalPages) {
    endPage = totalPages
    startPage = Math.max(1, endPage - maxVisiblePages + 1)
  }

  const pageNumbers = Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i)

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newItemsPerPage = parseInt(e.target.value)
    if (onItemsPerPageChange) {
      onItemsPerPageChange(newItemsPerPage)
    }
  }

  return (
    <div className={styles.pagination}>
      <div className={styles.paginationLeft}>
        {onItemsPerPageChange && (
          <div className={styles.itemsPerPageContainer}>
            <Select
              name="itemsPerPage"
              value={itemsPerPage}
              onChange={handleItemsPerPageChange}
            >
              <option value={5}>Mostrar 5 registros</option>
              <option value={10}>Mostrar 10 registros</option>
              <option value={20}>Mostrar 20 registros</option>
              <option value={50}>Mostrar 50 registros</option>
              <option value={100}>Mostrar 100 registros</option>
            </Select>
          </div>
        )}
      </div>

      {/* Controles de navegación y números de página */}
      <div className={styles.paginationControls}>
        {/* Botón de página anterior */}
        <button
          className={`${styles.paginationButton} ${
            currentPage === 1 || totalPages === 0 ? styles.disabled : ''
          }`}
          onClick={onPrevPage}
          disabled={currentPage === 1 || totalPages === 0}
          aria-label="Página anterior"
        >
          <FaChevronLeft />
        </button>

        {/* Números de páginas */}
        <div className={styles.pageNumbers}>
          {pageNumbers.map(number => (
            <button
              key={number}
              onClick={() => onNavigatePage(number)}
              className={`${styles.pageButton} ${currentPage === number ? styles.activePage : ''}`}
              aria-label={`Página ${number}`}
            >
              {number}
            </button>
          ))}
        </div>

        {/* Botón de página siguiente */}
        <button
          className={`${styles.paginationButton} ${
            currentPage === totalPages || totalPages === 0 ? styles.disabled : ''
          }`}
          onClick={onNextPage}
          disabled={currentPage === totalPages || totalPages === 0}
          aria-label="Página siguiente"
        >
          <FaChevronRight />
        </button>
      </div>

      {/* Información de rango de páginas */}
      <div className={styles.pageInfo}>
        {totalPages === 0 ? '0 of 0' : `${startPage}-${endPage} of ${totalPages}`}
      </div>
    </div>
  )
}

export default Pagination
