'use client'
import React, { useState } from 'react'
import styles from '../../styles/AccountsTable.module.css'
import DateFilter from '../../DateFilter'
import SearchBar from '../../SearchBar'
import Pagination from '../../Pagination'
import { useListTransactions } from '@/hooks/useListTransactions'
import { formatDateForComparing } from '@/utils/formatters'
import DownloadIcon from '../../icons/DownloadIcon'
import Skeleton from '../../Skeleton'

type AccountTransactionsTableProps = {
  email: string
  aliasEmpresa?: string
}

const AccountTransactionsTable = ({ email, aliasEmpresa }: AccountTransactionsTableProps) => {
  const [selectedDates, setSelectedDates] = useState<string[]>([])
  const { transactions, isLoading } = useListTransactions(email, selectedDates[0], selectedDates[1])
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)

  const itemsPerPage = 5

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleDateFilter = (dates: string[]) => {
    setSelectedDates(dates)
    setCurrentPage(1)
  }

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1)
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1)
  }

  const filteredTransactions = transactions
    .filter(tx => !!tx.key) // Solo transacciones con key
    .filter(tx => {
      const matchesSearch =
        (tx.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (tx.id || '').toString().includes(searchTerm) ||
        (tx.key || '').toLowerCase().includes(searchTerm.toLowerCase())

      const matchesDate = (() => {
        if (selectedDates.length === 0) return true
        
        const txDate = formatDateForComparing(tx.date)
        
        // Para modo range (2 fechas): verificar si está en el rango
        if (selectedDates.length === 2) {
          const [startDate, endDate] = selectedDates
          return txDate >= startDate && txDate <= endDate
        }
        
        // Para modo single (1 fecha): verificar coincidencia exacta
        if (selectedDates.length === 1) {
          return selectedDates[0] === txDate
        }
        
        return false
      })()

      return matchesSearch && matchesDate
    })

  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentTransactions = filteredTransactions.slice(startIndex, endIndex)

  return (
    <>
      <div className={styles.header}>
        <DateFilter onDateChange={handleDateFilter} mode="range" />
        <SearchBar placeholder="Buscar transacciones" onSearch={handleSearch} />
      </div>

      <table className={styles.table}>
        <thead>
          <tr>
            <th>Concepto</th>
            <th>Monto</th>
            <th>Fecha de operación</th>
            <th>Hora</th>
            <th>Número de transacción</th>
            <th>Clave de rastreo</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          {isLoading ? (
            Array.from({ length: 5 }).map((_, index) => (
              <tr key={`skeleton-${index}`}>
                <td>
                  <Skeleton width={120} height={14} rounded />
                </td>
                <td>
                  <Skeleton width={80} height={14} rounded />
                </td>
                <td>
                  <Skeleton width={100} height={14} rounded />
                </td>
                <td>
                  <Skeleton width={60} height={14} rounded />
                </td>
                <td>
                  <Skeleton width={140} height={14} rounded />
                </td>
                <td>
                  <Skeleton width={120} height={14} rounded />
                </td>
                <td>
                  <div className={styles.actions}>
                    <Skeleton width={24} height={24} circle />
                  </div>
                </td>
              </tr>
            ))
          ) : currentTransactions.length > 0 ? (
            currentTransactions.map((tx, index) => (
              <tr key={index}>
                <td>{tx.description || 'Sin concepto'}</td>
                <td>{tx.amount}</td>
                <td>{tx.dateFormatted}</td>
                <td>{tx.timeFormatted}</td>
                <td>{tx.id}</td>
                <td>{tx.key}</td>
                <td>
                  <div className={styles.actions}>
                    {tx.id ? (
                      <button
                        className={styles.actionButton}
                        onClick={() => {
                          const datailsMovement = {
                            ...tx,
                            aliasEmpresa,
                          }
                          localStorage.setItem('detalleMovimiento', JSON.stringify(datailsMovement))
                          window.location.href = `/detalle-movimiento`
                        }}
                      >
                        <DownloadIcon />
                      </button>
                    ) : (
                      <span className={styles.noFile}></span>
                    )}
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={7} className={styles.noResults}>
                No se encontraron resultados
              </td>
            </tr>
          )}
        </tbody>
      </table>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        onNavigatePage={setCurrentPage}
      />
    </>
  )
}

export default AccountTransactionsTable
