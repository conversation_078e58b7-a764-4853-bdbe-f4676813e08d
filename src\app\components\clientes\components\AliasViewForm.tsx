import React from 'react'
import styles from '../../styles/NewClientAdmin.module.css'
import { AdminDetail } from '@/types/admin/types'

interface Props {
  client: AdminDetail
}

const AliasViewForm: React.FC<Props> = ({ client }) => {
  return (
    <div className={styles.formContainer}>
      <div className={styles.inputGroup}>
        <label>Alias</label>
        <input type="text" value={client.alias} readOnly />
      </div>

      <div className={styles.inputGroup}>
        <label>Nombre del responsable</label>
        <input type="text" value={client.manager.name} readOnly />
      </div>

      <div className={styles.inputGroup}>
        <label>RFC</label>
        <input type="text" value={client.rfc} readOnly />
      </div>

      <div className={styles.inputGroup}>
        <label>Fecha de alta</label>
        <input type="text" value={new Date(client.createdAt).toLocaleDateString()} readOnly />
      </div>

      <div className={styles.inputGroup}>
        <label>Tipo de perfil</label>
        <input
          type="text"
          value={
            client.manager.enterprises.length > 0
              ? client.manager.enterprises[0].roleName
              : 'son rol'
          }
          readOnly
        />
      </div>

      <div className={styles.inputGroup}>
        <label>Saldo</label>
        <input
          type="text"
          value={
            typeof client.amount === 'string'
              ? Number(client.amount).toLocaleString('es-MX', {
                  style: 'currency',
                  currency: 'MXN',
                })
              : 'Sin saldo'
          }
          readOnly
        />{' '}
        {/* si no tienes el saldo real aún */}
      </div>

      <div className={styles.inputGroup}>
        <label>Número de cuenta CONVENIA</label>
        <input type="text" value={client.groupId.toString()} readOnly />
      </div>

      <div className={styles.inputGroup}>
        <label>CLABE interbancaria</label>
        <input type="text" value={client.manager.clabe} readOnly />{' '}
        {/* si tienes un campo futuro, lo agregas aquí */}
      </div>
    </div>
  )
}

export default AliasViewForm
