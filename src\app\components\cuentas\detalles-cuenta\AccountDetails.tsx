import React from 'react'
import CardsUsedTable from '../components/CardsUsedTable'
import { AccountDetailsProps } from '@/types/types'
import styles from '../../styles/AccountsDetails.module.css'
import AccountTransactionsTable from '../components/AccountTransactionsTable'

const AccountDetails = ({
  // accounts,
  // onDownload,
  onToggleCard,
  onDeleteCard,
  onCardReassignment,
  email,
  aliasEmpresa,
}: AccountDetailsProps) => {
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <p className={styles.title}>Tarjetas utilizadas</p>
        <CardsUsedTable
          onToggleCard={onToggleCard}
          onCardReassignment={acc => onCardReassignment(acc)}
          onDeleteCard={acc => onDeleteCard(acc.cardNumber, acc.id)}
        />
      </div>
      <div className={styles.content}>
        <p className={styles.title}>Movimientos de la cuenta</p>
        <AccountTransactionsTable email={email} aliasEmpresa={aliasEmpresa} />
      </div>
    </div>
  )
}

export default AccountDetails
