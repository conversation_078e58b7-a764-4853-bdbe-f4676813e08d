'use client'

import { useEffect, useState } from 'react'
import { MdCreditCard } from 'react-icons/md'
import ReportFilters from '@/app/components/ReportFilters'
import TextInput from '@/app/components/Input'
import DateFilter from '@/app/components/DateFilter'
import ModalLoading from '@/app/components/modals/alertModals/ModalLoading'
import { getEmbossingBatchesReport } from '@/api/endpoints/reports'
import { downloadFileFromApi } from '@/utils/downloadFileFromApi'
import { EmbossingBatchesReportParams } from '@/types/reports/types'

interface EmbossingBatchesReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
}

const EmbossingBatchesReport = ({ onLoadingChange }: EmbossingBatchesReportProps) => {
  const [status, setStatus] = useState('')
  const [embosserId, setEmbosserId] = useState('')
  const [creationDate, setCreationDate] = useState<string | undefined>()
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('')
  const [dateClearKey, setDateClearKey] = useState(0)

  /** Descarga del reporte */
  const handleDownload = async () => {
    const filters: EmbossingBatchesReportParams = {}
    const active: string[] = []

    if (status) {
      filters.status = status
      active.push(`Status: ${status}`)
    }

    if (embosserId) {
      filters.embosser_id = embosserId
      active.push(`Embosser ID: ${embosserId}`)
    }

    if (creationDate) {
      filters.creation_date = creationDate
      active.push(`Fecha: ${creationDate}`)
    }

    setActiveFilters(active)

    try {
      setIsLoading(true)
      setLoadingMessage('Generando reporte de batches...')
      const blob = await getEmbossingBatchesReport(filters)
      downloadFileFromApi(blob, 'embossing_batches.xlsx')
    } catch (err) {
      console.error('Error al descargar el reporte:', err)
    } finally {
      setIsLoading(false)
      setLoadingMessage('')
    }
  }

  /** Manejo del DateFilter */
  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      setCreationDate(undefined)
      setActiveFilters(prev => prev.filter(f => !f.startsWith('Fecha')))
      return
    }
    if (dates.length === 1) {
      setCreationDate(dates[0])
      const [y, m, d] = dates[0].split('-')
      setActiveFilters(prev => [
        ...prev.filter(f => !f.startsWith('Fecha')),
        `Fecha: ${d}/${m}/${y}`,
      ])
    }
  }

  /** Reset completo */
  const handleReset = () => {
    setStatus('')
    setEmbosserId('')
    setCreationDate(undefined)
    setActiveFilters([])
    setDateClearKey(k => k + 1)
  }

  /** Actualiza burbuja de Status dinámicamente */
  useEffect(() => {
    setActiveFilters(prev => {
      const base = prev.filter(f => !f.startsWith('Status:')) // limpiamos burbuja anterior
      return status ? [...base, `Status: ${status}`] : base
    })
  }, [status])

  /** Actualiza burbuja de Embosser ID dinámicamente */
  useEffect(() => {
    setActiveFilters(prev => {
      const base = prev.filter(f => !f.startsWith('Embosser ID:')) // limpiamos burbuja anterior
      return embosserId ? [...base, `Embosser ID: ${embosserId}`] : base
    })
  }, [embosserId])

  /** loading en el padre */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <>
      <ModalLoading open={isLoading} title={loadingMessage || 'Generando reporte...'} />
      <ReportFilters
        title="Batches de Embossing"
        icon={<MdCreditCard />}
        activeFilters={activeFilters}
        isLoading={isLoading}
        onSubmit={e => e.preventDefault()}
        onReset={handleReset}
        downloadOptions={{ onlyExcel: true, onExcelDownload: handleDownload }}
      >
        {/* Inputs */}
        <TextInput
          label="Status"
          placeholder="OPEN, CLOSED, etc"
          value={status}
          onChange={e => setStatus(e.target.value)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              // Lógica adicional si se desea forzar la búsqueda con Enter sin submit global
            }
          }}
        />

        <TextInput
          label="Embosser ID"
          placeholder="ID del embosser"
          value={embosserId}
          onChange={e => setEmbosserId(e.target.value)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              // Lógica adicional si se desea forzar la búsqueda con Enter sin submit global
            }
          }}
        />

        {/* DateFilter en lugar de input manual */}
        <DateFilter
          mode="single"
          onDateChange={handleDateChange}
          label="Fecha de creación"
          placeholder="Selecciona la fecha de creación"
          clearTrigger={dateClearKey}
        />
      </ReportFilters>
    </>
  )
}

export default EmbossingBatchesReport
