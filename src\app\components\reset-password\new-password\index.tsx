'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { <PERSON><PERSON><PERSON>, GoEyeClosed } from 'react-icons/go'
import GoBackButton from '../../GoBackButton'
import stylesResetPassword from '../../styles/ResetPasswordCard.module.css'
import stylesNewPassword from '../../styles/NewPassword.module.css'
import Button from '../../Button'
import Image from 'next/image'
import { resetPassword } from '@/api/endpoints/user'
import { useOtpStore } from '@/store/reset-password/useOtpStore'
import LoaderFull from '../../loader/LoaderFull'

export type CardType = {
  className?: string
}

const NewPassword = () => {
  const router = useRouter()
  const { otpData, clearOtpData } = useOtpStore()
  const email = otpData?.email ?? ''
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [loading, setLoading] = useState(false)
  const [passwordValidations, setPasswordValidations] = useState({
    hasMinLength: false,
    hasLetter: false,
    hasNumber: false,
  })
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  })

  const togglePasswordVisibility = () => setShowPassword(prev => !prev)
  const toggleConfirmPasswordVisibility = () => setShowConfirmPassword(prev => !prev)

  const validatePassword = (password: string): string => {
    if (password.length < 8) {
      return 'La contraseña debe tener al menos 8 caracteres'
    }
    if (!/[A-Za-z]/.test(password)) {
      return 'La contraseña debe contener al menos una letra'
    }
    if (!/[0-9]/.test(password)) {
      return 'La contraseña debe contener al menos un número'
    }
    return '' // Sin errores
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))

    if (name === 'password') {
      setPasswordValidations({
        hasMinLength: value.length >= 8,
        hasLetter: /[A-Za-z]/.test(value),
        hasNumber: /[0-9]/.test(value),
      })

      const validationError = validatePassword(value)
      setError(validationError)
    }

    if (name === 'confirmPassword') {
      setIsTyping(true)
      if (value !== formData.password) {
        setError('Tu contraseña no coincide')
      } else {
        setError('') // Limpiar el error si coinciden
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    const { password, confirmPassword } = formData

    // Validar la contraseña
    const validationError = validatePassword(password)
    if (validationError) {
      setError(validationError)
      return
    }

    // Validar que las contraseñas coincidan
    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden')
      return
    }

    try {
      // Lógica para enviar la nueva contraseña al backend
      const response = await resetPassword({
        email: Array.isArray(email) ? email[0] : email,
        password,
      })

      if (response.statusCode === 200) {
        // Redirigir al login después de guardar la contraseña
        clearOtpData() // Limpiar los datos del store
        setFormData({ password: '', confirmPassword: '' }) // Limpiar el formulario
        router.replace('/reset-password/success-reset-password')
      }
    } catch (error) {
      console.error('Error al restablecer la contraseña:', error)
      setLoading(false) // Finaliza el estado de carga
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter') {
      handleSubmit(e)
    }
  }

  return (
    <div className={[stylesNewPassword.card].join(' ')} onKeyDown={handleKeyDown} tabIndex={0}>
      {loading && ( <LoaderFull />) }
      <section className={stylesResetPassword.navigation}>
        <GoBackButton onPress={() => router.back()} />
        <div className={stylesResetPassword.navigationInner}>
          <div className={stylesResetPassword.headerParent}>
            <div className={stylesResetPassword.header}>
              <h1 className={stylesResetPassword.heading} style={{ fontWeight: 500 }}>
                Restablecer contraseña
              </h1>
              <p className={stylesResetPassword.text}>
                Tu nueva contraseña debe ser diferente de las utilizadas anteriormente.
              </p>
              <div className={stylesNewPassword.hintCheckboxesParent}>
                <div className={stylesNewPassword.hintCheckboxes}>
                  <Image
                    className={stylesNewPassword.iconCheck}
                    loading="lazy"
                    width={16}
                    height={16}
                    alt=""
                    src={
                      passwordValidations.hasMinLength
                        ? '/logo-check.svg'
                        : '/logo-alert-circle.svg'
                    }
                  />
                  <div className={stylesNewPassword.hintText}>Debe tener al menos 8 caracteres</div>
                </div>
                <div className={stylesNewPassword.hintCheckboxes}>
                  <Image
                    className={stylesNewPassword.iconCheck}
                    loading="lazy"
                    width={16}
                    height={16}
                    alt=""
                    src={
                      passwordValidations.hasLetter && passwordValidations.hasNumber
                        ? '/logo-check.svg'
                        : '/logo-alert-circle.svg'
                    }
                  />
                  <div className={stylesNewPassword.hintText}>Debe tener números y letras</div>
                </div>
              </div>
            </div>
            <div
              className={
                isTyping && error
                  ? stylesNewPassword.invalidInputPass
                  : stylesNewPassword.inputGroupPassword
              }
            >
              <label htmlFor="password" className={stylesNewPassword.inputGroupPasswordLabel}>
                Nueva Contraseña
              </label>
              <div
                className={`${stylesNewPassword.passwordWrapper} ${isTyping && error ? stylesNewPassword.invalidInputPass : ''
                  }`}
              >
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  placeholder="********"
                  value={formData.password}
                  onChange={handleChange}
                  required
                />

                {formData.password && (
                  <button
                    type="button"
                    className={stylesNewPassword.eyeButton}
                    onClick={togglePasswordVisibility}
                    aria-label="Mostrar/Ocultar contraseña"
                  >
                    {showPassword ? <GoEye /> : <GoEyeClosed />}
                  </button>
                )}
              </div>
              <label
                htmlFor="confirmPassword"
                className={stylesNewPassword.inputGroupPasswordLabel}
              >
                Confirmar Contraseña
              </label>
              <div
                className={`${stylesNewPassword.passwordWrapper} ${isTyping && error ? stylesNewPassword.invalidInputPass : ''
                  }`}
              >
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  name="confirmPassword"
                  placeholder="********"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                />

                {formData.confirmPassword && (
                  <button
                    type="button"
                    className={stylesNewPassword.eyeButton}
                    onClick={toggleConfirmPasswordVisibility}
                    aria-label="Mostrar/Ocultar contraseña"
                  >
                    {showConfirmPassword ? <GoEye /> : <GoEyeClosed />}
                  </button>
                )}
              </div>
              {isTyping && error && <p className={stylesResetPassword.errorText}>{error}</p>}
            </div>
          </div>
        </div>
      </section>
      <Button
        text={'Reestablecer contraseña'}
        type="submit"
        disabled={!!error || !formData.password || !formData.confirmPassword || loading}
        fullWidth
        onClick={handleSubmit}
      />
    </div>
  )
}

export default NewPassword
