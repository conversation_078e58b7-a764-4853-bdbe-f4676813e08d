/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import { fetchRoles } from '@/api/endpoints/role'
import { RoleStore } from '@/types/role/types'

export const useRoleStore = create<RoleStore>(set => ({
  roles: [],
  loading: false,
  error: null,
  fetchRoles: async roleType => {
    set({ loading: true, error: null })
    try {
      const roles = await fetchRoles(roleType)
      set({ roles })
    } catch (error: any) {
      set({ error: error.message || 'Error al obtener roles' })
    } finally {
      set({ loading: false })
    }
  },
}))
