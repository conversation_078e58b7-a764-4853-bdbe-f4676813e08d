'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import GoBackButton from '../GoBackButton'
import styles from '../styles/ResetPasswordCard.module.css'
import Button from '../Button'
import { toast } from 'react-toastify'
import { generateOTPCode, verifyEmailRegistered } from '@/api/endpoints/user'
import { useOtpStore } from '@/store/reset-password/useOtpStore'
import LoaderFull from '../loader/LoaderFull'

export type CardType = {
  className?: string
}

const ResetPasswordCard = () => {
  const router = useRouter()
  const [email, setEmail] = useState('')
  const [isValidEmail, setIsValidEmail] = useState(true)
  const [isTyping, setIsTyping] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setEmail(value)

    setIsTyping(true)

    // Validar formato del correo
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    setIsValidEmail(emailRegex.test(value))

    // Si el campo está vacío, desactivar el estado de escritura
    if (value.trim() === '') {
      setIsTyping(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSubmit(e)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Llamar a la API para verificar si el correo existe
      const emailExist = await verifyEmailRegistered(email)

      if (!isValidEmail) {
        return
      }

      if (emailExist) {
        const response = await generateOTPCode(email)
        if (response.statusCode === 200) {
          const token = response.data.token
          // Guarda en store
          useOtpStore.getState().setOtpData({ email, token })

          // Navega sin pasar email/token por URL
          router.push('/reset-password/otp')
        }
      } else {
        toast.error('Correo no encontrado', {
          position: 'top-right',
          autoClose: 3000,
          pauseOnHover: true,
          draggable: true,
        })
        setLoading(false)
      }
    } catch (error) {
      console.error('Error al enviar el código:', error)
      setLoading(false) // Finaliza el estado de carga
    }
  }

  return (
    <div className={[styles.card].join(' ')}>
      {loading && <LoaderFull />}
      <section className={styles.navigation}>
        <GoBackButton onPress={() => router.back()} />
        <div className={styles.navigationInner}>
          <div className={styles.headerParent}>
            <div className={styles.header}>
              <h1 className={styles.heading} style={{ fontWeight: 500 }}>
                Restablecer contraseña
              </h1>
              <p className={styles.text}>
                Te enviaremos por correo electrónico un código para restablecer tu contraseña
              </p>
            </div>
            <div className={styles.inputGroup}>
              <label htmlFor="email">Correo electrónico</label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="Ingresa correo electrónico"
                value={email}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                required
                className={!isValidEmail && isTyping ? styles.invalidInput : ''}
              />
              {!isValidEmail && isTyping && (
                <p className={styles.errorText}>Ingresa un correo válido</p>
              )}
            </div>
          </div>
        </div>
      </section>
      <Button
        text={'Enviar código'}
        type="submit"
        disabled={!email.trim() || loading}
        fullWidth
        onClick={handleSubmit}
      />
    </div>
  )
}

export default ResetPasswordCard
