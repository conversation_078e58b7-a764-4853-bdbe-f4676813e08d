import { useState } from 'react'
import { toast } from 'react-toastify'

interface UseDownloadOptions {
  onSuccess?: () => void
  onError?: (error: Error) => void
  loadingMessage?: string
}

export const useDownload = (options?: UseDownloadOptions) => {
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState(options?.loadingMessage || 'Generando archivo para descargar...')

  const downloadExcel = async (fetchFn: () => Promise<Blob>, filename: string) => {
    try {
      setIsLoading(true)
      setLoadingMessage(options?.loadingMessage || 'Generando archivo para descargar...')

      const blob = await fetchFn()

      // Detectar si el blob es un error (por ejemplo, JSON con message)
      const isJson = blob.type && blob.type.includes('application/json')
      if (isJson) {
        // Leer el mensaje de error del blob
        const text = await blob.text()
        try {
          const json = JSON.parse(text)
          const msg = json.message || 'Error al descargar el reporte'
          const error = new Error(msg)
          options?.onError?.(error)
          toast.error(msg, {
            position: 'top-right',
            autoClose: 3000,
          })
        } catch {
          options?.onError?.(new Error(text))
          toast.error(text, {
            position: 'top-right',
            autoClose: 3000,
          })
        }
        setIsLoading(false)
        return
      }

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      options?.onSuccess?.()
      toast.success('Reporte descargado con éxito', {
        position: 'top-right',
        autoClose: 3000,
      })
    } catch (err) {
      options?.onError?.(err as Error)
      toast.error(err instanceof Error ? err.message : 'Error al descargar el reporte', {
        position: 'top-right',
        autoClose: 3000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isLoading,
    downloadExcel,
    loadingMessage,
  }
}
