/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import AliasForm from '../../components/AliasForm'
import Button from '@/app/components/Button'
import { useFormValidationAlias } from '@/hooks/useFormValidationAlias'
import MultiCompanySelectFields from '../../components/MultiCompanySelectFields'
import styles from '../../../styles/EditAliasClient.module.css'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { useEffect } from 'react'
import { AdminDetail } from '@/types/admin/types'
import { toast } from 'react-toastify'

// const companyOptions = ["Bimbo", "Oxxo", "Coca-Cola", "Soriana"]

interface Props {
  client: AdminDetail
  setOpenModal: (value: boolean) => void
}

const EditAliasClient = ({ client, setOpenModal }: Props) => {
  const { fetchBasicAdmins, basicAdmins, updateAdmin } = useAdminStore()

  const formatCurrency = (value: number | string): string => {
  const number = typeof value === 'string' ? parseFloat(value) : value
  return `$${number.toFixed(2)}`
}

  const { formData, errors, validate, handleInputChange, setFormData } = useFormValidationAlias({
    alias: client.alias,
    responsibleName: client.manager.name,
    rfc: client.rfc,
    registrationDate: new Date(client.createdAt).toLocaleDateString(),
    profileType: client.manager.enterprises?.[0]?.roleName || 'Sin rol',
    balance: formatCurrency(client.amount || 0),
    extraCompanies: client.manager.enterprises?.map(e => e.name) || [''],
  })

  const companies = formData.extraCompanies || []

  const handleAddCompany = () => {
    const updated = [...companies, '']
    setFormData({ ...formData, extraCompanies: updated })
  }

  const handleRemoveCompany = (index: number) => {
    const updated = companies.filter((_, i) => i !== index)
    setFormData({ ...formData, extraCompanies: updated })
  }

  const handleCompanyChange = (index: number, value: string) => {
    const updated = [...companies]
    updated[index] = value
    setFormData({ ...formData, extraCompanies: updated })
  }

  const handleSubmit = async () => {
  if (!validate()) return

  try {
    const payload = {
      alias: formData.alias,
      rfc: formData.rfc,
      extraCompanies: formData.extraCompanies,
    }

    await updateAdmin(client.id, payload)
    setOpenModal(true) // 👉 Solo se abre el modal si se actualiza correctamente
   } catch (error: any) {
    console.error('Error actualizando cliente:', error)
    toast.error(
      error?.response?.data?.message || 'Hubo un error al actualizar el cliente.',
      {
        position: 'top-right',
        autoClose: 3000,
      }
    )
  }
}


  useEffect(() => {
    fetchBasicAdmins()
  }, [fetchBasicAdmins])

  return (
    <div className={styles.formContainer}>
      <AliasForm formData={formData} onChange={handleInputChange} errors={errors} />

      <div className={styles.formGroup}>
        <p className={styles.formGroupTitle}>Esta cuenta gestiona múltiples empresas</p>
        <MultiCompanySelectFields
          companies={companies}
          companyOptions={basicAdmins}
          errors={{ extraCompanies: errors.extraCompanies }}
          handleCompanyChange={handleCompanyChange}
          handleRemoveCompany={handleRemoveCompany}
          handleAddCompany={handleAddCompany}
        />
      </div>

      <Button onClick={handleSubmit} text="Editar" fullWidth />
    </div>
  )
}

export default EditAliasClient
