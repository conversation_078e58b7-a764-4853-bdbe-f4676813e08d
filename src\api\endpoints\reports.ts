import client from '../client'
import {
  AliasHistoryReportParams,
  CardStatusReportParams,
  ConveniaMovementsReportParams,
  DailyMonthlyReportParams,
  CostCommissionsReportPayload,
  EmbossingBatchesReportParams,
  ServiceUsageReportParams,
  AmountsReportParams,
  IssuedCardsReportParams,
  PeriodClosingsReportParams,
  CardTransactionsReportParams
} from '@/types/reports/types'
import { Statement, StatementParams } from '@/types/reports/statement'

export const getCardStatusReport = async (params: CardStatusReportParams) => {
  const response = await client.get('/reports/card-status', {
    params: {
      ...params,
      startDate: params.startDate ? new Date(params.startDate).toISOString() : undefined,
      endDate: params.endDate ? new Date(params.endDate).toISOString() : undefined,
    },
    responseType: 'blob',
  })
  return response.data
}

export const getConveniaMovementsReport = async (params: ConveniaMovementsReportParams) => {
  const response = await client.get('/reports/convenia-movements', {
    params: {
      ...params,
      startDate: params.startDate ? new Date(params.startDate).toISOString() : undefined,
      endDate: params.endDate ? new Date(params.endDate).toISOString() : undefined,
    },
    responseType: 'blob',
  })
  return response.data
}

export const getAliasHistoryReport = async (params: AliasHistoryReportParams) => {
  const response = await client.get('/reports/alias-history', {
    params: {
      ...params,
      startDate: params.startDate ? new Date(params.startDate).toISOString() : undefined,
      endDate: params.endDate ? new Date(params.endDate).toISOString() : undefined,
    },
    responseType: 'blob',
  })
  return response.data
}

export const getTransactionsDailyMonthlyReport = async (params: DailyMonthlyReportParams) => {
  const response = await client.get(`/reports/daily-monthly-report`, {
    params: {
      ...params
    },
    responseType: 'blob',
  })
  return response.data
}

export const getEmbossingBatchesReport = async (params: EmbossingBatchesReportParams) => {
  const response = await client.get('/reports/embossing-batches', {
    params,
    responseType: 'blob',
  })
  return response.data
}

export const getCostCommissionsReport = async (params: CostCommissionsReportPayload) => {
  const response = await client.get('/reports/cost-commission', {
    params: {
      ...params,
    },
    responseType: 'blob',
  })
  return response.data
}

export const getServiceUsageReport = async (params: ServiceUsageReportParams) => {
  const response = await client.get('/reports/service-usage', {
    params,
    responseType: 'blob',
  })
  return response.data
}

export const getAmountsReport = async (params: AmountsReportParams) => {
  const response = await client.get('/reports/amounts', {
    params,
    responseType: 'blob'
  })
  return response.data
}

export const getIssuedCardsReport = async (params: IssuedCardsReportParams) => {
  const response = await client.get('/reports/issued-cards', {
    params: {
      ...params,
      startDate: params.startDate ? new Date(params.startDate).toISOString() : undefined,
      endDate: params.endDate ? new Date(params.endDate).toISOString() : undefined,
    },
    responseType: 'blob'
  })
  return response.data
}

export const getPeriodClosingsReport = async (params: PeriodClosingsReportParams) => {
  const response = await client.get('/reports/period-closings', {
    params: {
      year: params.year,
      month: params.month,
      day: params.day
    },
    responseType: 'blob'
  })
  return response.data
}

export const getAccountStatement = async (params: StatementParams): Promise<Statement> => {
  const response = await client.get(`/reports/${params.userId}/statement`, {
    params: {
      startDate: params.startDate,
      endDate: params.endDate
    }
  })
  return response.data
}

export const getCardTransactionsReport = async (params: CardTransactionsReportParams) => {
  // Mostrar el body que se enviará
  // eslint-disable-next-line no-console
  console.log('POST /reports/card-transactions body:', params);
  const response = await client.post(
    '/reports/card-transactions',
    params,
    {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
}



