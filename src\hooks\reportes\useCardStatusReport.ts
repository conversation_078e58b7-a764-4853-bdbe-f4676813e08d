import { useState } from 'react'
import { CardStatusReportParams } from '@/types/reports/types'
import { getCardStatusReport } from '@/api/endpoints/reports'
import { useDownload } from '../useDownload'

export const useCardStatusReport = () => {
  const [error, setError] = useState<string | null>(null)
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: (err) => setError(err.message),
    loadingMessage: 'Generando reporte de estado de tarjetas...'
  })

  const downloadReport = async (params: CardStatusReportParams) => {
    const filename = `reporte-estado-tarjetas-${new Date().toISOString().split('T')[0]}.xlsx`
    await downloadExcel(
      () => getCardStatusReport(params),
      filename
    )
  }

  return {
    isLoading,
    loadingMessage,
    error,
    downloadReport
  }
}
