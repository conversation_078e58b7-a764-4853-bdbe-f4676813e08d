import React from 'react'
import styles from '../../styles/MassiveLoadTransferSuccess.module.css'
import AlertModal from '../AlertModal'

type Props = {
  open: boolean
  total: number
  onMinimize?: () => void
}

const MassiveLoadTransferSuccess: React.FC<Props> = ({ open, total, onMinimize }) => {
  if (!open) return null
  return (
    <AlertModal
      type="warning"
      title="Procesando transferencia masiva"
      message="Te notificaremos por correo cuando la transferencia masiva esté completa."
      open={open}
      onClose={onMinimize}
      textBtn="Minimizar"
      onPressBtn={onMinimize}
      renderMessage={() => (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            margin: '20px',
            flexDirection: 'column',
            gap: '20px',
          }}
        >
          <div className={styles.total}>{total} transferencias en proceso</div>
          <ol className={styles.steps}>
            <li>
              <span className={styles.stepNumber}>1</span> Validaremos cuentas
            </li>
            <li>
              <span className={styles.stepNumber}>2</span> Procesaremos transferencias
            </li>
            <li>
              <span className={styles.stepNumber}>3</span> Te enviaremos correo de confirmación
              exitosa
            </li>
          </ol>
          <div className={styles.errorBox}>
            <b>En caso de error:</b>
            <p>
              Te notificaremos por correo e incluiremos un archivo Excel con el detalle para que
              puedas corregirlos
            </p>
          </div>
        </div>
      )}
    />
  )
}

export default MassiveLoadTransferSuccess
