.input {
  max-height: 38px;
}

.inputContainerHelperSuggestions {
  position: relative;
  width: 100%;
  display: flex;
}

.suggestionsContainer {
  position: absolute;
  z-index: 1000;
  background: white;
  /* border: 1px solid #ccc; */
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  margin-top: 5px;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  top: 38px;
  left: 0;
}

.suggestionItem {
  padding: 8px 12px;
  cursor: pointer;
}

.suggestionItem:hover {
  background-color: #f5f5f5;
}
