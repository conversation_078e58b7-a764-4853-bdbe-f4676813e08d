import React, { useState, useEffect } from 'react'
import AlertModal from '../AlertModal'
import Select from '../../Select'
import { useAccountStore } from '@/store/account/useAccountStore'

type ModalSelectDateProps = {
  open: boolean
  onConfirm: (date: string) => void
  onClose?: () => void
}

const ModalSelectDate: React.FC<ModalSelectDateProps> = ({ open, onConfirm, onClose }) => {
  const [dates, setDates] = useState<{ value: string; label: string }[]>([])
  const [selectedDate, setSelectedDate] = useState<string>('')

  const { accountSelected } = useAccountStore()

  useEffect(() => {
    const generateDates = () => {
      const createdAt = accountSelected?.createdAt
      if (!createdAt) return []

      const result: { value: string; label: string }[] = []
      const now = new Date()

      const accountCreated = new Date(createdAt)
      const tenYearsAgo = new Date(now)
      tenYearsAgo.setFullYear(now.getFullYear() - 10)

      // ✅ Limitar createdAt a no más de 10 años
      const start = accountCreated < tenYearsAgo ? tenYearsAgo : accountCreated

      start.setDate(1)
      start.setHours(0, 0, 0, 0)

      const end = new Date(now)
      end.setDate(1)
      end.setHours(0, 0, 0, 0)

      const months = [
        'Enero',
        'Febrero',
        'Marzo',
        'Abril',
        'Mayo',
        'Junio',
        'Julio',
        'Agosto',
        'Septiembre',
        'Octubre',
        'Noviembre',
        'Diciembre',
      ]
      const current = new Date(start)
      while (current <= end) {
        const year = current.getFullYear()
        const month = current.getMonth() + 1
        const value = `${year}-${String(month).padStart(2, '0')}`
        const label = `${months[current.getMonth()]} de ${year}`
        result.push({ value, label })
        current.setMonth(current.getMonth() + 1)
      }
      return result
    }

    setDates(generateDates())
  }, [open, accountSelected?.createdAt])
  return (
    <AlertModal
      type="warning"
      title="Descargar Estado de Cuenta"
      message="Selecciona lo siguiente para poder ver en tu estado de cuenta."
      open={open}
      onClose={onClose}
      textBtn="Descargar estado de cuenta"
      onPressBtn={() => selectedDate && onConfirm(selectedDate)}
      renderMessage={() => (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            margin: '20px 0',
            width: '100%',
          }}
        >
          <Select
            className="fullWidth"
            name="date"
            label="Elige la fecha"
            value={selectedDate}
            onChange={e => setSelectedDate(e.target.value)}
          >
            <option value="">Selecciona una fecha</option>
            {dates.map(date => (
              <option key={date.value} value={date.value}>
                {date.label}
              </option>
            ))}
          </Select>
        </div>
      )}
    />
  )
}

export default ModalSelectDate
