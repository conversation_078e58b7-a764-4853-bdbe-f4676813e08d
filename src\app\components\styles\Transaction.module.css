.subtitle {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */

  color: #475467;

  margin: 32px 0 31.5px;
  text-rendering: geometricPrecision;
}

.title {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;

  color: #101828;
}

.tabContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  margin-top: 24px;
  /* border-bottom: 1px solid #EAECF0; */
}

.formContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem 2rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  align-self: stretch;
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.montoInput::-webkit-outer-spin-button,
.montoInput::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.montoInput {
  -moz-appearance: textfield;
  /* Firefox */
}

/* Seccion succes */

.sucessWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  padding: 76px 0px;

  background: #ffffff;
  /* Shadows/shadow-xl */
  box-shadow:
    0px 20px 24px -4px rgba(16, 24, 40, 0.08),
    0px 8px 8px -4px rgba(16, 24, 40, 0.03);
  border-radius: 12px;

  margin: 0 -30px;
  /* Added negative margin to counteract parent padding */
}

.buttonMassive {
  display: block;
  margin-left: auto;
  margin-right: 0;
}

.selectWrapper {
  display: flex;
  gap: 6px;
  flex-direction: column;
}

.selectWrapper label {
  color: #000;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.threeInputsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem 2rem;
}

.threeInputsContainer > * {
  flex: 1;
  min-width: 0;
}

.switchWrapper {
  display: flex;
  gap: 24px;
  align-items: center;
}

.swhitchLabel {
  /* Body/Large */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;

  color: #475467;
}

.newClientContainer {
  display: flex;
  flex-direction: column-reverse;
  gap: 1rem;
  justify-content: flex-end;
  align-items: flex-start;
}

/* contact list */

.contactsWrapper {
  display: flex;
  flex-direction: column;
  margin-top: 32px;
}

.contactsList {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.contactFrame {
  display: flex;
  flex-direction: column;
  padding: 16px 0;
  position: relative;
  gap: 8px;
}

.contactFrame:not(:last-child) {
  border-bottom: 1px solid #eaecf0;
}

.contactItem {
  display: flex;
  align-items: center;
}

.userItem {
  display: flex;
  flex-direction: column;
  flex: 0.4;
  cursor: pointer;
}

.contactAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f2f4f7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter';
  font-weight: 500;
  font-size: 16px;
  color: #101828;
  margin-right: 12px;
}

.contactInfo {
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  width: 200px;
}

.contactName {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;

  color: #000000;
}

.contactCategory {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  /* identical to box height */

  color: #000000;

  text-rendering: geometricPrecision;
}

.contactBankInfo {
  display: flex;
  flex: 1;
  gap: 8px 24px;
  flex-wrap: wrap;
  cursor: pointer;
}

.bankName {
  width: 200px;

  /* Body/Medium */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  /* identical to box height */

  color: #6f7280;
}

.accountNumber {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  /* identical to box height */

  color: #6f7280;
}

.contactActions {
  display: flex;
  gap: 8px;
}

.actionButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #555;
  font-family: Inter;
  font-size: 1.2rem;
  transition: color 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.actionButton:hover {
  color: #b4915f;
}

/* edit contact */

.successEditedtContactWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 108px;
}

.infoAmountToTransfer {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */
  color: #475467;
  margin: 0;
  padding: 0;
  text-rendering: geometricPrecision;
}

@media screen and (min-width: 650px) {
  .threeInputsContainer {
    flex-direction: row;
  }

  .newClientContainer {
    flex-direction: row;
  }
}
