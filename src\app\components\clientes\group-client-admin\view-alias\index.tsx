'use client'
import React, { Suspense } from 'react'
import Header from '@/app/components/Header'
import { FaArrowLeft } from 'react-icons/fa'
import styles from '../../../styles/Clientes.module.css'
import ViewAliasClient from './ViewAliasClient'
import Loader from '@/app/components/loader/Loader'
import { useAdminStore } from '@/store/admin/useAdminStore'

const HomePage = () => {
  return (
    <Suspense fallback={<Loader />}>
      <Content />
    </Suspense>
  )
}

const Content = () => {
  const { adminSelected } = useAdminStore()

  if (!adminSelected) return <p>Cliente no encontrado.</p>

  return (
    <>
      <Header title={`${adminSelected.alias}`} />
      <button className={styles.backButton} onClick={() => window.history.back()}>
        <FaArrowLeft size={16} className={styles.icon} /> Atrás
      </button>
      <ViewAliasClient client={adminSelected} />
    </>
  )
}

export default HomePage
