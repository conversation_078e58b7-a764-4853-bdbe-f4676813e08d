import React from 'react'
import styles from '../../styles/NewClientAdmin.module.css'
import { Errors, AliasFormData } from '@/hooks/useFormValidationAlias'
import { handleNumberOnlyInput } from '@/utils/inputRestrictions'
import { formatCurrency } from '@/utils/formatters'

type AliasFormProps = {
  formData: AliasFormData
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
  errors: Errors
}

const AliasForm = ({ formData, onChange, errors }: AliasFormProps) => {
  return (
    <div className={styles.formContainer}>
      <div className={styles.inputGroup}>
        <label>Alias</label>
        <input
          type="text"
          name="alias"
          value={formData.alias}
          onChange={onChange}
          className={errors.alias ? styles.error : ''}
        />
        {errors.alias && <span className={styles.error}>{errors.alias}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Nombre del responsable</label>
        <input
          type="text"
          name="responsibleName"
          value={formData.responsibleName}
          onChange={onChange}
          className={errors.responsibleName ? styles.error : ''}
        />
        {errors.responsibleName && <span className={styles.error}>{errors.responsibleName}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>RFC</label>
        <input
          type="text"
          name="rfc"
          value={formData.rfc}
          onChange={onChange}
          maxLength={13}
          className={errors.rfc ? styles.error : ''}
        />
        {errors.rfc && <span className={styles.error}>{errors.rfc}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Fecha de alta</label>
        <input
          type="text"
          name="registrationDate"
          value={formData.registrationDate}
          onChange={onChange}
          placeholder="12/Octubre/2000"
          className={errors.registrationDate ? styles.error : ''}
        />
        {errors.registrationDate && <span className={styles.error}>{errors.registrationDate}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Tipo de perfil</label>
        <select
          name="profileType"
          value={formData.profileType}
          onChange={onChange}
          className={errors.profileType ? styles.error : ''}
        >
          <option value="">Seleccionar</option>
          <option value="Lector">Lector</option>
          <option value="Editor">Editor</option>
          <option value="Administrador">Administrador</option>
        </select>
        {errors.profileType && <span className={styles.error}>{errors.profileType}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Saldo</label>
        <input
          type="text"
          name="balance"
          value={formatCurrency(formData.balance)}
          onChange={onChange}
          placeholder="$0.00"
          onKeyDown={handleNumberOnlyInput}
          className={errors.balance ? styles.error : ''}
        />
        {errors.balance && <span className={styles.error}>{errors.balance}</span>}
      </div>
    </div>
  )
}

export default AliasForm
