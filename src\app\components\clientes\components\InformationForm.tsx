import React, { useEffect, useState } from 'react'
import styles from '../../styles/NewClientAdmin.module.css'
import { Errors, FormData } from '@/hooks/useFormValidationClient'
import {
  handleAlphaNumericInput,
  handleNumberOnlyInput,
  handleTextOnlyInput,
  handleUppercaseInput,
} from '@/utils/inputRestrictions'
import { getStates, getCitiesByStateId } from '@/api/endpoints/catalogs'
import { Cities, States } from '@/types/catalogs/types'
import LoaderFull from '../../loader/LoaderFull'

type InformationFormProps = {
  formData: FormData
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
  errors: Errors
}

const InformationForm = ({ formData, onChange, errors }: InformationFormProps) => {
  const [states, setStates] = useState<States[]>([])
  const [cities, setCities] = useState<Cities[]>([])
  const [loadingStates, setLoadingStates] = useState(true)
  const [loadingCities, setLoadingCities] = useState(false)

  const mexicoTime = new Date().toLocaleString('en-US', { timeZone: 'America/Mexico_City' })

  if (formData.registrationDate !== '') {
    formData.registrationDate = new Date(formData.registrationDate).toISOString().split('T')[0]
  } else {
    formData.registrationDate = new Date(mexicoTime).toISOString().split('T')[0]
  }

  if (formData.assignedCards === '') {
    formData.assignedCards = '0'
  }

  useEffect(() => {
    // traer los estados desde la api y guardarlos en el estado
    const fetchStates = async () => {
      const response = await getStates()
      setStates(response)
      setLoadingStates(false)
    }

    fetchStates()
  }, [])

  useEffect(() => {
    // Cargar ciudades cuando cambie el estado seleccionado
    const fetchCities = async () => {
      if (formData.state) {
        setLoadingCities(true)
        try {
          const response = await getCitiesByStateId(formData.state)
          setCities(response)
        } catch (error) {
          console.error('Error al obtener ciudades:', error)
          setCities([])
        } finally {
          setLoadingCities(false)
        }
      } else {
        setCities([])
      }
    }

    fetchCities()
  }, [formData.state])

  return (
    <div className={styles.formContainer}>
      <div className={styles.inputGroup}>
        <label>Nombre de la compañía</label>
        <input
          type="text"
          name="companyName"
          placeholder="Mi Empresa S.A. de C.V."
          value={formData.companyName}
          onChange={onChange}
          onKeyDown={handleTextOnlyInput}
          className={errors.companyName ? styles.error : ''}
        />
        {errors.companyName && <span className={styles.error}>{errors.companyName}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Alias</label>
        <input
          type="text"
          name="alias"
          placeholder="MiEmpresa"
          value={formData.alias}
          onChange={onChange}
          className={errors.alias ? styles.error : ''}
        />
        {errors.alias && <span className={styles.error}>{errors.alias}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>RFC</label>
        <input
          type="text"
          name="rfc"
          placeholder="ABCD123456XYZ"
          value={formData.rfc}
          onChange={e => {
            handleUppercaseInput(e)
            onChange(e)
          }}
          maxLength={13}
          onKeyDown={handleAlphaNumericInput}
          className={errors.rfc ? styles.error : ''}
        />
        {errors.rfc && <span className={styles.error}>{errors.rfc}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Fecha de alta</label>
        <input
          type="date"
          name="registrationDate"
          value={formData.registrationDate}
          readOnly
          className={errors.registrationDate ? styles.error : ''}
        />
      </div>

      <div className={styles.inputGroup}>
        <label>Nombre del responsable</label>
        <input
          type="text"
          name="responsibleName"
          placeholder="Juan Pérez"
          value={formData.responsibleName}
          onChange={onChange}
          onKeyDown={handleTextOnlyInput}
          className={errors.responsibleName ? styles.error : ''}
        />
        {errors.responsibleName && <span className={styles.error}>{errors.responsibleName}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Número de tarjetas asignadas</label>
        <input
          type="text"
          name="assignedCards"
          placeholder="10"
          value={formData.assignedCards}
          onChange={onChange}
          onKeyDown={handleNumberOnlyInput}
          disabled={true}
          readOnly
          className={errors.assignedCards ? styles.error : ''}
        />
        {errors.assignedCards && <span className={styles.error}>{errors.assignedCards}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Estado</label>
        <select
          name="state"
          value={formData.state}
          onChange={onChange}
          className={errors.state ? styles.error : ''}
        >
          <option value="" disabled>
            Seleccione
          </option>
          {states.map(state => (
            <option key={state.id} value={state.id}>
              {state.name}
            </option>
          ))}
        </select>
        {errors.state && <span className={styles.error}>{errors.state}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Ciudad</label>
        <select
          name="city"
          value={formData.city}
          onChange={onChange}
          className={errors.city ? styles.error : ''}
          disabled={loadingCities || !formData.state}
        >
          <option value="">Seleccione</option>
          {cities.map(city => (
            <option key={city.id} value={city.id}>
              {city.name}
            </option>
          ))}
        </select>
        {errors.city && <span className={styles.error}>{errors.city}</span>}
      </div>

      {loadingStates || loadingCities ? <LoaderFull /> : null}
    </div>
  )
}

export default InformationForm
