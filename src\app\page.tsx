/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/auth/useAuthStore'

export default function PageRedirect() {
  const { user, hasTriedLogin } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!hasTriedLogin) return

    if (user) {
      router.replace('/home')
    } else {
      router.replace('/login')
    }
  }, [user, hasTriedLogin])

  return null
}
