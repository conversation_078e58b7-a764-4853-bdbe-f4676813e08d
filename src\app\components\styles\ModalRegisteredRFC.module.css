.modalContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px 50px 0;
}

.companyInfo {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 6px;
}

.label {
  color: #344054;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.input {
  width: 100%;
  display: flex;
  padding: 10px 14px;
  align-items: center;
  gap: var(--spacing-md, 8px);
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #f9fafb;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);

  color: #667085;
  text-overflow: ellipsis;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

.input:focus {
  border-color: #c19a6b;
  outline: none;
}

.confirmationText {
  color: #101828;
  font-family: Inter;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
}

.inputError {
  border: 1px solid #d92d20 !important;
  background: #fff5f5;
}

.inputError::placeholder {
  color: #d92d20;
}

.error {
  align-self: stretch;
  color: #d92d20;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
