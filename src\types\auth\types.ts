import { RoleName, RoleType } from '@/constants/roles'
import { Role } from '../role/types'

/* eslint-disable @typescript-eslint/no-explicit-any */
export type SignInData = {
  email: string
  password: string
  origin: 'web' | 'mobile'
}

export type RelUserRoleAdmin = {
  id: string
  role: Role
  admin: Admin | null
}

export type User = {
  id: string
  name: string
  email: string
  phone: string
  created_at: string
  created_by: string
  enabled: boolean
  isSpeiInEnabled: boolean | null
  isSpeiOutEnabled: boolean | null
  rfc: string | null
  relUserRoleAdmins: RelUserRoleAdmin[]
}

export type Admin = {
  id: string
  company_name: string
  alias: string
  rfc: string
  num_asigned_cards: number
  is_sucursal: boolean
  membership_number: number
  spei_in: number
  spei_out: number
  target_refound: number
  ambassador: string
  group_id: number
  createdAt: string
  managerId: string
  manager: {
    email: string
  }
}

export type AuthState = {
  user: User | null
  token: string | null
  loading: boolean
  error: string | null
  hasTriedLogin: boolean
}

export type AuthActions = {
  signIn: (email: string, password: string) => Promise<void>
  renewSession: () => Promise<void>
  logout: () => void
  checkSession: () => Promise<void>
  getUserRole: () => Role | null
  hasRole: (roleType: string) => boolean
  getUserRoleName: () => RoleName | null
  hasRoleName: (roleName: string) => boolean
  getUserRoleType: () => string | null
  getRoleType: () => RoleType
}
