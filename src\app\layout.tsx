// src/app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import SessionProvider from '@/providers/SessionProvider'
import ClientSidebar from './components/ClientSidebar'

const inter = Inter({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'Convenia',
  description: 'Convenia',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="es" className={inter.variable}>
      <head>
        <link rel="icon" href="/logo-convenia.svg" />
        <meta name="google" content="notranslate" />
      </head>
      <body>
        <SessionProvider>
          <ClientSidebar>{children}</ClientSidebar>
          <ToastContainer />
        </SessionProvider>
      </body>
    </html>
  )
}
