.titleHead {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;
  margin-bottom: 68px;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 360px;
  margin: 0 auto;
  /* padding-top: 68px; */
}

.title {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-size: 32px;
  line-height: 39px;
  text-align: center;

  color: #000000;
}

.successIcon {
  margin-top: 12px;
}

.date {
  margin-top: 8px;

  /* Body/Large */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  text-align: center;

  color: #000000;
}

.amount {
  /* Display/Small */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 36px;
  line-height: 44px;
  /* identical to box height */
  text-align: center;

  color: #000000;

  margin-top: 8px;
}

.commission {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  text-align: center;

  color: #000000;
  margin-top: 8px;
}

.infoSection {
  width: 100%;
  margin-top: 36px;
  display: flex;
  max-width: 400px;
}

.infoRow {
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
  justify-content: center;
  width: 100%;
  flex-direction: column;
}

.infoLabel {
  /* Body/Medium */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  /* identical to box height */
  text-align: center;

  color: #000000;
}

.infoDescription {
  margin-top: 36px;
}

.infoDesLabel {
  /* Tittle/Medium */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  text-align: center;

  color: #9093a5;
}

.desRow {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 36px;
  gap: 4px;
}

.infoValue {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;

  color: #000000;
}

.verifyLink {
  display: block;
  text-align: center;
  color: #000;
  text-decoration: none;
}

.buttonsContainer {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  margin-top: 32px;
  min-width: 360px;
}
