/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useEffect, useMemo, useState } from 'react'
import { BsCreditCard2Front } from 'react-icons/bs'
import { useCardTransactionsReport } from '@/hooks/reportes/useCardTransactionsReport'
import ReportFilters from '@/app/components/ReportFilters'
import DateFilter from '@/app/components/DateFilter'
import Input from '@/app/components/Input'
import Select from '@/app/components/Select'
import { toast } from 'react-toastify'
import { CardTransactionsReportParams } from '@/types/reports/types'
import { AdminBasic } from '@/types/admin/types'

interface CardTransactionsFiltersParams {
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
  admins: AdminBasic[]
}

function CardTransactionsFilters({
  isClient,
  selectedAdminId,
  onAdminChange,
  admins,
}: CardTransactionsFiltersParams) {
  const [dateClear<PERSON><PERSON>, setDateClearKey] = useState(0)
  // Estado de filtros con los campos requeridos
  const [filters, setFilters] = useState({
    adminId: selectedAdminId || undefined,
    pan: '',
    start: undefined as string | undefined,
    end: undefined as string | undefined,
  })

  const activeFilters = useMemo(() => {
    const list: string[] = []

    list.push(
      `Empresa: ${admins.find(a => a.id === filters.adminId)?.alias || 'Todas las empresas'}`
    )

    if (filters.pan) list.push(`PAN: ${filters.pan}`)
    if (filters.start && filters.end) {
      list.push(`Fecha: ${filters.start} – ${filters.end}`)
    }

    return list
  }, [filters, admins, isClient, selectedAdminId])

  /** Manejo genérico de filtros */
  const handleFilterChange = (name: keyof typeof filters, value: string | undefined) => {
    if (isClient && name === 'adminId') return
    setFilters(prev => ({ ...prev, [name]: value }))
    if (name === 'adminId') onAdminChange(value as string)
  }

  /** Reset de filtros y burbujas */
  const handleReset = () => {
    if (isClient) {
      onAdminChange(selectedAdminId)
      setFilters({
        adminId: selectedAdminId,
        pan: '',
        start: undefined,
        end: undefined,
      })
    } else {
      onAdminChange('')
      setFilters({
        adminId: undefined,
        pan: '',
        start: undefined,
        end: undefined,
      })
    }
    setDateClearKey(k => k + 1)
  }

  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      handleFilterChange('start', undefined)
      handleFilterChange('end', undefined)
      return
    }

    if (dates.length === 1) {
      handleFilterChange('start', dates[0])
      handleFilterChange('end', dates[0])
    } else if (dates.length === 2) {
      handleFilterChange('start', dates[0])
      handleFilterChange('end', dates[1])
    }
  }

  useEffect(() => {
    if (!selectedAdminId || admins.length === 0) return
    setFilters(prev => ({ ...prev, adminId: selectedAdminId || undefined }))
  }, [selectedAdminId, admins])

  return {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleReset,
    handleDateChange,

  }
}

interface CardTransactionsReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
  admins: AdminBasic[]
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
}

const CardTransactionsReport = ({
  admins,
  onLoadingChange,
  isClient,
  selectedAdminId,
  onAdminChange,
}: CardTransactionsReportProps) => {
  const { isLoading, loadingMessage, downloadReport } = useCardTransactionsReport()
  const {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleReset,
    handleDateChange,
  } = CardTransactionsFilters({
    isClient,
    selectedAdminId,
    onAdminChange,
    admins,
  })

  /** Descarga reporte */
  const handleDownload = async () => {
    // Validación obligatoria: fechas
    if (!filters.start || !filters.end) {
      toast.error('Debes seleccionar un rango de fechas para descargar el reporte.')
      return
    }

    // Validación PAN opcional
    if (filters.pan && filters.pan.length !== 16) {
      toast.error('El PAN debe tener exactamente 16 dígitos numéricos.')
      return
    }

    // Construir payload
    const params: CardTransactionsReportParams = {
      start: filters.start,
      end: filters.end,
    }

    if (filters.adminId) params.adminId = filters.adminId
    if (filters.pan) params.pan = filters.pan
    if (isClient) params.adminId = selectedAdminId

    await downloadReport(params)
  }

  /** Loading global */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onSubmit={(e) => e.preventDefault()}
      onReset={handleReset}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Transacciones por Tarjeta"
      icon={<BsCreditCard2Front />}
      downloadOptions={{
        onlyExcel: true,
        onExcelDownload: handleDownload,
      }}
    >
      <Select
        name="adminId"
        label="Empresa"
        value={filters.adminId || ''}
        onChange={e => handleFilterChange('adminId', e.target.value)}
        disabled={isClient}
      >
        <option value="">Todas las empresas</option>
        {admins.map(admin => (
          <option key={admin.id} value={admin.id}>
            {admin.alias || admin.companyName}
          </option>
        ))}
      </Select>

      <DateFilter
        mode="range"
        onDateChange={handleDateChange}
        label="Rango de fechas"
        placeholder="Selecciona el rango de fechas"
        clearTrigger={dateClearKey}
      />

      <Input
        type="text"
        name="pan"
        label="PAN (opcional)"
        placeholder="Número de tarjeta (16 dígitos)"
        value={filters.pan || ''}
        minLength={16}
        maxLength={16}
        onChange={e => {
          const numericValue = e.target.value.replace(/\D/g, '') // solo números
          handleFilterChange('pan', numericValue)
        }}
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault()
          }
        }}
      />
    </ReportFilters>
  )
}

export default CardTransactionsReport
