.modal {
  position: relative;
  padding: 0px 24px;
  overflow: hidden;
}

.closeButton {
  position: absolute;
  top: 22px;
  right: 22px;
  background: none;
  border: none;
  font-family: Inter;
  font-size: 20px;
  cursor: pointer;
  z-index: 9999;
}

.image {
  position: absolute;
  left: 0px;
  top: 0%;
  transform: translateY(-50%);
  z-index: 1;
}

.content {
  padding-left: 40px;
  padding-right: 12px;
  position: relative;
  z-index: 2;
  gap: 4px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.title {
  /* Text */
  width: 399px;
  /* height: 56px; */

  /* Text lg/Semibold */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-family: Inter;
  font-size: 18px;
  line-height: 28px;
  /* or 156% */

  color: #000000;

  /* Inside auto layout */
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.description {
  /* Supporting text */

  width: 399px;
  /* height: 20px; */

  /* Text sm/Regular */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */

  color: #6f7280;

  /* Inside auto layout */
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
  white-space: pre-line;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  /* margin-top: 20px; */
}

.btn {
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  max-width: 100%;
  height: 48px;
  width: 100%;
  color: white;
  padding: 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  margin-top: 2rem;
  transition:
    all 0.4s ease,
    background 0.4s ease;
  position: relative;
  z-index: 2;
}

.bgIcon {
  position: absolute;
  left: -118px;
  top: 50px;
  transform: translateY(-50%);
  z-index: 1;
}
