'use client'
import React from 'react'
import styles from './styles/Button.module.css'

type ButtonProps = {
  text: string
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void // Función que se ejecuta al hacer clic en el botón
  className?: string // Permite añadir clases adicionales
  fullWidth?: boolean // Permite hacer el botón de ancho completo
  disabled?: boolean // Permite deshabilitar el botón
  type?: 'button' | 'submit' // Tipo de botón, por defecto es "button"
}

const Button: React.FC<ButtonProps> = ({ text, onClick, className, fullWidth, disabled, type }) => {
  return (
    <button
      className={`${styles.button} ${fullWidth ? styles.fullWidth : ''} ${className || ''}`}
      onClick={onClick}
      disabled={disabled}
      type={type}
    >
      {text}
    </button>
  )
}

export default Button
