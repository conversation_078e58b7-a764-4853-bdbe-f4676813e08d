import AlertModalBase from '../AlertModalBase'
import Button from '../../Button'
import ButtonOutlined from '../../ButtonOutlined'
import styles from '../../styles/DispersionModal.module.css'
import InputDigit from '../../InputDigit'
import { useState, useRef, useEffect } from 'react'
import Loader from '../../loader/Loader'

type Props = {
  open: boolean
  error: boolean
  loading: boolean
  onClose: () => void
  onResendCode: () => void
  onContinue: (value: string) => void
  accountCard?: string
}

const ModalOTPTransfer: React.FC<Props> = ({
  open,
  error,
  loading,
  onClose,
  onResendCode,
  onContinue,
  accountCard
}) => {
  const [code, setCode] = useState(['', '', '', ''])
  const inputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ]

  // Focus first input when modal opens
  useEffect(() => {
    if (open && !loading && inputRefs[0].current) {
      inputRefs[0].current.focus()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, loading])

  const handleCodeChange = (value: string, index: number) => {
    const newCode = [...code]
    newCode[index] = value
    setCode(newCode)

    // Auto-focus next input after entering a digit
    if (value && index < 3) {
      inputRefs[index + 1].current?.focus()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    // Navigate with arrow keys
    if (e.key === 'ArrowRight' && index < 3) {
      inputRefs[index + 1].current?.focus()
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs[index - 1].current?.focus()
    }
    // Handle backspace to go to previous input when current is empty
    else if (e.key === 'Backspace' && index > 0 && code[index] === '') {
      inputRefs[index - 1].current?.focus()
    }
  }

  return (
    <AlertModalBase
      type="warning"
      title={'Realizar transferencia de saldo a la cuenta '+ (accountCard || '')}
      message="Por favor, ingrese el código de verificación que se envió al correo electrónico registrado."
      open={open}
      onClose={onClose}
    >
      {loading && (
        <div className={styles.codeContainer} style={{ margin: '24px' }}>
          <Loader />
        </div>
      )}
      {!loading && (
        <>
          <div className={styles.codeWrapper}>
            <div className={styles.codeContainer}>
              <InputDigit
                value={code[0]}
                error={error}
                onChange={value => handleCodeChange(value, 0)}
                onKeyDown={e => handleKeyDown(e, 0)}
                inputRef={inputRefs[0]}
                autoFocus={true}
              />
              <InputDigit
                value={code[1]}
                error={error}
                onChange={value => handleCodeChange(value, 1)}
                onKeyDown={e => handleKeyDown(e, 1)}
                inputRef={inputRefs[1]}
              />
              <InputDigit
                value={code[2]}
                error={error}
                onChange={value => handleCodeChange(value, 2)}
                onKeyDown={e => handleKeyDown(e, 2)}
                inputRef={inputRefs[2]}
              />
              <InputDigit
                value={code[3]}
                error={error}
                onChange={value => handleCodeChange(value, 3)}
                onKeyDown={e => handleKeyDown(e, 3)}
                inputRef={inputRefs[3]}
              />
            </div>
            {error && <p className={styles.textError}>Código incorrecto</p>}
          </div>
          <div className={styles.footer}>
            <Button
              text="Continuar"
              fullWidth
              disabled={code.some(digit => digit === '')}
              onClick={() => onContinue(code.join(''))}
            />
            <p className={styles.textEmail}>¿No has recibido el correo?</p>
            <ButtonOutlined text="Reenviar código" fullWidth onClick={onResendCode} />
          </div>
        </>
      )}
    </AlertModalBase>
  )
}

export default ModalOTPTransfer
