.card {
  box-sizing: border-box;
}
.card {
  height: 488px;
  /* height: auto; */
  box-shadow: 0 2px 10px rgba(76, 78, 100, 0.22);
  border-radius: 10px;
  background-color: #fff;
  max-width: 480px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 24px 28px;
  gap: 24px;
  line-height: normal;
  letter-spacing: normal;
}
.passwordWrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.hintCheckboxesParent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
  width: 211px;
  margin-top: 4px;
}
.hintCheckboxes {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 7px;
}
.iconCheck {
  height: 16px;
  width: 16px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}
.hintText {
  position: relative;
  font-size: 10px;
  letter-spacing: 0.4px;
  line-height: 16px;
  font-family: Inter;
  color: #606572;
  text-align: left;
}
.inputGroupPassword {
  text-align: left;
}
.inputGroupPasswordLabel {
  display: block;
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 16.94px;
  color: #000;
  background-color: white;
  margin-bottom: 6px;
}
.inputGroupPassword input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #d0d5dd;
  border-radius: 10px;
  font-weight: 400;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  color: #000000;
  background-color: #fff;
}

.inputGroupPassword input:focus {
  outline: none;
  border-color: #e9d688; /* Color dorado */
  box-shadow: 0 0 5px rgba(180, 145, 95, 0.5);
}
.invalidInputPass input {
  width: 100%;
  padding: 0.8rem;
  border-radius: 10px;
  font-weight: 400;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  color: #000000;
  background-color: #fff;
  border: 1px solid red;
  border-radius: 10px;
  outline: none;
}
.eyeButton {
  background: none;
  border: none;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  width: 16px;
  height: 16px;
  color: #000000;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.eyeButton:focus {
  outline: none;
}

.eyeButton svg {
  width: 16px;
  height: 16px;
}
.errorText {
  color: red;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}
