import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import { hasRouteAccess, getDefaultRouteForRole } from '@/utils/roleNavigation'

/**
 * Hook para verificar permisos de ruta y redirige automáticamente
 * si el usuario no tiene acceso a la página actual
 * 
 * @param options - Opciones de configuración
 * @param options.enabled - Si está habilitado el hook (por defecto true)
 * @param options.redirectOnRoleChange - Si debe redirigir cuando cambia el rol (por defecto true)
 */
export const useRoleBasedRedirect = (options: {
  enabled?: boolean
  redirectOnRoleChange?: boolean
} = {}) => {
  const { enabled = true, redirectOnRoleChange = true } = options
  
  const router = useRouter()
  const pathname = usePathname()
  const { getUserRoleName } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()
  
  const currentRoleName = getUserRoleName()

  useEffect(() => {
    if (!enabled || !currentRoleName) {
      return
    }

    // Verificar si el usuario tiene acceso a la ruta actual
    const hasAccess = hasRouteAccess(currentRoleName, pathname)
    
    if (!hasAccess) {
      // Si no tiene acceso, redirigir a la primera ruta permitida para su rol
      const defaultRoute = getDefaultRouteForRole(currentRoleName)
      
      router.push(defaultRoute)
    }
  }, [enabled, currentRoleName, pathname, router, selectedAdminIndex, redirectOnRoleChange])

  return {
    currentRoleName,
    hasAccess: hasRouteAccess(currentRoleName, pathname),
    defaultRoute: getDefaultRouteForRole(currentRoleName)
  }
}

/**
 * Hook simplificado que solo verifica si el usuario actual tiene acceso a la ruta
 * sin realizar redirección automática
 */
export const useRouteAccess = () => {
  const pathname = usePathname()
  const { getUserRoleName } = useAuthStore()
  
  const currentRoleName = getUserRoleName()
  const hasAccess = hasRouteAccess(currentRoleName, pathname)
  
  return {
    currentRoleName,
    hasAccess,
    pathname
  }
}
