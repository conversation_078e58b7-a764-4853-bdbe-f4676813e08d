[{"profile": "consultant", "companyName": "FinTech Corp", "alias": "FinTech", "rfc": "ABC123456T12", "balance": "$20,000", "responsibleName": "<PERSON>", "state": "Nuevo León", "city": "Monterrey", "registrationDate": "2024-05-15", "assignedCards": "25", "phone": "8112345678", "email": "<EMAIL>", "street": "Av. Constitución", "zipCode": "64000", "exteriorNumber": "120", "neighborhood": "Centro", "password": "securePass123", "confirmPassword": "securePass123", "files": {"act": {"name": "fintech_acta.pdf", "size": 7340032, "type": "application/pdf", "lastModified": *************}, "constancy": {"name": "fintech_constancia.pdf", "size": 5242880, "type": "application/pdf", "lastModified": *************}, "file": null, "other": null}, "commissionPercentage": "2", "commissionAmount": "100", "commissionCardFunding": "3", "commissionAmbassador": "Test", "groupAccounts": [{"alias": "FinTech Monterrey", "responsibleName": "<PERSON>", "profileType": "Administrador cliente", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "ABC123456T12", "registrationDate": "2023-01-15", "extraCompanies": ["Bimbo", "Oxxo"], "interbankClabe": "123456789012345678"}, {"alias": "FinTech Guadalajara", "responsibleName": "<PERSON>", "profileType": "Tesorero", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "DEF789012H34", "registrationDate": "2023-02-20", "extraCompanies": ["Amazon", "Google", "Microsoft"], "interbankClabe": "234567890123456789"}, {"alias": "FinTech Puebla", "responsibleName": "<PERSON>", "profileType": "Gestor de tarjetas", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "GHI345678J56", "registrationDate": "2023-03-25", "extraCompanies": ["Tesla", "Walmart", "Pepsi", "Nestlé"], "interbankClabe": "345678901234567890"}]}, {"profile": "convenia", "companyName": "PagoFácil Ltd", "alias": "PagoFácil", "rfc": "DEF789012H34", "balance": "$30,000", "responsibleName": "<PERSON>", "state": "Jalisco", "city": "Guadalajara", "registrationDate": "2023-08-20", "assignedCards": "40", "phone": "**********", "email": "<EMAIL>", "street": "Calle Vallarta", "zipCode": "44160", "exteriorNumber": "300", "neighborhood": "Americana", "password": "passPagoFacil", "confirmPassword": "passPagoFacil", "files": {"act": {"name": "pago_acta.pdf", "size": 2097152, "type": "application/pdf", "lastModified": *************}, "constancy": null, "file": null, "other": null}, "commissionPercentage": "2", "commissionAmount": "100", "commissionCardFunding": "3", "commissionAmbassador": "Test", "groupAccounts": [{"alias": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "responsibleName": "<PERSON>", "profileType": "Administrador cliente", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "JKL901234K78", "registrationDate": "2023-04-10", "extraCompanies": ["Samsung"], "interbankClabe": "******************"}, {"alias": "PagoFácil CDMX", "responsibleName": "<PERSON>", "profileType": "Tesorero", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "MNO567890L90", "registrationDate": "2023-05-15", "extraCompanies": ["Ford", "Chevrolet", "Toyota"], "interbankClabe": "567890123456789012"}, {"alias": "PagoFácil GDL", "responsibleName": "<PERSON>", "profileType": "Gestor de tarjetas", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "PQR123456M12", "registrationDate": "2023-06-20", "extraCompanies": ["Adidas", "Nike", "<PERSON><PERSON>", "Reebok", "Under Armour"], "interbankClabe": "678901234567890123"}]}, {"profile": "convenia", "companyName": "FinTech Innovations S.A", "alias": "FinTech", "rfc": "GHI345678J56", "balance": "$40,000", "responsibleName": "<PERSON>", "state": "CDMX", "city": "CDMX", "registrationDate": "2024-07-12", "assignedCards": "50", "phone": "**********", "email": "<EMAIL>", "street": "Avenida Reforma", "zipCode": "11000", "exteriorNumber": "150", "neighborhood": "Polanco", "password": "pass123", "confirmPassword": "pass123", "files": {"act": null, "constancy": null, "file": null, "other": null}, "commissionPercentage": "2", "commissionAmount": "100", "commissionCardFunding": "3", "commissionAmbassador": "Test", "groupAccounts": [{"alias": "FinTech Innovations S.A", "responsibleName": "<PERSON>", "profileType": "<PERSON><PERSON>", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "ABC123456T12", "registrationDate": "2023-01-15", "extraCompanies": ["Coca-Cola", "Pepsi"], "interbankClabe": "123456789012345678"}, {"alias": "FinTech Innovations Matriz", "responsibleName": "<PERSON>", "profileType": "Tesorero", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "DEF789012H34", "registrationDate": "2023-02-20", "extraCompanies": ["Sony", "LG", "Panasonic"], "interbankClabe": "234567890123456789"}, {"alias": "FinTech Innovations principal", "responsibleName": "<PERSON>", "profileType": "Administrador cliente", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "GHI345678J56", "registrationDate": "2023-03-25", "extraCompanies": ["Apple", "Microsoft", "Google", "Amazon"], "interbankClabe": "345678901234567890"}, {"alias": "FinTech Innovations CDMX", "responsibleName": "<PERSON>", "profileType": "Gestor de tarjetas", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "JKL901234K78", "registrationDate": "2023-04-10", "extraCompanies": ["Company G", "Company H"], "interbankClabe": "******************"}, {"alias": "FinTech Innovations GDL", "responsibleName": "<PERSON>", "profileType": "<PERSON><PERSON><PERSON>, Gestor de tarjetas", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "MNO567890L90", "registrationDate": "2023-05-15", "extraCompanies": ["Company I", "Company J"], "interbankClabe": "567890123456789012"}]}, {"profile": "convenia", "companyName": "PagoFácil Ltd", "alias": "PagoFácil", "rfc": "JKL901234K78", "balance": "$50,000", "responsibleName": "<PERSON>", "state": "CDMX", "city": "CDMX", "registrationDate": "2024-07-12", "assignedCards": "35", "phone": "**********", "email": "<EMAIL>", "street": "Calle <PERSON>", "zipCode": "06000", "exteriorNumber": "25", "neighborhood": "Centro", "password": "secure123", "confirmPassword": "secure123", "files": {"act": null, "constancy": null, "file": null, "other": null}, "commissionPercentage": "2", "commissionAmount": "100", "commissionCardFunding": "3", "commissionAmbassador": "Test", "groupAccounts": [{"alias": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "responsibleName": "<PERSON>", "profileType": "Administrador cliente", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "PQR123456M12", "registrationDate": "2023-06-20", "extraCompanies": ["Company K", "Company L"], "interbankClabe": "678901234567890123"}, {"alias": "PagoFácil CDMX", "responsibleName": "<PERSON>", "profileType": "Tesorero", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "ABC123456T12", "registrationDate": "2023-01-15", "extraCompanies": ["Company A", "Company B"], "interbankClabe": "123456789012345678"}, {"alias": "PagoFácil GDL", "responsibleName": "<PERSON>", "profileType": "Gestor de tarjetas", "balance": "$5,000", "conveniaAccount": "0123456", "rfc": "DEF789012H34", "registrationDate": "2023-02-20", "extraCompanies": ["Company C", "Company D"], "interbankClabe": "234567890123456789"}]}, {"profile": "consultant", "companyName": "DigitalWallet Solutions", "alias": "DigiWallet", "rfc": "MNO567890L90", "balance": "$60,000", "responsibleName": "<PERSON>", "state": "CDMX", "city": "CDMX", "registrationDate": "2024-07-12", "assignedCards": "42", "phone": "**********", "email": "<EMAIL>", "street": "Calle Insurgentes", "zipCode": "03100", "exteriorNumber": "12", "neighborhood": "Del Valle", "password": "walletpass", "confirmPassword": "walletpass", "files": {"act": null, "constancy": null, "file": null, "other": null}, "commissionPercentage": "2", "commissionAmount": "100", "commissionCardFunding": "3", "commissionAmbassador": "Test", "groupAccounts": [{"alias": "DigiWallet Central", "responsibleName": "<PERSON>", "profileType": "Administrador cliente", "balance": "$7,000", "conveniaAccount": "DW001", "rfc": "GHI345678J56", "registrationDate": "2023-03-25", "extraCompanies": ["Company E", "Company F"], "interbankClabe": "345678901234567890"}, {"alias": "DigiWallet Norte", "responsibleName": "<PERSON>", "profileType": "Tesorero", "balance": "$6,500", "conveniaAccount": "DW002", "rfc": "JKL901234K78", "registrationDate": "2023-04-10", "extraCompanies": ["Company G", "Company H"], "interbankClabe": "******************"}, {"alias": "DigiWallet Sur", "responsibleName": "<PERSON>", "profileType": "Gestor de tarjetas", "balance": "$5,300", "conveniaAccount": "DW003", "rfc": "MNO567890L90", "registrationDate": "2023-05-15", "extraCompanies": ["Company I", "Company J"], "interbankClabe": "567890123456789012"}]}, {"profile": "convenia", "companyName": "CloudFinances", "alias": "CloudFin", "rfc": "PQR123456M12", "balance": "$70,000", "responsibleName": "<PERSON><PERSON><PERSON>", "state": "CDMX", "city": "CDMX", "registrationDate": "2024-05-15", "assignedCards": "32", "phone": "**********", "email": "<EMAIL>", "street": "Calle de la Nube", "zipCode": "04560", "exteriorNumber": "3", "neighborhood": "Cielo", "password": "cloudyday", "confirmPassword": "cloudyday", "files": {"act": null, "constancy": null, "file": null, "other": null}, "commissionPercentage": "2", "commissionAmount": "100", "commissionCardFunding": "3", "commissionAmbassador": "Test", "groupAccounts": [{"alias": "<PERSON><PERSON><PERSON>", "responsibleName": "<PERSON><PERSON><PERSON>", "profileType": "Administrador cliente", "balance": "$5,500", "conveniaAccount": "CLOUD001", "rfc": "PQR123456M12", "registrationDate": "2023-06-20", "extraCompanies": ["Company K", "Company L"], "interbankClabe": "678901234567890123"}, {"alias": "CloudFin Guadalajara", "responsibleName": "<PERSON><PERSON><PERSON>", "profileType": "Tesorero", "balance": "$4,800", "conveniaAccount": "CLOUD002", "rfc": "ABC123456T12", "registrationDate": "2023-01-15", "extraCompanies": ["Company A", "Company B"], "interbankClabe": "123456789012345678"}, {"alias": "<PERSON><PERSON><PERSON>", "responsibleName": "<PERSON><PERSON><PERSON>", "profileType": "Gestor de tarjetas", "balance": "$6,200", "conveniaAccount": "CLOUD003", "rfc": "DEF789012H34", "registrationDate": "2023-02-20", "extraCompanies": ["Company C", "Company D"], "interbankClabe": "234567890123456789"}]}]