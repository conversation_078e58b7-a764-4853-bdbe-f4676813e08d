[{"name": "<PERSON>", "email": "<EMAIL>", "accountNumber": "**** **** **** 2009", "companyAlias": "Fintech", "accountBalance": "$5,000.00", "speiIn": false, "speiOut": false, "status": false, "registrationDate": "2025-03-12", "phone": "+52 55 1234 5678", "membershipNumber": "AFI-00123", "accountNumberConvenia": "********", "accountNumberTransfer": "********", "cards": [{"cardNumber": "**** **** **** 1234", "cable": "CLABE********901234", "expirationDate": "12/25", "cvv": "123", "status": true}, {"cardNumber": "**** **** **** 5678", "expirationDate": "11/24", "cable": "CLABE12346678901234", "cvv": "456", "status": false}]}, {"name": "<PERSON>", "email": "<EMAIL>", "accountNumber": "**** **** **** 2008", "companyAlias": "Fintech", "accountBalance": "$5,000.00", "speiIn": false, "speiOut": false, "status": false, "registrationDate": "2025-03-12", "phone": "+52 55 1234 5678", "membershipNumber": "AFI-00123", "accountNumberConvenia": "********", "accountNumberTransfer": "********", "cards": [{"cardNumber": "**** **** **** 2345", "cable": "CLABE********012345", "expirationDate": "10/26", "cvv": "234", "status": true}, {"cardNumber": "**** **** **** 6789", "cable": "CLABE23457789012345", "expirationDate": "09/25", "cvv": "567", "status": false}]}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "accountNumber": "**** **** **** 2007", "companyAlias": "Fintech", "accountBalance": "$5,000.00", "speiIn": false, "speiOut": false, "status": false, "registrationDate": "2025-03-13", "phone": "+52 55 2345 6789", "membershipNumber": "AFI-00456", "accountNumberConvenia": "********", "accountNumberTransfer": "********", "cards": [{"cardNumber": "**** **** **** 3456", "cable": "CLABE********123456", "expirationDate": "08/27", "cvv": "345", "status": true}, {"cardNumber": "**** **** **** 7890", "cable": "CLABE34568890123456", "expirationDate": "07/26", "cvv": "678", "status": false}]}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "accountNumber": "**** **** **** 2006", "companyAlias": "Fintech", "accountBalance": "$5,000.00", "speiIn": false, "speiOut": false, "status": false, "registrationDate": "2025-03-14", "phone": "+52 55 3456 7890", "membershipNumber": "AFI-00389", "accountNumberConvenia": "********", "accountNumberTransfer": "********", "cards": [{"cardNumber": "**** **** **** 4567", "cable": "CLABE********234567", "expirationDate": "06/28", "cvv": "456", "status": true}, {"cardNumber": "**** **** **** 8901", "cable": "CLABE45679901234567", "expirationDate": "05/27", "cvv": "789", "status": false}]}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "accountNumber": "**** **** **** 2005", "companyAlias": "Fintech", "accountBalance": "$5,000.00", "speiIn": false, "speiOut": false, "status": false, "registrationDate": "2025-03-15", "phone": "+52 55 4567 8901", "membershipNumber": "AFI-00999", "accountNumberConvenia": "********", "accountNumberTransfer": "********", "cards": [{"cardNumber": "**** **** **** 5678", "cable": "CLABE567890********", "expirationDate": "04/29", "cvv": "567", "status": true}, {"cardNumber": "**** **** **** 9012", "cable": "CLABE567800********", "expirationDate": "03/28", "cvv": "890", "status": false}]}]