/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useRef, useEffect } from 'react'
import AlertModalBase from '../AlertModalBase'
import Button from '../../Button'
import ButtonOutlined from '../../ButtonOutlined'
import InputDigit from '../../InputDigit'
import Loader from '../../loader/Loader'
import styles from '../../styles/DispersionModal.module.css'

type Props = {
  open: boolean
  error: boolean
  loading: boolean
  onClose: () => void
  onResendCode: () => void
  onContinue: (value: string) => void
}

const ModalOTPRegister: React.FC<Props> = ({
  open,
  error,
  loading,
  onClose,
  onResendCode,
  onContinue,
}) => {
  const [code, setCode] = useState(['', '', '', ''])
  const inputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ]

  useEffect(() => {
    if (open && !loading && inputRefs[0].current) {
      inputRefs[0].current.focus()
    }
  }, [open, loading])

  const clearAll = () => setCode(['', '', '', ''])

  const handleClose = () => {
    clearAll()
    onClose()
  }

  const handleContinue = () => {
    onContinue(code.join(''))
    clearAll()
  }

  const handleCodeChange = (value: string, index: number) => {
    const newCode = [...code]
    newCode[index] = value
    setCode(newCode)

    if (value && index < 3) inputRefs[index + 1].current?.focus()
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'ArrowRight' && index < 3) inputRefs[index + 1].current?.focus()
    if (e.key === 'ArrowLeft'  && index > 0) inputRefs[index - 1].current?.focus()
    if (e.key === 'Backspace'  && index > 0 && code[index] === '')
      inputRefs[index - 1].current?.focus()
  }

  return (
    <AlertModalBase
      type="warning"
      title="Crear usuario"
      message="Ingresa el código de verificación enviado a tu correo electrónico para confirmar la creación del usuario."
      open={open}
      onClose={handleClose}
    >
      {loading && (
        <div className={styles.codeContainer} style={{ margin: 24 }}>
          <Loader />
        </div>
      )}

      {!loading && (
        <>
          <div className={styles.codeWrapper}>
            <div className={styles.codeContainer}>
              {code.map((digit, idx) => (
                <InputDigit
                  key={idx}
                  value={digit}
                  error={error}
                  onChange={v => handleCodeChange(v, idx)}
                  onKeyDown={e => handleKeyDown(e, idx)}
                  inputRef={inputRefs[idx]}
                  autoFocus={idx === 0}
                />
              ))}
            </div>
            {error && <p className={styles.textError}>Código incorrecto</p>}
          </div>

          <div className={styles.footer}>
            <Button
              text="Confirmar"
              fullWidth
              disabled={code.some(d => d === '') || loading}
              onClick={handleContinue}
            />
            <p className={styles.textEmail}>¿No has recibido el correo?</p>
            <ButtonOutlined text="Reenviar código" fullWidth onClick={onResendCode} />
          </div>
        </>
      )}
    </AlertModalBase>
  )
}

export default ModalOTPRegister
