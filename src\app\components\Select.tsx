import React, { SelectHTMLAttributes } from 'react'
import styles from './styles/Select.module.css'

interface SelectProps extends SelectHTMLAttributes<HTMLSelectElement> {
  name: string
  className?: string
  children: React.ReactNode
  label?: string
  isError?: boolean
  errorText?: string
}

const Select: React.FC<SelectProps> = ({ children, label, isError, errorText, className, ...props }) => {
  return (
    <div className={`${styles.selectWrapper} ${className}`}>
      {label && <label>{label}</label>}
      <select className={`${styles.select} ${isError ? styles.error : ''}`} {...props}>
        {children}
      </select>
      {isError && errorText && <p className={styles.errorText}>{errorText}</p>}
    </div>
  )
}

export default Select
