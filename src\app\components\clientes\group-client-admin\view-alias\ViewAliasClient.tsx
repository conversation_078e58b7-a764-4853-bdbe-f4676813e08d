import React from 'react'
import AliasViewForm from '../../components/AliasViewForm'
import MultiCompanySelectReadOnly from '../../components/MultiCompanySelectReadOnly'
import styles from '../../../styles/EditAliasClient.module.css'
import AccountTransactionsTable from '@/app/components/cuentas/components/AccountTransactionsTable'
import { AdminDetail } from '@/types/admin/types'

interface Props {
  client: AdminDetail
}

const ViewAliasClient: React.FC<Props> = ({ client }) => {
  return (
    <div className={styles.formContainer}>
      <AliasViewForm client={client} />
      <div className={styles.formGroup}>
        <p className={styles.formGroupTitle}>Esta cuenta administra estas empresas</p>
        <MultiCompanySelectReadOnly companies={client.manager.enterprises.map(e => e.name)} />
      </div>
      <div className={styles.content}>
        <p className={styles.title}>Movimientos de {client.alias}</p>
        {/* mandar el email  */}
        {client.manager.email ? (
          <AccountTransactionsTable email={client.manager.email} aliasEmpresa={client.alias} />
        ) : (
          <p>El cliente no tiene correo electrónico disponible para mostrar transacciones.</p>
        )}
      </div>
    </div>
  )
}

export default ViewAliasClient
