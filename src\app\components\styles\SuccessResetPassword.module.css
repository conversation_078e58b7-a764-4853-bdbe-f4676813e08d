.iconUnlocked {
  height: 136.3px;
  width: 116.2px;
  position: relative;
}
.iconUnlockedContainer {
  align-self: stretch;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 1px 15px;
}
.iconCheck {
  height: 50px;
  width: 50px;
  position: relative;
}

.iconCheckContainer {
  align-self: stretch;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
  padding: 4px;
}
.title,
.description {
  align-self: stretch;
  position: relative;
}
.title {
  margin: 0;
  font-size: 32px;
}
.description {
  font-size: 16px;
}
.successMessageContainer {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.cardContent,
.illustrationWrapper {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.illustrationWrapper {
  gap: 36px;
}
.cardContent {
  text-align: center;
  font-size: 32px;
  color: #000;
  font-family: Inter;
}
.card {
  width: 480px;
  box-shadow: 0 2px 10px rgba(76, 78, 100, 0.22);
  border-radius: 10px;
  background-color: #fff;
  max-width: 480px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 24px 28px;
  gap: 24px;
  line-height: normal;
  letter-spacing: normal;
}
.button {
  align-self: stretch;
  height: 48px;
  padding: 12px;
}
@media screen and (max-width: 424px) {
  .illustrationWrapper {
    gap: 18px;
  }
  .cardContent {
    gap: 20px;
  }
}
