.popupContainer {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  margin-top: 10px;
  display: flex;
  width: 144px;
  padding: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 12px;
  border-radius: 10px;
  background: #fff;
  z-index: 10;
  /* position: absolute;
    top: 100%;
    left: 82%; */
}

.popupContainer hr {
  width: 100%;
  height: 1px;
  background-color: #e6e8f4;
  margin: 0;
}

.fileOption {
  display: flex;
  align-items: center;
  align-self: stretch;
  cursor: pointer;
}

.fileOption:hover {
  background: #f9fafb;
  border-radius: 8px;
}

.fileLabel {
  display: flex;
  width: 71px;
  height: 28px;
  flex-direction: column;
  justify-content: center;
  color: #000;
  text-align: center;
  font-family: Inter;
  font-size: 10px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
