import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

const ModalResponseToUserSuccess: React.FC<BaseModalProps> = ({ open, onClose }) => {
  return (
    <AlertModal
      open={open}
      type="success"
      title="Mensaje enviado al usuario"
      onClose={onClose}
      onPressBtn={() => null}
    />
  )
}

export default ModalResponseToUserSuccess
