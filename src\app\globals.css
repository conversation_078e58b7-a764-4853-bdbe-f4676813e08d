@import 'react-phone-input-2/lib/style.css';

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Variable para la fuente Inter */
  --font-inter: 'Inter', Arial, Helvetica, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;

  /* Ajuste de scrollbar compatibilidad entre navegadores */
  /* https://developer-mozilla-org.translate.goog/en-US/docs/Web/CSS/scrollbar-color?_x_tr_sl=en&_x_tr_tl=es&_x_tr_hl=es&_x_tr_pto=tc */

  /* scrollbar-width: thin;
  scrollbar-color: #0a0a0a transparent; */
}

/* width */
::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #0a0a0a;
}

::-webkit-scrollbar-thumb:hover {
  border-radius: 5px;
  background: #0a0a0ab3;
}

body {
  color: var(--foreground);
  background: var(--background);

  /* Cambia la fuente a Inter */
  font-family: var(--font-inter);

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}
