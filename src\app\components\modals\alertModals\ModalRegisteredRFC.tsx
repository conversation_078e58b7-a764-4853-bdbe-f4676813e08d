import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'
import styles from '../../styles/ModalRegisteredRFC.module.css'

interface ModalRegisteredRFCProps extends BaseModalProps {
  companyName: string
  alias?: string
}

const ModalRegisteredRFC: React.FC<ModalRegisteredRFCProps> = ({
  open,
  onClose,
  companyName,
  alias,
}) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title="Este RFC ya esta registrado"
      message="Datos de la empresa"
      textBtn="Continuar"
      renderMessage={() => (
        <div className={styles.modalContainer}>
          <div className={styles.companyInfo}>
            <div className={styles.inputGroup}>
              <p className={styles.label}>Nombre de la compañía</p>
              <input
                className={styles.input}
                placeholder="Escribe aquí"
                value={companyName}
                readOnly
              />
            </div>
            <div className={styles.inputGroup}>
              <p className={styles.label}>Alias</p>
              <input className={styles.input} placeholder="Escribe aquí" value={alias} readOnly />
            </div>
          </div>
          <p className={styles.confirmationText}>
            ¿Estás seguro que deseas añadir <strong>{companyName}</strong> a este RFC?
          </p>
        </div>
      )}
      onClose={onClose}
      onPressBtn={onClose}
    />
  )
}

export default ModalRegisteredRFC
