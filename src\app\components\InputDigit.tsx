import styles from './styles/InputDigit.module.css'
import { useRef, useEffect } from 'react'

type Props = {
  value: string
  error: boolean
  onChange: (value: string) => void
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
  autoFocus?: boolean
  inputRef?: React.RefObject<HTMLInputElement>
}

const InputDigit: React.FC<Props> = ({
  value,
  error,
  onChange,
  onKeyDown,
  autoFocus,
  inputRef: externalRef,
}) => {
  const internalRef = useRef<HTMLInputElement>(null)
  const ref = externalRef || internalRef

  useEffect(() => {
    if (autoFocus && ref.current) {
      ref.current.focus()
    }
  }, [autoFocus, ref])

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (!/^\d*$/.test(value)) {
      e.target.value = value.replace(/[^\d]/g, '')
    }
    onChange(e.target.value)
  }

  return (
    <div
      className={`${styles.wrapper} ${value ? styles.hasContent : ''} ${error ? styles.error : ''}`}
    >
      <input
        ref={ref}
        type="text"
        maxLength={1}
        className={`${styles.wrapper} ${value ? styles.hasContent : ''} ${error ? styles.error : ''}`}
        onInput={handleInput}
        onKeyDown={onKeyDown}
        inputMode="numeric"
        pattern="\d*"
        placeholder="0"
        value={value}
        autoFocus={autoFocus}
      />
    </div>
  )
}

export default InputDigit
