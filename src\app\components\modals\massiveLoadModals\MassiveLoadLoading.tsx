import React from 'react'
import AlertModal from '../AlertModal'
import Loader from '../../loader/Loader'

type MassiveLoadLoadingProps = {
  open: boolean
  title: string
}

const MassiveLoadLoading: React.FC<MassiveLoadLoadingProps> = ({ open, title }) => {
  return (
    <AlertModal
      type="warning"
      title={title}
      message="Se están cargando los datos, esta acción puede tomar unos minutos."
      open={open}
      textBtn=""
      renderMessage={() => (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            margin: '20px',
          }}
        >
          <Loader size={100} />
        </div>
      )}
    />
  )
}

export default MassiveLoadLoading
