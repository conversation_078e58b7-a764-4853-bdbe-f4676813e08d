'use client'
import styles from '../../styles/CommissionsTable.module.css'
import ButtonGroup from './ButtonGroup'
import Pagination from '../../Pagination'
import { CommissionTransfer } from '@/types/commissions/types'
import { formatCurrency } from '@/utils/formatters'
import { AiOutlineSortAscending, AiOutlineSortDescending } from 'react-icons/ai'
import { PiHandDeposit } from "react-icons/pi";
import { HiOutlineArrowDownOnSquareStack } from "react-icons/hi2";

interface CommissionsTableProps {
  commissions: CommissionTransfer[]
  filter: {
    sort: string
    transfer_way: string
    setFilter: (filter: { sort: string; transfer_way: string }) => void
  }
  pagination: {
    totalPages: number
    currentPage: number
    setCurrentPage: (page: number) => void
  }
}

const CommissionsTable = ({ commissions, pagination, filter }: CommissionsTableProps) => {

  const transferWays = {
    "IN": 'Cuenta origen',
    "OUT": 'Cuenta destino'
  }

  const key = transferWays[filter.transfer_way as keyof typeof transferWays]

  const handlePrevPage = () => {
    if (pagination.currentPage > 1) pagination.setCurrentPage(pagination.currentPage - 1)
  }

  const handleNextPage = () => {
    if (pagination.currentPage < pagination.totalPages) pagination.setCurrentPage(pagination.currentPage + 1)
  }

  return (
    <>
      <div className={styles.header}>
        <section className={styles.filterContainer}>
          <ButtonGroup
            options={[{title:'ASC', icon: AiOutlineSortAscending }, { title: 'DESC', icon: AiOutlineSortDescending}]}
            activeValue={filter.sort}
            onChange={(value) => filter.setFilter({...filter, sort: value})}
            styles={{ inputGroup: styles.inputGroup, btnGroup: styles.btnGroup, btnGroupActive: styles.btnGroupActive}}
          />
          <ButtonGroup
            options={[{title: 'IN', icon: PiHandDeposit }, { title: 'OUT', icon: HiOutlineArrowDownOnSquareStack }]}
            activeValue={filter.transfer_way}
            onChange={(value) => filter.setFilter({...filter, transfer_way: value})}
            styles={{ inputGroup: styles.inputGroup, btnGroup: styles.btnGroup, btnGroupActive: styles.btnGroupActive}}
          />
        </section>
      </div>

      <table className={styles.table}>
        <thead>
          <tr>
            <th>Descripción</th>
            <th>Monto</th>
            <th>Cargo</th>
            <th>Fecha</th>
            <th>Hora</th>
            <th>{key}</th>
          </tr>
        </thead>
        <tbody>
          {commissions.length > 0 ? (
            commissions.map((commission, index) => (
              <tr key={index}>
                <td>{commission.description}</td>
                <td>{formatCurrency(commission.cargo) || 'Sin monto'}</td>
                <td>{formatCurrency(commission.commission) || 'Sin cargo'}</td>

                <td>{commission.fecha}</td>
                <td>{commission.hora}</td>
                <td>{commission.player_account}</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={5} className={styles.noResults}>
                No se encontraron resultados
              </td>
            </tr>
          )}
        </tbody>
      </table>

      <Pagination
        currentPage={pagination.currentPage}
        totalPages={pagination.totalPages}
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        onNavigatePage={pagination.setCurrentPage}
      />
    </>
  )
}

export default CommissionsTable
