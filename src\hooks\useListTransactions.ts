import { getListTransactions } from '@/api/endpoints/user'
import { formatCurrency, formatDate, formatTime } from '@/utils/formatters'
import { useState, useEffect } from 'react'

export interface Transaction {
  id: string // Identificador único de la transacción
  date: string // Fecha de la transacción, en formato ISO
  dateFormatted: string // Fecha formateada
  timeFormatted: string // Hora formateada
  description: string // Descripción de la transacción
  amount: string // Monto de la transacción
  key: string
  type: string
}

export function getThreeMonthRange(firstDate?: string, secondDate?: string) {
  const now = new Date();
  const todayUTC = new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate());
  
  // Si no hay fechas, usar rango de 3 meses por defecto
  if (!firstDate && !secondDate) {
    const start = new Date(todayUTC);
    start.setUTCMonth(todayUTC.getUTCMonth() - 3);
    start.setUTCHours(0, 0, 0, 0);
    
    const end = new Date(now); // Usar hora actual para no exceder el día de hoy
    
    return {
      initialDate: start.toISOString(),
      endDate: end.toISOString(),
    };
  }

  // Crear fechas siempre en UTC
  const start = firstDate 
    ? new Date(`${firstDate}T00:00:00.000Z`)
    : new Date(todayUTC);
  
  let end: Date;
  if (secondDate) {
    const endDateUTC = new Date(`${secondDate}T23:59:59.999Z`);
    // Si la fecha final es hoy o posterior, usar la hora actual
    const todayString = todayUTC.toISOString().split('T')[0];
    if (secondDate >= todayString) {
      end = new Date(now); // Usar hora actual para no exceder
    } else {
      end = endDateUTC; // Usar fin del día para fechas pasadas
    }
  } else {
    // Si no hay segunda fecha, usar hora actual
    end = new Date(now);
  }

  return {
    initialDate: start.toISOString(),
    endDate: end.toISOString(),
  };
}

export const useListTransactions = (email: string, firstDate: string, secondDate: string) => {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)

  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true)
      try {
        const { initialDate, endDate } = getThreeMonthRange(firstDate, secondDate)
        const page = 0
        const limit = 100

        const data = await getListTransactions(email, {
          initialDate,
          endDate,
          page,
          limit,
        })
        const formattedData = data
          .map((transaction: Transaction) => {
            const formattedAmount =
              transaction.type === 'creditor'
                ? `+${formatCurrency(String(transaction.amount))}` // "+" si es "creditor"
                : `-${formatCurrency(String(transaction.amount))}` // "-" si es "debtor"

            return {
              ...transaction,
              dateFormatted: formatDate(transaction.date), // Formatear la fecha
              timeFormatted: formatTime(transaction.date),
              amount: formattedAmount, // Usar el monto formateado con el signo correcto
            }
          })
          .sort(
            (a: Transaction, b: Transaction) =>
              new Date(b.date).getTime() - new Date(a.date).getTime()
          ) // Ordena de más reciente a menos reciente
        setTransactions(formattedData)
      } catch (err) {
        console.error('Error fetching transactions:', err)
      } finally {
        setIsLoading(false)
      }
    }

    if (email) {
      fetchTransactions()
    }
  }, [email, firstDate, secondDate])

  return { transactions, isLoading }
}
