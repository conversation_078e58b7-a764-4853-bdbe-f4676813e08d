.dateButtonContainer {
  position: relative;
  display: inline-block;
}

.dateButton {
  padding: 10px 14px;
  gap: 4px;
  width: 103px;
  height: 48px;
  background-color: #ffffff;
  border: 1px solid #d0d5dd;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
  border-radius: 8px;
  color: black;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Inter', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
}

.dateButton:hover {
  background-color: #f0f4f8;
  border-color: #b0b7c3;
}

.calendar {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.calendarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

.calendarHeader button {
  border: none;
  background: none;
  cursor: pointer;
  padding: 4px 8px;
}

.calendarDays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  padding: 8px;
}

.calendarDays > div {
  text-align: center;
  padding: 6px;
  font-size: 14px;
}

.calendarDay {
  cursor: pointer;
}

.calendarDay.active:hover {
  background: #f0f0f0;
  border-radius: 4px;
}
