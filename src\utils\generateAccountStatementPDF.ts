import { PDFGenerator } from './pdfGenerator'
import { getAccountStatement } from '@/api/endpoints/reports'
import { Statement, StatementParams } from '@/types/reports/statement'

// Función para formatear moneda
const formatCurrency = (amount: number | string): string => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN'
  }).format(numericAmount)
}

// Función para formatear fecha robusta (siempre UTC y español)
const formatDate = (date: string | Date): string => {
  const d = typeof date === 'string' ? new Date(date) : date
  // Extraer partes en UTC para evitar desfases de zona horaria
  const day = d.getUTCDate().toString().padStart(2, '0')
  const year = d.getUTCFullYear()
  // Array de meses en español
  const months = [
    'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
    'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
  ]
  const month = months[d.getUTCMonth()]
  return `${day} de ${month} de ${year}`
}

// Función para cargar el template HTML
const loadTemplate = async (templateName: string = 'account-statement'): Promise<string> => {
  try {
    const response = await fetch(`/templates/${templateName}.html`)
    return await response.text()
  } catch (error) {
    console.error('Error loading template:', error)
    throw new Error(`No se pudo cargar el template ${templateName}.html`)
  }
}

// Función para generar las filas de transacciones
const generateTransactionRows = (transactions: Statement['transactions']): string => {
  if (!transactions || transactions.length === 0) {
    return `
      <tr class="empty-row">
        <td colspan="7" style="text-align: center;">
          No hay movimientos disponibles
        </td>
      </tr>
    `
  }

  return transactions.map(transaction => `
    <tr>
      <td>${transaction.transactionDate ? formatDate(transaction.transactionDate) : 'N/A'}</td>
      <td>${transaction.transactionDate ? formatDate(transaction.transactionDate) : 'N/A'}</td>
      <td>${transaction.concept || 'N/A'}</td>
      <td>${transaction.id || 'N/A'}</td>
      <td>${transaction.type === 'OUT' && transaction.amount ? formatCurrency(transaction.amount) : '$0.00'}</td>
      <td>${transaction.type === 'IN' && transaction.amount ? formatCurrency(transaction.amount) : '$0.00'}</td>
      <td>${transaction.currentAmount ? formatCurrency(transaction.currentAmount) : '$0.00'}</td>
    </tr>
  `).join('')
}

// Función para reemplazar placeholders en el template
const replacePlaceholders = (
  template: string,
  statement: Statement,
  pageNumber?: number
): string => {
  const { adminData, userData, startDate, endDate, transactions } = statement
  const periodString = `${formatDate(startDate)} - ${formatDate(endDate)}`

  const replacements = {
    denominacionSocial: adminData.companyName || 'N/A',
    nombreComercial: userData.name || 'N/A',
    fecha: periodString,
    numeroCuenta: userData.account || 'N/A',
    numeroClabe: userData.clabe || 'N/A',
    rfc: userData.rfc || 'N/A',
    direccionFiscal: userData.address 
      ? `${userData.address.street || 'N/A'} ${userData.address.numExt || 'N/A'}, ${userData.address.colonia || 'N/A'}, ${userData.address.city || 'N/A'}, ${userData.address.state || 'N/A'}, CP: ${userData.address.zipCode || 'N/A'}`
      : 'N/A',
    saldoAnterior: statement.lastAmount ? formatCurrency(statement.lastAmount) : '$0.00',
    depositos: statement.amountIn ? formatCurrency(statement.amountIn) : '$0.00',
    retiros: statement.amountOut ? formatCurrency(statement.amountOut) : '$0.00',
    saldoFinal: statement.currentAmount ? formatCurrency(statement.currentAmount) : '$0.00',
    promedioAbonos: statement.averageAmount ? formatCurrency(statement.averageAmount) : '$0.00',
    movimientos: generateTransactionRows(transactions),
    pageNumber: pageNumber?.toString() || '1'
  }

  let result = template
  for (const [key, value] of Object.entries(replacements)) {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value)
  }

  return result
}

// Función principal para generar el PDF del estado de cuenta
export const generateAccountStatementPDF = async (params: StatementParams): Promise<void> => {
  try {
    // Obtener datos del estado de cuenta desde el endpoint
    const statement = await getAccountStatement(params)

    // Cargar los templates
    const mainTemplate = await loadTemplate('account-statement')
    const continuationTemplate = await loadTemplate('account-statement-continuation')

    // Calcular número de páginas necesarias (5 transacciones en primera página, 17 en las siguientes)
    const itemsPerFirstPage = 5
    const itemsPerPage = 17
    // const totalTransactions = statement.transactions.length
    // const remainingTransactions = Math.max(0, totalTransactions - itemsPerFirstPage)
    // const additionalPages = Math.ceil(remainingTransactions / itemsPerPage)

    // Configurar el generador de PDF
    const pdfGenerator = new PDFGenerator({
      templateHTML: mainTemplate,
      fileName: `estado-cuenta-${params.userId}.pdf`,
      pageConfig: {
        itemsPerFirstPage,
        itemsPerPage
      },
      pageCallback: (pageNumber: number) => {
        if (pageNumber === 1) {
          // Primera página con el template principal
          const firstPageTransactions = {
            ...statement,
            transactions: statement.transactions.slice(0, itemsPerFirstPage)
          }
          return replacePlaceholders(mainTemplate, firstPageTransactions)
        } else {
          // Páginas adicionales con el template de continuación
          const startIdx = itemsPerFirstPage + (pageNumber - 2) * itemsPerPage
          const endIdx = startIdx + itemsPerPage
          const pageTransactions = {
            ...statement,
            transactions: statement.transactions.slice(startIdx, endIdx)
          }
          return replacePlaceholders(continuationTemplate, pageTransactions, pageNumber)
        }
      }
    })

    // Generar el PDF
    await pdfGenerator.generatePDF(statement.transactions)
  } catch (error) {
    console.error('Error generando el PDF del estado de cuenta:', error)
    throw new Error('No se pudo generar el PDF del estado de cuenta')
  }
}
