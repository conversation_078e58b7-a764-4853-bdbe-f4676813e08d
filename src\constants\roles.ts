// Macro-roles
export const ROLE_TYPES = {
  CONVENIA: 'CONVENIA',
  CLIENTE: 'CLIENTE',
} as const

// Roles específicos
export const ROLES = {
  ADMIN_CONVENIA: 'Administrador Convenia',
  ADMIN_CONVENIA_CONSULTOR: 'Administrador Convenia Consultor',
  SOPORTE_CONVENIA: 'Administrador Soporte',

  CLIENTE_EMPRESA_ADMIN: 'Cliente Administrador',
  CLIENTE_EMPRESA_TESORERO: 'Cliente Tesorero',
  CLIENTE_TARJETAHABIENTES: 'Cliente Gestor de Tarjetahabientes',
  CLIENTE_LECTOR: 'Client<PERSON> Lector',
} as const

export type RoleName = (typeof ROLES)[keyof typeof ROLES]

export type RoleType = (typeof ROLE_TYPES)[keyof typeof ROLE_TYPES]

export const hasAccess = (roleName: RoleName | null, allowedRoles: RoleName[]): boolean => {
  return !!roleName && allowedRoles.includes(roleName)
}
