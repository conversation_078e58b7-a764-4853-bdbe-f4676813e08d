export interface ClarificationProps {
  page: number
  limit: number
}

export interface ClarificationFile {
  id: string
  file_name: string
  file_url: string
}

export type ClarificationStatus = 'Pendiente' | 'Resuelto' | 'Abierto'

export interface Clarification {
  trackingNumber: string
  type: string
  description: string
  status: ClarificationStatus
  createdAt: string
  user: {
    name: string
    email: string
    avatar: string
  }
  files: ClarificationFile[]
}

export interface ClarificationResponse {
  clarifications: Clarification[]
  totalRows: number
}
