import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'
interface Props extends BaseModalProps {
  accountNumber: string
  isActive: boolean
  onConfirm: (accountNumber: string) => void
}

const ModalActiveAccount: React.FC<Props> = ({
  open,
  onClose,
  accountNumber,
  isActive,
  onConfirm,
}) => {
  const actionText = isActive ? 'desactivar' : 'activar'

  return (
    <AlertModal
      open={open}
      type="warning"
      title={`¿Estás seguro que deseas ${actionText} la cuenta ${accountNumber}?`}
      onClose={onClose}
      onPressBtn={() => onConfirm(accountNumber)}
    />
  )
}

export default ModalActiveAccount
