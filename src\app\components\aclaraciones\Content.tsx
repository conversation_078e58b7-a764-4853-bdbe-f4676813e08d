/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react'
import Header from '../Header'
import Styles from '../styles/Aclaraciones.module.css'
import SearchBar from '../SearchBar'
import DateButton from '../DateButton'
import Clarifications from './Clarifications'
import ModalDeleteClarification from '../modals/alertModals/ModalDeleteClarification'
import { useClarificationStore } from './store/useClarificationStore'

const Content = () => {
  const [showModal, setShowModal] = useState(false)

  const { fetchClarifications, handleSearch, currentPage, totalPages } = useClarificationStore()

  useEffect(() => {
    fetchClarifications({ page: currentPage, limit: 5 })
  }, [fetchClarifications, currentPage])

  return (
    <div>
      <Header title="Aclaraciones" />
      <div className={Styles.actionsTableContainer}>
        <DateButton onSelectedDate={() => null} />
        <SearchBar onSearch={handleSearch} />
      </div>
      <Clarifications
        onOpenDeleteClientModal={clarification => {
          setShowModal(true)
        }}
      />
      <ModalDeleteClarification open={showModal} onClose={() => setShowModal(false)} />
    </div>
  )
}

export default Content
