import React from 'react'
import styles from '../styles/AlertModal.module.css'
import { AlertModalProps } from '@/types/types'
import AlertModalBase from './AlertModalBase'

const AlertModal: React.FC<AlertModalProps> = ({
  type,
  title,
  message,
  open,
  textBtn,
  renderMessage,
  onClose,
  onPressBtn,
}) => {
  return (
    <AlertModalBase type={type} title={title} message={message} open={open} onClose={onClose}>
      <div style={{ margin: '0px 24px' }}>
        {renderMessage && renderMessage()}
        {textBtn !== '' && (
          <div className={styles.buttonContainer}>
            <button className={styles.btn} onClick={onPressBtn}>
              {textBtn || 'Aceptar'}
            </button>
          </div>
        )}
      </div>
    </AlertModalBase>
  )
}

export default AlertModal
