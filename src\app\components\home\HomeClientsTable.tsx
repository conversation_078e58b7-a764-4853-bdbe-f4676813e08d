'use client'
import { MdOutlineEdit } from 'react-icons/md'
import { LuTrash2 } from 'react-icons/lu'
import styles from '../styles/ClientsTable.module.css'
import { HomeClientsTableProps } from '@/types/types'
import { showActionsInClientTable } from '@/permissions/access'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { HiOutlineEye } from 'react-icons/hi'
import { useRouter } from 'next/navigation'
import { AdminClient } from '@/types/admin/types'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { toast } from 'react-toastify'

const HomeClientsTable: React.FC<HomeClientsTableProps> = ({
  clients,
  onEditClient,
  onDeleteClient,
}) => {
  const router = useRouter()
  const { getUserRoleName } = useAuthStore()
    const { fetchClientAdminByGroupId, setAdminSelected } = useAdminStore()

  const handleViewClient = (client: AdminClient) => {
    setAdminSelected(client)
    const groupId = client.groupId ? client.groupId.toString() : null
    if (!groupId) {
      toast.error('Este cliente no tiene grupo asignado.', {
        position: 'top-right',
        autoClose: 3000,
      })
      return
    }

    fetchClientAdminByGroupId({ limit: 5, page: 1, q: '', groupId, orderBy: 'createdAt' })
    router.push(`/clientes/group-client-admin`)
  }

  return (
    <table className={styles.table}>
      <thead>
        <tr>
          <th>Clientes</th>
          <th>Responsable</th>
          <th>Número de tarjetas asignadas</th>
          <th>Saldo</th>
          <th>Fecha de registro</th>
          <th>RFC</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        {clients.length > 0 ? (
          clients.map((client, index) => (
            <tr key={index}>
              <td>
                <strong>{client.companyName}</strong>
                <span>{client.manager?.email ?? 'Sin correo'}</span>
              </td>
              <td>{client.manager?.name ?? 'No asignado'}</td>
              <td>{client.numAsignedCards}</td>
              <td>
                {typeof client.amount === 'string'
                  ? Number(client.amount).toLocaleString('es-MX', {
                      style: 'currency',
                      currency: 'MXN',
                    })
                  : 'Sin saldo'}
              </td>
              <td>{new Date(client.createdAt).toLocaleDateString('es-MX')}</td>
              <td>{client.rfc}</td>
              <td>
                <div className={styles.actions}>
                  <button className={styles.actionButton}>
                    <HiOutlineEye size={18} onClick={() => handleViewClient(client)} />
                  </button>
                  {showActionsInClientTable(getUserRoleName()) && (
                    <>
                      <button className={styles.actionButton} onClick={() => onEditClient(client)}>
                        <MdOutlineEdit size={18} />
                      </button>
                      <button
                        className={styles.actionButton}
                        onClick={() => onDeleteClient(client)}
                      >
                        <LuTrash2 size={18} />
                      </button>
                    </>
                  )}
                </div>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className={styles.noResults}>
              No se encontraron resultados
            </td>
          </tr>
        )}
      </tbody>
    </table>
  )
}

export default HomeClientsTable
