.stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.stat {
  background-color: #000;
  color: #f4f5fb;
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  font-weight: 600;
  font-family: Inter;
  font-size: 22px;
  line-height: 26.63px;
  height: auto;
  min-height: 122px;
}

.stat span {
  display: block;
  margin-top: 20px;
}

.aloneStat {
  background-color: #000;
  color: #f4f5fb;
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  font-weight: 600;
  font-family: Inter;
  font-size: 22px;
  line-height: 26.63px;
  height: 122px;
  width: 350px;
}

.stat,
.aloneStat {
  display: flex;
  flex-direction: column;
  min-height: 122px;
  text-align: center;
}

.stat p,
.aloneStat p {
  margin: 0 0 8px 0;
}

.stat span,
.aloneStat span {
  margin-top: auto;
  margin-bottom: auto;
}

.loaderWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 28px;
}

@media screen and (min-width: 640px) {
  .stats {
    flex-direction: row;
    justify-content: flex-end;
  }

  .stat {
    width: 100%;
  }
}

@media screen and (max-width: 900px) {
  .stats {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .stat {
    flex: 1 1 calc(50% - 7.5px);
  }

  .aloneStat {
    flex: 1 1 100%;
    max-width: 100%;
  }
}
