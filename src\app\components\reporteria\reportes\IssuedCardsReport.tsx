/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useEffect, useMemo, useState } from 'react'
import { BsCreditCard2Front } from 'react-icons/bs'
import { useIssuedCardsReport } from '@/hooks/reportes'
import ReportFilters from '@/app/components/ReportFilters'
import Select from '@/app/components/Select'
import DateFilter from '@/app/components/DateFilter'
import Input from '@/app/components/Input'
import { AdminBasic } from '@/types/admin/types'
import type { CardStatus, CardType } from '@/types/reports/issued-cards'
import { TYPE_OPTIONS, STATUS_OPTIONS, formatRangeDate, getAdminLabel, getTypeLabel, getStatusLabel } from './report.utils'

interface UseIssuedCardsFiltersParams {
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
  admins: AdminBasic[]
}

export interface IssuedCardsFilters {
  startDate?: Date
  endDate?: Date
  cardType?: CardType
  status?: CardStatus
  cardHolderName?: string
  adminId?: string
}

export function useIssuedCardsFilters({
  isClient,
  selectedAdminId,
  onAdminChange,
  admins,
}: UseIssuedCardsFiltersParams) {
  const [dateClearKey, setDateClearKey] = useState(0)
  const [filters, setFilters] = useState<IssuedCardsFilters>({})

  const activeFilters = useMemo(() => {
    const list: string[] = []

    list.push(
      `Empresa: ${getAdminLabel(
        filters.adminId,
        isClient,
        admins,
        selectedAdminId
      )
      }`
    )

    list.push(`Tipo de Tarjeta: ${getTypeLabel(filters.cardType)}`)
    list.push(`Estado: ${getStatusLabel(filters.status)}`)

    if (filters.cardHolderName) list.push(`Titular: ${filters.cardHolderName}`)

    if (filters.startDate && filters.endDate) list.push(formatRangeDate(filters.startDate, filters.endDate))


    return list
  }, [filters, admins, isClient, selectedAdminId])

  const handleFilterChange = (
    name: keyof IssuedCardsFilters,
    value: string | Date | undefined
  ) => {
    if (isClient && name === 'adminId') return

    setFilters(prev => ({
      ...prev,
      [name]: value === '' ? undefined : (value as string | Date),
    }))

    if (name === 'adminId') {
      onAdminChange(value as string)
    }
  }

  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      setFilters(f => ({ ...f, startDate: undefined, endDate: undefined }))
      return
    }
    const [s, e] = dates.length === 1 ? [dates[0], dates[0]] : dates
    setFilters(f => ({
      ...f,
      startDate: new Date(s),
      endDate: new Date(e)
    }))
  }

  const handleReset = () => {
    if (isClient) {
      onAdminChange(selectedAdminId)
      setFilters({
        startDate: undefined,
        endDate: undefined,
        cardType: undefined,
        status: undefined,
        cardHolderName: undefined,
        adminId: selectedAdminId,
      })
    } else {
      onAdminChange('')
      setFilters({
        startDate: undefined,
        endDate: undefined,
        cardType: undefined,
        status: undefined,
        cardHolderName: undefined,
        adminId: undefined,
      })
    }
    setDateClearKey(k => k + 1)
  }

  useEffect(() => {
    if (!selectedAdminId || admins.length === 0) return
    setFilters(prev => ({ ...prev, adminId: selectedAdminId || undefined }))
  }, [selectedAdminId, admins])

  return {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleDateChange,
    handleReset,
  }
}

interface IssuedCardsReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
  admins: AdminBasic[]
  /** Nuevo: si es cliente, fuerza admin fijo */
  isClient: boolean
  /** El admin seleccionado en el padre */
  selectedAdminId: string
  /** Aviso al padre cuando cambia el admin */
  onAdminChange: (adminId: string) => void
}

interface IssuedCardsReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
  admins: AdminBasic[]
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
}

const IssuedCardsReport: React.FC<IssuedCardsReportProps> = ({
  onLoadingChange,
  admins,
  isClient,
  selectedAdminId,
  onAdminChange,
}) => {
  const { isLoading, loadingMessage, downloadReport } = useIssuedCardsReport()

  const {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleDateChange,
    handleReset,
  } = useIssuedCardsFilters({ isClient, selectedAdminId, onAdminChange, admins })

  const handleDownload = async () => {
    const raw = { ...filters, ...(isClient ? { adminId: selectedAdminId } : {}) }
    const params = Object.fromEntries(
      Object.entries(raw).filter(([, v]) => v != null && v !== '')
    )
    await downloadReport(params)
  }

  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onSubmit={e => e.preventDefault()}
      onReset={handleReset}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Nuevas tarjetas"
      icon={<BsCreditCard2Front />}
      downloadOptions={{
        onlyExcel: true,
        onExcelDownload: handleDownload,
      }}
    >
      <Select
        name="adminId"
        label="Empresa"
        value={filters.adminId || ''}
        onChange={e => handleFilterChange('adminId', e.target.value)}
        disabled={isClient}
      >
        <option value="">Todas las empresas</option>
        {admins.map(a => (
          <option key={a.id} value={a.id}>
            {a.alias || a.companyName}
          </option>
        ))}
      </Select>

      <Select
        name="cardType"
        label="Tipo de Tarjeta"
        value={filters.cardType || ''}
        onChange={e => handleFilterChange('cardType', e.target.value)}
      >
        <option value="">Todos los tipos</option>
        {TYPE_OPTIONS.map(opt => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </Select>

      <Select
        name="status"
        label="Estado de la tarjeta"
        value={filters.status || ''}
        onChange={e => handleFilterChange('status', e.target.value)}
      >
        <option value="">Todos los estados</option>
        {STATUS_OPTIONS.map(opt => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </Select>

      <DateFilter
        mode="range"
        onDateChange={handleDateChange}
        label="Rango de fechas"
        placeholder="Selecciona el rango de fechas"
        clearTrigger={dateClearKey}
      />

      <Input
        type="text"
        name="cardHolderName"
        label="Buscar por titular"
        placeholder="Nombre del titular"
        value={filters.cardHolderName || ''}
        onChange={e => handleFilterChange('cardHolderName', e.target.value)}
        onKeyDown={e => {
          if (e.key === 'Enter') e.preventDefault()
        }}
      />
    </ReportFilters>
  )
}

export default IssuedCardsReport
