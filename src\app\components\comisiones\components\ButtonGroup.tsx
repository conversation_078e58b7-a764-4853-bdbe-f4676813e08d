import { IconType } from "react-icons";

interface filterOption {
  title: string;
  icon?: IconType;
}

interface ButtonGroupProps {
  options: filterOption[];
  activeValue: string;
  onChange: (value: string) => void;
  styles: {
    inputGroup: string;
    btnGroup: string;
    btnGroupActive: string;
  };
}

const ButtonGroup = ({ options, activeValue, onChange, styles }: ButtonGroupProps) => (
  <div className={styles.inputGroup}>
    {options.map(({ title, icon: Icon}) => (
      <button
        key={title}
        className={activeValue === title ? styles.btnGroupActive : styles.btnGroup}
        onClick={() => onChange(title)}
      >
        {Icon && <Icon size={18} />}
        {title}
      </button>
    ))}
  </div>
);

export default ButtonGroup;