import styles from './styles/GoldCheckbox.module.css'

interface GoldCheckboxProps {
  checked?: boolean
  onChange?: () => void
}

const GoldCheckbox = ({ checked, onChange }: GoldCheckboxProps) => {
  return (
    <div className={checked ? styles.wrapper : styles.wrapperNotSelected} onClick={onChange}>
      {checked && (
        <svg
          width="18"
          height="13"
          viewBox="0 0 18 13"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16.4668 1.40039L6.20011 11.6671L1.53345 7.00039"
            stroke="white"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}
    </div>
  )
}

export default GoldCheckbox
