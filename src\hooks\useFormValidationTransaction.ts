import { useState } from 'react'
import { transferValidationRules } from '@/utils/validationRules'

export type TransactionFormData = {
  empresa: string
  numeroCuenta: string
  cuentaDestino: string
  tipoTransferencia: string
  banco: string
  numeroReferencia: string
  importe: string
  nombreBeneficiario: string
  rfcBeneficiario: string
  correo: string
  conceptoBeneficiario: string
  tipoPago: string
  guardarDestinatario: boolean
}

export type TransactionErrors = Partial<Record<keyof TransactionFormData, string>>

const useFormValidationTransaction = (initialData?: Partial<TransactionFormData>) => {
  const [formData, setFormData] = useState<TransactionFormData>({
    empresa: initialData?.empresa || '0',
    numeroCuenta: initialData?.numeroCuenta || '',
    cuentaDestino: initialData?.cuentaDestino || '',
    tipoTransferencia: initialData?.tipoTransferencia || '',
    banco: initialData?.banco || '',
    numeroReferencia: initialData?.numeroReferencia || '',
    importe: initialData?.importe || '',
    nombreBeneficiario: initialData?.nombreBeneficiario || '',
    rfcBeneficiario: initialData?.rfcBeneficiario || '',
    correo: initialData?.correo || '',
    conceptoBeneficiario: initialData?.conceptoBeneficiario || '',
    tipoPago: initialData?.tipoPago || '',
    guardarDestinatario: initialData?.guardarDestinatario || false,
  })

  const [errors, setErrors] = useState<TransactionErrors>({})
  const [touched, setTouched] = useState<Record<keyof TransactionFormData, boolean>>(
    {} as Record<keyof TransactionFormData, boolean>
  )

  // Validate a single field
  const validateField = (name: keyof TransactionFormData, value: string): string => {
    const validationFn = transferValidationRules[name as keyof typeof transferValidationRules]
    if (validationFn) {
      return validationFn(value)
    }
    return ''
  }

  // Validate all fields or specific fields
  const validate = (fieldsToValidate?: (keyof TransactionFormData)[]): boolean => {
    const newErrors: TransactionErrors = {}
    const fields =
      fieldsToValidate || (Object.keys(transferValidationRules) as (keyof TransactionFormData)[])

    fields.forEach(field => {
      if (field in transferValidationRules) {
        const value = formData[field] || ''
        const error = validateField(field, typeof value === 'string' ? value : String(value))
        if (error) {
          newErrors[field] = error
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle input changes with validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    const typedName = name as keyof TransactionFormData

    setFormData(prev => ({ ...prev, [typedName]: value }))

    // Mark field as touched
    setTouched(prev => ({ ...prev, [typedName]: true }))

    // Validate the field
    const error = validateField(typedName, value)
    setErrors(prev => ({ ...prev, [typedName]: error }))
  }

  // Handle checkbox/switch changes
  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, guardarDestinatario: checked }))
  }

  // Mark a field as touched (for blur events)
  const handleBlur = (name: keyof TransactionFormData) => {
    setTouched(prev => ({ ...prev, [name]: true }))

    const value = formData[name] || ''
    const error = validateField(name, typeof value === 'string' ? value : String(value))
    setErrors(prev => ({ ...prev, [name]: error }))
  }

  // Reset the form to initial state or empty
  const resetForm = () => {
    setFormData({
      empresa: '0',
      numeroCuenta: '',
      cuentaDestino: '',
      tipoTransferencia: '',
      banco: '',
      numeroReferencia: '',
      importe: '',
      nombreBeneficiario: '',
      rfcBeneficiario: '',
      correo: '',
      conceptoBeneficiario: '',
      tipoPago: '0',
      guardarDestinatario: false,
    })
    setErrors({})
    setTouched({} as Record<keyof TransactionFormData, boolean>)
  }

  // Check if the form is valid
  const isFormValid = (): boolean => {
    // Create a temporary errors object without updating state
    const newErrors: TransactionErrors = {}
    const fields = Object.keys(transferValidationRules) as (keyof TransactionFormData)[]

    fields.forEach(field => {
      if (field in transferValidationRules) {
        const value = formData[field] || ''
        const error = validateField(field, typeof value === 'string' ? value : String(value))
        if (error) {
          newErrors[field] = error
        }
      }
    })

    return Object.keys(newErrors).length === 0
  }

  return {
    formData,
    errors,
    touched,
    setFormData,
    handleInputChange,
    handleSwitchChange,
    handleBlur,
    validate,
    resetForm,
    isFormValid,
  }
}

export default useFormValidationTransaction
