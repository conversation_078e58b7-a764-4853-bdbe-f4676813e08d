// este modal se muestra cuando se guardan los datos de un nuevo cliente
import React from 'react'
import InfoModal from '../InfoModal'
import { BaseModalProps } from '@/types/types'

const EditedUserSuccess: React.FC<BaseModalProps> = ({ open, onClose }) => {
  return (
    <InfoModal
      title="¡El usuario fue editado correctamente!"
      message="Hemos recibido y almacenado los datos correctamente."
      open={open}
      onPressPrimaryBtn={onClose}
      onClose={onClose}
    />
  )
}

export default EditedUserSuccess
