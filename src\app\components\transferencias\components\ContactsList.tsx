/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState } from 'react'
import styles from '../../styles/Transaction.module.css'
import { MdOutlineEdit } from 'react-icons/md'
import { LuTrash2 } from 'react-icons/lu'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import ModalDeleteContactTransfer from '../../modals/alertModals/ModalDeleteContactTransfer'
import { useEffect } from 'react'
import { useContactStore } from '@/store/contact/useContactStore'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { Contact } from '@/types/contact/types'
type Props = {
  onSelectContact: (contact: Contact) => void
}
const ContactList: React.FC<Props> = ({ onSelectContact }) => {
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const { user } = useAuthStore()
  const { contacts, loadContacts, setSelectedContact, removeContact } = useContactStore()
  const [selectedId, setSelectedId] = useState<string | null>(null)
  const [aliasContacto, setAliasContacto] = useState<string>('')

  useEffect(() => {
    if (user?.id && user?.email) {
      loadContacts(user.id, user.email)
    }
  }, [user])

  const handleEdit = (contact: Contact): void => {
    setSelectedContact(contact)
    router.push(`/transferencias/editar-contacto`)
  }

  const handleDelete = (contact: Contact): void => {
    setSelectedId(contact.id)
    setAliasContacto(contact.name || '')
    setOpen(true)
  }

  const confirmDelete = async () => {
    if (selectedId) {
      await removeContact(selectedId)
      setSelectedId(null)
      setOpen(false)
    }
  }

  const goToTranferContact = (contact: Contact) => {
    setSelectedContact(contact)
    onSelectContact(contact)
  }

  return (
    <>
      <div className={styles.contactsWrapper}>
        <div className={styles.contactsList}>
          {contacts.length === 0 ? (
            <div className={styles.contactFrame}>
              <p className={styles.contactCategory}>No tienes contactos guardados aún.</p>
            </div>
          ) : (
            contacts.map(contact => (
              <div key={contact.id} className={styles.contactFrame}>
                <div className={styles.contactItem}>
                  <div className={styles.userItem} onClick={() => goToTranferContact(contact)}>
                    <div className={styles.contactInfo}>
                      <Image
                        src="/t-convenia1.svg"
                        alt={`${contact.name}'s avatar`}
                        className={styles.contactAvatar}
                        width={40}
                        height={40}
                      />
                      <h3 className={styles.contactName}>{contact.name}</h3>
                    </div>
                  </div>

                  <div
                    className={styles.contactBankInfo}
                    onClick={() => goToTranferContact(contact)}
                  >
                    <p className={styles.bankName}>{contact.bank_institution}</p>
                    <p className={styles.accountNumber}>
                      Número de cuenta ••••
                      {contact.num_clabe ? contact.num_clabe.toString().slice(-4) : '----'}
                    </p>
                  </div>

                  <div className={styles.contactActions}>
                    <button className={styles.actionButton} onClick={() => handleEdit(contact)}>
                      <MdOutlineEdit size={18} />
                    </button>

                    <button className={styles.actionButton} onClick={() => handleDelete(contact)}>
                      <LuTrash2 size={18} />
                    </button>
                  </div>
                </div>
                <p className={styles.contactCategory}>{contact.bank_institution || 'Sin banco'}</p>
              </div>
            ))
          )}
        </div>
      </div>
      <ModalDeleteContactTransfer
        open={open}
        onClose={() => {
          setOpen(false)
          setSelectedId(null)
        }}
        onPressBtn={confirmDelete}
        aliasContacto={aliasContacto}
      />
    </>
  )
}

export default ContactList
