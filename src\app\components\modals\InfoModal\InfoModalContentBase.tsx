import Image from 'next/image'
import styles from '../../styles/InfoModal.module.css'
type Props = {
  title: string
  message?: string
  titleBtn?: string
  renderBody?: () => JSX.Element
  onPressPrimaryBtn: () => void
  onClose: () => void
}
const InfoModalContentBase: React.FC<Props> = ({
  title,
  message,
  titleBtn,
  renderBody,
  onPressPrimaryBtn,
  onClose,
}) => {
  return (
    <>
      <Image
        src="/check-circle.svg"
        alt="icon"
        width={28}
        height={28}
        className={styles.image}
        onClick={onClose}
      />
      <div className={styles.title}>{title}</div>
      {renderBody && renderBody()}
      {message && <div className={styles.message}>{message}</div>}
      <button className={styles.btn} onClick={onPressPrimaryBtn}>
        {titleBtn || 'Ir al inicio'}
      </button>
    </>
  )
}

export default InfoModalContentBase
