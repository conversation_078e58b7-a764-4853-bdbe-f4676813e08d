// este modal se muestra cuando se guardan los datos de un nuevo cliente
import React from 'react'
import InfoModal from '../InfoModal'
import { BaseModalProps } from '@/types/types'

type SaveEditedAliasDataModalProps = BaseModalProps & {
  onConfirm: () => void
}

const SaveEditedAliasDataModal: React.FC<SaveEditedAliasDataModalProps> = ({
  open,
  onClose,
  onConfirm,
}) => {
  return (
    <InfoModal
      title="¡La edición se ha realizado con éxito!"
      message="Hemos recibido y almacenado los datos correctamente. "
      open={open}
      onPressPrimaryBtn={onConfirm}
      onClose={onClose}
    />
  )
}

export default SaveEditedAliasDataModal
