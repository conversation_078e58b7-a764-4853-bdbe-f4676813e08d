'use user'
import { MdOutlineEdit } from 'react-icons/md'
import { LuTrash2 } from 'react-icons/lu'
import styles from '../styles/ClientsTable.module.css'
import { canCreateUser } from '@/permissions/access'
import { useUserStore } from '@/store/user/useUserStore'
import { UserResponse } from '@/types/user/types'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { toast } from 'react-toastify'
import { useUiStore } from '@/store/ui/useUiStore'

type UsersManagementTableProps = {
  handleDeleteUserModal: (userName: string) => void
}

const UsersManagementTable: React.FC<UsersManagementTableProps> = ({ handleDeleteUserModal }) => {
  const { fetchConveniaUserById, users, error } = useUserStore()
  const { setLoading } = useUiStore()
  const { getUserRoleName } = useAuthStore()
  const router = useRouter()

  const handleEditUser = async (user: UserResponse) => {
    setLoading(true)
    const userFound = await fetchConveniaUserById(user.id)

    if (!userFound) {
      toast.error(error || 'No se encontró al usuario', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
      return
    }

    router.push(`/gestion-usuarios/edit-user-admin`)
  }

  const handleDeleteUser = (user: UserResponse) => {
    const adminName = user.id
    handleDeleteUserModal(adminName)
  }

  return (
    <table className={styles.table}>
      <thead>
        <tr>
          <th>Usuario</th>
          <th>Nombre</th>
          <th>Perfil</th>
          <th>Fecha de alta</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        {users.length > 0 ? (
          users.map((user, index) => (
            <tr key={index}>
              <td>
                <strong>{user.email}</strong>
              </td>
              <td>
                <strong>{user.name}</strong>
              </td>
              <td>{user.roles?.[0]?.name || ''}</td>
              <td>{new Date(user.createdAt).toLocaleDateString('es-MX')}</td>
              <td>
                {canCreateUser(getUserRoleName()) && (
                  <div className={styles.actions}>
                    <button className={styles.actionButton} onClick={() => handleEditUser(user)}>
                      <MdOutlineEdit size={18} />
                    </button>
                    <button className={styles.actionButton} onClick={() => handleDeleteUser(user)}>
                      <LuTrash2 size={18} />
                    </button>
                  </div>
                )}
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className={styles.noResults}>
              No se encontraron resultados
            </td>
          </tr>
        )}
      </tbody>
    </table>
  )
}

export default UsersManagementTable
