import React from 'react'
import styles from '../styles/Loader.module.css'

type LoaderProps = {
  size?: number // tamaño en px
}

const Loader = ({ size = 100 }: LoaderProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 100 100"
      fill="none"
      className={ styles.loader }
    >
      <path
        d="M50 25V12.5M67.7083 32.2917L76.6667 23.3333M75 50H87.5M67.7083 67.7083L76.6667 76.6667M50 75V87.5M32.2917 67.7083L23.3333 76.6667M25 50H12.5M32.2917 32.2917L23.3333 23.3333"
        stroke="url(#paint0_linear)"
        strokeWidth="5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear"
          x1="12.4989"
          y1="50.0026"
          x2="87.5008"
          y2="50.0026"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.0189996" stopColor="#DCB992" />
          <stop offset="0.249" stopColor="#DAB890" />
          <stop offset="0.459" stopColor="#D5B289" />
          <stop offset="0.684" stopColor="#CCA87C" />
          <stop offset="0.9751" stopColor="#BF9B6B" />
          <stop offset="0.9977" stopColor="#AF8B56" />
          <stop offset="0.9985" stopColor="#AF8B55" />
          <stop offset="1" stopColor="#E9D6B8" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export default Loader
