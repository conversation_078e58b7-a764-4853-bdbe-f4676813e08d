import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

interface ModalDeleteAdminProps extends BaseModalProps {
  name: string
  onConfirm?: () => void
}

const ModalDeleteAdmin: React.FC<ModalDeleteAdminProps> = ({ name, open, onClose, onConfirm }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title={`¿Estás seguro de que deseas eliminar este administrador ${name}?`}
      message="Esta acción no se puede deshacer."
      onClose={onClose}
      onPressBtn={onConfirm}
    />
  )
}

export default ModalDeleteAdmin
