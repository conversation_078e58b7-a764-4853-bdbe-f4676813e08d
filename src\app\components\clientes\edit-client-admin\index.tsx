/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import Header from '@/app/components/Header'
import EditClientAdmin from './EditClientAdmin'
import SaveEditedClientDataModal from '@/app/components/modals/informationModals/SaveEditedClientDataModal'
import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminStore } from '@/store/admin/useAdminStore'
import LoaderFull from '../../loader/LoaderFull'
import { toast } from 'react-toastify'

const HomePage = () => {
  const router = useRouter()
  const { adminSelected } = useAdminStore()
  const [openModal, setOpenModal] = useState(false)
  const hasShownToast = useRef(false)

  useEffect(() => {
    if (!adminSelected && !hasShownToast.current) {
      hasShownToast.current = true // ✅ evita múltiples ejecuciones
      toast.error('El cliente no fue encontrado', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
      router.push('/clientes')
    }
  }, [adminSelected, router])

  const handleCloseModal = () => router.push('/clientes')

  if (!adminSelected) return <LoaderFull />

  return (
    <>
      <Header title={`Editar ${adminSelected.companyName}`} />
      <EditClientAdmin onOpenModal={() => setOpenModal(true)} clientData={adminSelected} isEditing={true} />
      <SaveEditedClientDataModal open={openModal} onClose={handleCloseModal} />
    </>
  )
}

export default HomePage
