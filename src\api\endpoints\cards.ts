/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ChangeCardStatusResponse,
  ParamsChangeCardStatus,
  ReassignCardDto,
  ReassignCardResponse,
} from '@/types/cards/types'
import apiClient from '../client'

export const reassignCard = async (data: ReassignCardDto): Promise<ReassignCardResponse> => {
  const response = await apiClient.post('/card-assignment/reassign', data)
  return response.data
}

export const deleteCard = async (
  data: ParamsChangeCardStatus
): Promise<ChangeCardStatusResponse> => {
  const response = await apiClient.put('/dock-cards/control-card-status', data)
  return response.data
}
