// este modal es base para los modales de informacion
import React from 'react'
import Modal from '../Modal'
import Image from 'next/image'
import styles from '../../styles/InfoModal.module.css'
import { InfoModalProps } from '@/types/types'
import InfoModalContentBase from './InfoModalContentBase'

const InfoModal: React.FC<InfoModalProps> = ({
  title,
  message,
  titleBtn,
  open,
  renderBody,
  onPressPrimaryBtn,
  onClose,
}) => {
  return (
    <Modal open={open}>
      <div className={styles.content}>
        <Image
          src="/x-close.svg"
          alt="icon"
          width={24}
          height={24}
          className={styles.closeButton}
          onClick={onClose}
        />
        <InfoModalContentBase
          title={title}
          message={message}
          titleBtn={titleBtn}
          renderBody={renderBody}
          onPressPrimaryBtn={onPressPrimaryBtn}
          onClose={onClose}
        />
      </div>
    </Modal>
  )
}

export default InfoModal
