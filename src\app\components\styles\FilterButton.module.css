.filterContainer {
  position: relative;
  display: inline-block;
}

.toggleButton {
  height: 48px;
  /* width: 180px; */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: white;
  border: 1px solid #cbd5e0;
  border-radius: 12px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 16px;
  color: #667085;
  transition: background 0.2s;
}

.toggleButton:hover {
  background-color: #f1f1f1;
}

.dropdownContainer {
  position: absolute;
  top: 100%;
  left: 0;
  display: flex;
  margin-top: 8px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.filterOptions,
.accountOptions {
  min-width: 200px;
  padding: 8px 0px;
  position: relative;
}

.accountOptions::before {
  content: '';
  width: 1px;
  height: 90%;
  background-color: #ebebeb;
  /* position: relative; */
  z-index: 10;
  position: absolute;
  left: 0px;
  /* top: 25%; */
}

.filterOptions h3,
.accountOptions h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #666;
}

.filterOptions ul,
.accountOptions ul {
  list-style: none;
  padding: 0;
  margin: 0;

  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.15px;
  color: rgba(77, 80, 98, 0.87);
}

.accountOptions ul {
  margin-top: 6px;
}
.filterOptions li,
.accountOptions li {
  padding: 9px 16px;
  cursor: pointer;
  border-radius: 4px;
  height: 38px;
}

.filterOptions li:hover,
.accountOptions li:hover {
  background-color: #f5f5f5;
}

.filterOptions li.selected {
  background-color: #f4f5fb;
}

.selected {
  background-color: #f4f5fb;
}

.searchContainer {
  position: relative;
  width: calc(100% - 12px);
  display: flex;
  margin: 0 auto;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #d2d6db;
  border-radius: 8px;
  color: #111927;
  /* height: 41px; */
  padding-right: 15.3px;
}

.searchContainer input {
  border: none;
  padding: 9px 5px 9px 12px;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 24px;
  letter-spacing: 0.15px;
  border-radius: 8px;
  outline: none; /* Removes the focus outline */
  width: 100%;
}

.searchContainer input:focus {
  box-shadow: none;
  border: none;
}

.searchContainer:hover {
  border-color: #d0d5dd; /* Cambia el color del borde al pasar el cursor */
}

.searchContainer:focus-within {
  border-color: #6b7280; /* Cambia el borde al enfocarse */
}

.searchIconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
}
