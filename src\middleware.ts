import { NextRequest, NextResponse } from 'next/server'
import { PUBLIC_ROUTES } from './lib/auth/publicRoutes'

export function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl
  const token = req.cookies.get('token')?.value

  const isPublic = PUBLIC_ROUTES.some(
    route => pathname === route || pathname.startsWith(`${route}/`)
  )

  if (isPublic) {
    // Permitir acceso a rutas públicas
    return NextResponse.next()
  }

  if (!token) {
    // Si no hay token y no es ruta pública, redirige
    const url = req.nextUrl.clone()
    url.pathname = '/login'
    return NextResponse.redirect(url)
  }

  // Si hay token, continúa
  return NextResponse.next()
}

export const config = {
  matcher: ['/((?!_next|static|favicon.ico|logo).*)'], // aplica a todas menos assets
}
