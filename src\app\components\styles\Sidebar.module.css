.sidebar {
  max-width: 280px;
  width: 100%;
  background: #000;
  color: #fff;
  display: none;
  flex-direction: column;
  justify-content: space-between;
  padding: 3rem 1.5rem;
  height: 100vh;
  overflow-y: none; /* No permite scroll vertical */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE y Edge */
}

.sidebar::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.logoContainer {
  text-align: center;
  margin-bottom: 2rem;
}

.profileContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333; /* <PERSON><PERSON>ea inferior */
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.profileLabel {
  color: #c3c6d8;
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  margin-top: 0.5rem;
}

.menu {
  flex: 1;
  overflow-y: auto;
  min-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #b4915f transparent;
}

.menu::-webkit-scrollbar {
  width: 6px;
}

.menu::-webkit-scrollbar-track {
  background: transparent;
}

.menu::-webkit-scrollbar-thumb {
  background-color: #b4915f;
  border-radius: 3px;
}

.menu ul {
  list-style: none;
  padding: 0;
}

.menuItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.8rem 1rem;
  border-radius: 10px; /* Bordes redondeados */
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
}

.menuItem:hover {
  background-color: #333; /* Fondo más claro al pasar el cursor */
}

.active {
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #232429;
}

.icon {
  color: #fff; /* Color blanco para los iconos */
}

.iconActive {
  color: #232429;
}


.companySelector {
  margin-top: 2rem;
}

.sectionTitle {
  font-weight: bold;
  margin-bottom: 0.5rem;
  font-size: 15px;
}

.companyList {
  list-style: none;
  padding-left: 0;
  font-size: 12px;
  margin-left: 0.5rem;
  font-weight: bold;
}

.companyItem {
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.companyItem:hover {
  background-color: #333;
}

.activeCompany {
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #232429;
}

.profile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #333; /* Línea superior */
  padding-top: 1rem;
  margin-top: 2rem;
  gap: 1rem;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow: hidden;
}

.userInfo p {
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 1;
}

.logoutButton {
  background: none;
  border: none;
  color: #fff; /* Blanco para el icono */
  cursor: pointer;
  transition: color 0.3s ease;
}

.logoutButton:hover {
  color: #b4915f; /* Dorado al pasar el cursor */
}

@media screen and (min-width: 1150px) {
  .sidebar {
    display: flex; /* Mostrar sidebar en pantallas grandes */
  }
}