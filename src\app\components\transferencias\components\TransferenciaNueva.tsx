
'use client'
import { useEffect, useState } from 'react'
import styles from '../../styles/Transaction.module.css'
import TransactionsForm from './TransactionsForm'
import Dispersions from './Dispersions'
import dispersionsData from '@/app/components/data/transactions_data.json'
import { Transactions } from '@/types/types'
import ModalOTPTransfer from '../../modals/alertModals/ModalOTPTransfer'
import ModalOTPTransferFailed from '../../modals/alertModals/ModalOTPTransferFailed'
import useFormValidationTransaction, {
  TransactionFormData,
} from '@/hooks/useFormValidationTransaction'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { canSubmitTransfer, canViewTransfers } from '@/permissions/access'
import { useContactStore } from '@/store/contact/useContactStore'
import { toast } from 'react-toastify'
import { useTransfersStore } from '@/store/transfers/useTransfersStore'
import LoaderFull from '../../loader/LoaderFull'
import { TransferWithDockData } from '@/types/transfers/types'
import { generateTransferOTPCode, verifyOTPCode } from '@/api/endpoints/user'
import { useAccountStore } from '@/store/account/useAccountStore'

type Props = {
  transferSavedData?: TransactionFormData
  showSwitch?: boolean
  onCompleteTransfer?: () => void
  isConveniaAdmin?: boolean
  amountTransfer?: number | null
}

const TransferenciaNueva: React.FC<Props> = ({
  transferSavedData,
  showSwitch,
  onCompleteTransfer,
  isConveniaAdmin,
  amountTransfer,
}) => {
  const router = useRouter()
  const {
    formData,
    errors,
    touched,
    handleInputChange,
    handleSwitchChange,
    handleBlur,
    // validate,
    // resetForm,
    isFormValid,
    setFormData,
  } = useFormValidationTransaction()

  const { user } = useAuthStore()
  const { adminsByUserId, fetchAdminsByUserId, loading: loadingAdmins } = useAdminStore()
  const {
    banks,
    getBanks,
    transferWithDock,
    setTransferFormData,
  } = useTransfersStore()
  const [openModal, setOpenModal] = useState(false)
  const [openModalFailed, setOpenModalFailed] = useState(false)
  const { getUserRoleName } = useAuthStore()
  const [saveContact, setSaveContact] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [emailEnterpriseOut, setEmailEnterpriseOut] = useState<string>('')
  const [bankLegalCode, setBankLegalCode] = useState<string>('')
  const [tokenState, setTokenState] = useState<string | null>(null)
  const [isTransfering, setIsTransferring] = useState(false)
  const { accountDetails, fetchAccountDetails, clearAccountDetails } = useAccountStore()

  const sendOTPCode = async (email: string) => {
    try {
      const response = await generateTransferOTPCode(email)
      setTokenState(response.data.token)
    } catch (error) {
      console.error('Error sending OTP code:', error)
      toast.error('Error al enviar el código OTP', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    }
  }

  const handleModalContinue = async (otpCode: string) => {
    const response = await verifyOTPCode({
      email: user!.email,
      code: otpCode,
      access: tokenState ?? '',
    })
    if (response.statusCode === 200) {
      await handleTransfer()
      setOpenModal(false)
    } else {
      setOpenModalFailed(true)
      toast.error('Código OTP inválido. Intenta nuevamente.', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    }
  }

  const { addContact } = useContactStore()

  const handleTransfer = async () => {
    const data: TransferWithDockData = {
      num_clabe: formData.cuentaDestino,
      email: emailEnterpriseOut,
      amount: Number(formData.importe), // Asegúrate de que importe esté en número
      description: formData.conceptoBeneficiario,
      beneficiaryName: formData.nombreBeneficiario,
    }

    if (formData.tipoTransferencia === 'SPEI') {
      data.legalBank = bankLegalCode
      data.NameBank = formData.banco
    }

    setIsTransferring(true)
    try {
      const response = await transferWithDock(data) // respuesta directa
      setTransferFormData({
        ...formData,
        tipoTransferencia: formData.tipoTransferencia === 'SPEI' ? 'SPEI' : 'CONVENIA',
        numeroReferencia: response?.reference || '',
        operationFolio: response?.external_transaction_id || '',
        trackingKey: response?.transaction_id || '',
        commission: response?.commission || 0,
      })
      setIsTransferring(false)
      router.replace(`/transferencias/transferencia-exitosa?id=${response?.transaction_id}`)
      onCompleteTransfer?.()
    } catch (err) {
      setIsTransferring(false)
      setOpenModalFailed(true)
      toast.error('Error en la transferencia')
      console.error(err)
    } finally {
      setOpenModal(false)
    }
  }

  const handleSwitchAndSaveContact = async (checked: boolean) => {
    handleSwitchChange(checked)
    setSaveContact(checked)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isFormValid()) {
      toast.error('El formulario tiene errores. Revisa los campos obligatorios.', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
      return
    }
    setIsSubmitting(true)

    try {
      // Guardar contacto si el switch está activado
      if (saveContact && user) {
        await addContact({
          name: formData.nombreBeneficiario,
          num_clabe: String(formData.cuentaDestino),
          bank_institution: formData.banco,
          rfc: formData.rfcBeneficiario,
          email: formData.correo || '',
          userId: user.id,
        })
      }
      await sendOTPCode(user!.email)
      setOpenModal(true)
    } catch {
      toast.error('Ocurrió un error durante la transferencia', {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSelectCompany = async (value: string) => {
    const selectedAdmin = adminsByUserId.find(admin => admin.alias === value)
    if (!selectedAdmin) {
      setEmailEnterpriseOut('')
      return
    }
    setEmailEnterpriseOut(selectedAdmin?.manager.email)
  }

  const validForm = isFormValid()

  useEffect(() => {
    if (transferSavedData) {
      setFormData(transferSavedData)
    }
  }, [setFormData, transferSavedData])

  useEffect(() => {
    const fetchAdmins = async () => {
      if (user) {
        await fetchAdminsByUserId({ userId: user.id })
      }
    }
    fetchAdmins()
  }, [user, fetchAdminsByUserId])

  useEffect(() => {
    fetchAccountDetails(emailEnterpriseOut)
  }, [emailEnterpriseOut, fetchAccountDetails])

  useEffect(() => {
    getBanks()
  }, [getBanks])

  useEffect(() => {
    return () => {
      clearAccountDetails()
    }
  }, [clearAccountDetails])

  if (isSubmitting || isTransfering || loadingAdmins) return <LoaderFull />

  return (
    <div style={{ marginTop: '-24px' }}>
      {canSubmitTransfer(getUserRoleName()) && (
        <>
          <div>
            <span className={styles.infoAmountToTransfer}>
              Saldo disponible: $
              {isConveniaAdmin && user?.email === emailEnterpriseOut
                ? amountTransfer?.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
                : accountDetails?.available_resource?.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
            </span>
            <p className={styles.subtitle}>
              Realiza tus transferencias, completa los datos requeridos:
            </p>
          </div>
          <TransactionsForm
            formData={formData}
            errors={errors}
            touched={touched}
            handleInputChange={handleInputChange}
            onToggleSwitch={handleSwitchAndSaveContact}
            handleBlur={handleBlur}
            isFormValid={validForm}
            showSwitch={showSwitch}
            onHandleSubmit={handleSubmit}
            adminsByUserId={adminsByUserId ?? []}
            banks={banks ?? []} // 👈 asegura que siempre sea un array
            emailEnterpriseOut={emailEnterpriseOut}
            setEmailEnterpriseOut={setEmailEnterpriseOut}
            setBankLegalCode={setBankLegalCode}
            isConveniaAdmin={isConveniaAdmin}
            amountTransfer={
              isConveniaAdmin && user?.email === emailEnterpriseOut
                ? amountTransfer
                : accountDetails?.available_resource
            }
            handleSelectCompany={handleSelectCompany}
            accountDetails={accountDetails}
          />
        </>
      )}
      {canViewTransfers(getUserRoleName()) && (
        <Dispersions data={dispersionsData as Transactions[]} adminsByUserId={adminsByUserId} />
      )}
      <ModalOTPTransfer
        open={openModal}
        error={false}
        loading={false}
        onClose={() => setOpenModal(false)}
        onContinue={handleModalContinue}
        onResendCode={() => sendOTPCode(user!.email)}
        accountCard={formData.cuentaDestino}
      />
      <ModalOTPTransferFailed
        open={openModalFailed}
        onClose={() => setOpenModalFailed(false)}
        onPressBtn={() => setOpenModalFailed(false)}
        accountCard={formData.cuentaDestino}
      />
    </div>
  )
}

export default TransferenciaNueva
