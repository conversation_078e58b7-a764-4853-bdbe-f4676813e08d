/* eslint-disable react-hooks/exhaustive-deps */
'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Header from '../../Header'
import ClientsGroup from './ClientsGroup'
import { FaArrowLeft } from 'react-icons/fa'
import styles from '../../styles/Clientes.module.css'
import Stats from '../../home/<USER>'
import ModalDeleteAlias from '../../modals/alertModals/ModalDeleteAlias'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { AdminClient } from '@/types/admin/types'
import LoaderFull from '../../loader/LoaderFull'

const Content = () => {
  const router = useRouter()

  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const [clientToDelete, setClientToDelete] = useState<AdminClient | null>(null)
  const [currentPage, setCurrentPage] = useState(1)

  const itemsPerPage = 5

  const {
    fetchClientAdminByGroupId,
    adminsByGroupId,
    adminCountByGroupId,
    adminClientSelected,
    amount,
    loading,
    fetchAmount,
    fetchAdminById,
  } = useAdminStore()

  const totalPages = Math.ceil(adminCountByGroupId / itemsPerPage)

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1)
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1)
  }

  useEffect(() => {
    fetchClientAdminByGroupId({
      limit: itemsPerPage,
      page: currentPage,
      q: '',
      orderBy: 'createdAt',
      groupId: adminClientSelected?.groupId,
    })
  }, [currentPage, fetchClientAdminByGroupId])

  useEffect(() => {
    if (adminClientSelected) {
      fetchAmount({ groupId: adminClientSelected.groupId })
    }
  }, [adminClientSelected, fetchAmount])

  const handleBack = () => router.push('/clientes')

  const handleOpenDelete = (client: AdminClient) => {
    setClientToDelete(client)
    setOpenDeleteModal(true)
  }

  const handleCloseDelete = () => {
    setOpenDeleteModal(false)
    setClientToDelete(null)
  }

  const handleConfirmDelete = () => {
    // lógica de borrado aquí
    handleCloseDelete()
  }

  const handleEditClient = (client: AdminClient) => {
    fetchAdminById(client.id)
    router.push(`/clientes/group-client-admin/edit-alias`)
  }

  const handleViewClient = (client: AdminClient) => {
    fetchAdminById(client.id)
    router.push(`/clientes/group-client-admin/view-alias`)
  }

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('es-MX', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`
  }

  return (
    <>
      <Header title={adminClientSelected?.companyName || 'Cliente no encontrado'} />

      <button className={styles.backButton} onClick={handleBack}>
        <FaArrowLeft size={16} className={styles.icon} /> Atrás
      </button>

      <Stats stats={[{ label: 'Saldo Total', value: formatCurrency(amount) }]} />

      <ClientsGroup
        clientData={adminsByGroupId}
        onDeleteClient={handleOpenDelete}
        onEditClient={handleEditClient}
        onViewClient={handleViewClient}
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevPage={handlePrevPage}
        onNextPage={handleNextPage}
        onNavigatePage={setCurrentPage}
      />

      <ModalDeleteAlias
        open={openDeleteModal}
        onClose={handleCloseDelete}
        onConfirm={handleConfirmDelete}
        name={clientToDelete?.alias || ''}
      />
      {loading && <LoaderFull />}
    </>
  )
}

export default Content
