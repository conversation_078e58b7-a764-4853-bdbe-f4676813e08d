import React, { useState, useRef, useEffect } from 'react'
import { FiCalendar } from 'react-icons/fi'
import styles from './styles/DateButton.module.css'

type Props = {
  onSelectedDate: (date: Date) => void
}

const DateButton: React.FC<Props> = ({ onSelectedDate }) => {
  const [showCalendar, setShowCalendar] = useState(false)
  // const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const calendarRef = useRef<HTMLDivElement>(null)

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const daysInMonth = new Date(year, month + 1, 0).getDate()
    const firstDayOfMonth = new Date(year, month, 1).getDay()
    return { daysInMonth, firstDayOfMonth }
  }

  const handleDateSelect = (day: number) => {
    const newDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
    onSelectedDate(newDate)
    setShowCalendar(false)
  }

  const changeMonth = (offset: number) => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + offset, 1))
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setShowCalendar(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const renderCalendar = () => {
    const { daysInMonth, firstDayOfMonth } = getDaysInMonth(currentMonth)
    const days = []
    const monthNames = [
      'Enero',
      'Febrero',
      'Marzo',
      'Abril',
      'Mayo',
      'Junio',
      'Julio',
      'Agosto',
      'Septiembre',
      'Octubre',
      'Noviembre',
      'Diciembre',
    ]

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className={styles.calendarDay}></div>)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(
        <div
          key={day}
          className={`${styles.calendarDay} ${styles.active}`}
          onClick={() => handleDateSelect(day)}
        >
          {day}
        </div>
      )
    }

    return (
      <div className={styles.calendar} ref={calendarRef}>
        <div className={styles.calendarHeader}>
          <button onClick={() => changeMonth(-1)}>&lt;</button>
          <span>
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </span>
          <button onClick={() => changeMonth(1)}>&gt;</button>
        </div>
        <div className={styles.calendarDays}>
          <div>Do</div>
          <div>Lu</div>
          <div>Ma</div>
          <div>Mi</div>
          <div>Ju</div>
          <div>Vi</div>
          <div>Sa</div>
          {days}
        </div>
      </div>
    )
  }

  return (
    <div className={styles.dateButtonContainer}>
      <button
        className={styles.dateButton}
        // onClick={() => setShowCalendar(!showCalendar)}
      >
        <FiCalendar color="#4A4B55" fontSize={20} />
        {/* {selectedDate 
          ? selectedDate.toLocaleDateString('es-ES')
          : 'Fechas'
        } */}
        Fechas
      </button>
      {showCalendar && renderCalendar()}
    </div>
  )
}

export default DateButton
