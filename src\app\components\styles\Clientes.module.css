.backButton {
  margin-left: 2%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  /* Espaciado entre el icono y el texto */
  align-self: flex-start;
  font-weight: 400;
  font-family: Inter;
  font-size: 14px;
  background-color: #fff;
  border: none;
  color: #000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backButton:hover {
  color: #a67c52;
}

.icon {
  color: #000;
  /* Color del icono */
  transition: color 0.3s ease;
}

.backButton:hover .icon {
  color: #a67c52;
  /* Cambio de color del icono al pasar el mouse */
}
