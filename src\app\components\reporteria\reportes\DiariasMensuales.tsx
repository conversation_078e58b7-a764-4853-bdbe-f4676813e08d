'use client'
import { useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { useTransactionsDailyMonthlyReport } from '@/hooks/reportes'
import { useAuthStore } from '@/store/auth/useAuthStore'
import ReportFilters from '@/app/components/ReportFilters'
import Select from '@/app/components/Select'
import DateFilter from '@/app/components/DateFilter'
import Input from '@/app/components/Input'
import { AdminBasic } from '@/types/admin/types'
import { AdminUserResponse } from '@/types/user/types'

const STATUS_OPTIONS = [
  { value: 'PENDIENTE', label: 'Pendiente' },
  { value: 'COMPLETADO', label: 'Completado' },
  { value: 'RECHAZADO', label: 'Rechazado' },
]

const TRANSACTIONS_OPTIONS = [
  { value: 'SPEI', label: 'SPEI' },
  { value: 'CONVENIA', label: 'CONVENIA' },
  { value: 'COMP<PERSON>', label: 'COMPRA' },
]

interface TransactionDailyMonthlyReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
  admins: AdminBasic[]
  users: AdminUserResponse[]
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
}

interface DiariasMensualesFiltersParams {
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
  admins: AdminBasic[]
  users: AdminUserResponse[]
}

function useDiariasMensualesFilters({
  isClient,
  selectedAdminId,
  onAdminChange,
  users,
  admins,
}: DiariasMensualesFiltersParams) {
  const [dateClearKey, setDateClearKey] = useState(0)
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    status: '',
    transactionType: '',
    amount: '',
    adminId: selectedAdminId || '',
    userId: '',
  })

  const activeFilters = useMemo(() => {
    const list: string[] = []

    list.push(
      `Empresa: ${admins.find(a => a.id === filters.adminId)?.alias || 'Todas las empresas'}`
    )

    list.push(
      `Usuario: ${filters.userId ? users.find(u => u.id === filters.userId)?.name || filters.userId : 'Todos los usuarios'}`
    )

    list.push(
      `Estado: ${filters.status ? STATUS_OPTIONS.find(opt => opt.value === filters.status)?.label || filters.status : 'Todos los estados'}`
    )

    list.push(
      `Tipo: ${filters.transactionType ? TRANSACTIONS_OPTIONS.find(opt => opt.value === filters.transactionType)?.label || filters.transactionType : 'Todos los tipos'}`
    )

    if (filters.amount) list.push(`Monto: ${filters.amount}`)

    if (filters.startDate && filters.endDate) {
      const start = new Date(filters.startDate).toLocaleDateString('es-MX')
      const end = new Date(filters.endDate).toLocaleDateString('es-MX')
      list.push(`Fecha inicio: ${start} – Fecha fin: ${end}`)
    }

    return list
  }, [filters, admins, users])

  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      setFilters(prev => ({ ...prev, startDate: '', endDate: '' }))
      return
    }

    if (dates.length === 1) {
      setFilters(prev => ({ ...prev, startDate: dates[0], endDate: dates[0] }))
    } else if (dates.length === 2) {
      setFilters(prev => ({ ...prev, startDate: dates[0], endDate: dates[1] }))
    }
  }

  const resetFilters = () => {
    if (isClient) {
      onAdminChange(selectedAdminId)
      setFilters({
        startDate: '',
        endDate: '',
        status: '',
        transactionType: '',
        amount: '',
        adminId: selectedAdminId,
        userId: '',
      })
    } else {
      onAdminChange('')
      setFilters({
        startDate: '',
        endDate: '',
        status: '',
        transactionType: '',
        amount: '',
        adminId: '',
        userId: '',
      })
    }
    setDateClearKey(k => k + 1)
  }

  useEffect(() => {
    if (!selectedAdminId || admins.length === 0) return
    setFilters(prev => ({ ...prev, adminId: selectedAdminId }))
  }, [selectedAdminId, admins])

  return {
    filters,
    setFilters,
    activeFilters,
    handleDateChange,
    resetFilters,
    dateClearKey,
  }
}

const DiariasMensuales = ({
  onLoadingChange,
  admins,
  users,
  isClient,
  selectedAdminId,
  onAdminChange,
}: TransactionDailyMonthlyReportProps) => {
  const { user } = useAuthStore()
  const { isLoading, loadingMessage, downloadReport } = useTransactionsDailyMonthlyReport()
  const { filters, setFilters, activeFilters, handleDateChange, resetFilters, dateClearKey } =
    useDiariasMensualesFilters({
      isClient,
      selectedAdminId,
      onAdminChange,
      admins,
      users,
    })

  /** Maneja cambios de input/select */
  const eventInput = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (isClient && e.target.name === 'adminId') return
    if (e.target.name === 'adminId') {
      onAdminChange(e.target.value)
    }
    setFilters(prev => ({ ...prev, [e.target.name]: e.target.value }))
  }

  /** Descarga de reporte */
  const handleDownloadReport = async () => {
    if (!filters.startDate || !filters.endDate) {
      toast.error('El rango de fechas es obligatorio para descargar el reporte.', {
        position: 'top-right',
        autoClose: 3000,
      })
      return
    }
    await downloadReport({ ...filters, email: user?.email ?? '' })
    resetFilters()
  }

  /** Loading del padre */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onReset={resetFilters}
      onSubmit={e => e.preventDefault()}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Transacciones Diarias/Mensuales"
      downloadOptions={{
        onlyExcel: true,
        onExcelDownload: handleDownloadReport,
      }}
    >
      {/* Tipo de transacción */}
      <Select
        label="Tipo de transacción"
        name="transactionType"
        value={filters.transactionType}
        onChange={eventInput}
      >
        <option value="">Todos los tipos</option>
        {TRANSACTIONS_OPTIONS.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Select>

      {/* Estado */}
      <Select
        label="Estado de transacción"
        name="status"
        value={filters.status}
        onChange={eventInput}
      >
        <option value="">Todos los estados</option>
        {STATUS_OPTIONS.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Select>

      {/* Monto */}
      <Input
        label="Monto de transacción"
        onChange={eventInput}
        value={filters.amount}
        type="text"
        name="amount"
        placeholder="Escribe monto"
        onInput={(e: React.ChangeEvent<HTMLInputElement>) =>
          (e.target.value = e.target.value.replace(/\D/g, ''))
        }
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault()
            // Lógica adicional si se desea forzar la búsqueda con Enter sin submit global
          }
        }}
      />

      {/* Empresa */}
      <Select
        name="adminId"
        label="Empresa"
        value={filters.adminId ?? ''}
        onChange={eventInput}
        disabled={isClient}
      >
        <option value="">Todas las empresas</option>
        {admins.map(a => (
          <option key={a.id} value={a.id}>
            {a.alias || a.companyName}
          </option>
        ))}
      </Select>

      {/* Usuario */}
      <Select
        name="userId"
        label="Usuario"
        value={filters.userId ?? ''}
        onChange={eventInput}
        disabled={!filters.adminId}
      >
        <option value="">Todos los usuarios</option>
        {users.map(u => (
          <option key={u.id} value={u.id}>
            {u.name} ({u.email})
          </option>
        ))}
      </Select>

      {/* Fecha */}
      <DateFilter
        onDateChange={handleDateChange}
        mode="range"
        clearTrigger={dateClearKey}
        label="Rango de fechas"
        placeholder="Selecciona el rango de fechas"
      />
    </ReportFilters>
  )
}

export default DiariasMensuales
