'use client'
import { MdAddCard, MdOutlineFileDownload } from 'react-icons/md'
import { LuTrash2 } from 'react-icons/lu'
import styles from '../../styles/AccountsTable.module.css'
import { CardsUsedTableProps, TableColumn } from '@/types/types'
import ToggleBlock from './ToggleBlock'
import Image from 'next/image'
import { GetCardResponse } from '@/types/account/types'
import { ROLES } from '@/constants/roles'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useCardsStore } from '@/store/cards/useCardsStore'
import Skeleton from '../../Skeleton'

const CardsUsedTable: React.FC<CardsUsedTableProps> = ({
  // accounts,
  onToggleCard,
  onCardReassignment,
  onDeleteCard,
}) => {
  const { cards: cardsData, loading } = useCardsStore()
  const { getUserRoleName } = useAuthStore()
  const columns: TableColumn<GetCardResponse>[] = [
    {
      key: 'user',
      label: 'Usuario',
      render: (item) => (
        <div className={styles.accountCell}>
          <Image src="/icon-card.svg" alt="Card Icon" width={60} height={35} />
          <div className={styles.accountInfo}>
            <strong>{item.user.name}</strong>
            {item.user.email && <span>{item.user.email}</span>}
          </div>
        </div>
      ),
    },
    {
      key: 'card',
      label: 'Número de tarjeta',
      render: (item) => `**** **** **** ${item.card.card_last_4}`,
    },
    {
      key: 'clabe',
      label: 'CLABE interbancaria',
      render: (item) => item.user.clabe,
    },
    {
      key: 'enterprise',
      label: 'Empresa',
      render: (item) => item.user.enterprise,
    },
    {
      key: 'toggle',
      label: 'Bloqueo de tarjeta',
      allowedRoles: [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR, ROLES.SOPORTE_CONVENIA, ROLES.CLIENTE_EMPRESA_ADMIN, ROLES.CLIENTE_TARJETAHABIENTES],
      render: (item) => (
        <ToggleBlock
          value={item.card.status === 'NORMAL'}
          onToggle={() =>
            onToggleCard(item.card.id, item.card.status, item.card.card_last_4)
          }
        />
      ),
    },
    {
      key: 'actions',
      label: 'Acciones',
      allowedRoles: [ROLES.ADMIN_CONVENIA, ROLES.ADMIN_CONVENIA_CONSULTOR, ROLES.SOPORTE_CONVENIA, ROLES.CLIENTE_EMPRESA_ADMIN, ROLES.CLIENTE_TARJETAHABIENTES, ROLES.CLIENTE_EMPRESA_TESORERO],
      render: (item) => (
        <div className={styles.actions}>
          <button disabled className={styles.actionButton}>
            <MdOutlineFileDownload size={24} />
          </button>
          {![ROLES.CLIENTE_EMPRESA_TESORERO].includes(getUserRoleName()! as typeof ROLES.CLIENTE_EMPRESA_TESORERO) && (
            <>
              <button
                className={styles.actionButton}
                onClick={() =>
                  onCardReassignment({
                    cardNumber: item.card.card_last_4,
                    expirationDate: item.card.expiration_date,
                    cable: item.user.clabe,
                    cvv: '',
                    status: item.card.status === 'NORMAL',
                    id: item.card.id,
                  })
                }
              >
                <MdAddCard size={24} />
              </button>
              <button
                className={styles.actionButton}
                onClick={() =>
                  onDeleteCard({
                    cardNumber: item.card.card_last_4,
                    expirationDate: item.card.expiration_date,
                    cable: item.user.clabe,
                    cvv: '',
                    status: item.card.status === 'NORMAL',
                    id: item.card.id,
                  })
                }
              >
                <LuTrash2 size={24} />
              </button>
            </>
          )}
        </div>
      ),
    },
  ]

  const visibleColumns = columns.filter(
    col => !col.allowedRoles || col.allowedRoles.includes(getUserRoleName()!)
  )

  return (
    <table className={styles.table} >
      <thead>
        <tr>
          {visibleColumns.map((col) => (
            <th key={col.key}>{col.label}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {loading ? (
          Array.from({ length: 1 }).map((_, rowIndex) => (
            <tr key={`skeleton-${rowIndex}`}>
              {visibleColumns.map((col) => {
                if (col.key === 'user') {
                  return (
                    <td key={col.key}>
                      <div className={styles.accountCell}>
                        <Skeleton width={60} height={35} circle />
                        <div className={styles.accountInfo}>
                          <Skeleton margin='0 0 5px' width={100} height={14} rounded />
                          <Skeleton width={140} height={12} rounded />
                        </div>
                      </div>
                    </td>
                  )
                }

                if (col.key === 'toggle') {
                  return (
                    <td key={col.key}>
                      <Skeleton margin='auto' width={140} height={20} rounded />
                    </td>
                  )
                }

                if (col.key === 'actions') {
                  return (
                    <td key={col.key}>
                      <div className={styles.actions}>
                        <Skeleton width={24} height={24} circle />
                        <Skeleton width={24} height={24} circle />
                        <Skeleton width={24} height={24} circle />
                      </div>
                    </td>
                  )
                }

                // default para texto tipo tarjeta, clabe, empresa
                return (
                  <td key={col.key}>
                    <Skeleton height={14} rounded />
                  </td>
                )
              })}
            </tr>
          ))
        ) : cardsData.length > 0 ? (
          cardsData.map((item, index) => (
            <tr key={index}>
              {visibleColumns.map((col) => (
                <td key={col.key}>{col.render(item)}</td>
              ))}
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={visibleColumns.length} className={styles.noResults}>
              No se encontraron tarjetas utilizadas
            </td>
          </tr>
        )}

      </tbody>
    </table>
  )

}

export default CardsUsedTable
