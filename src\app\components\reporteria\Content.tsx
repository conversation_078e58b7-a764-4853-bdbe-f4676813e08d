/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import Header from '../Header'
import Collapse from '../Collapse'
import styles from '../styles/Reporteria.module.css'
import {
  CardStatusReport,
  ConveniaMovementsReport,
  AmountsReport,
  DiariasMensuales,
  CostosComisiones,
  AliasHistoryReport,
  EmbossingBatchesReport,
  ServiceUsageReport,
  IssuedCardsReport,
  PeriodClosingsReport,
  CardTransactionsReport,
} from './reportes'
import { useState, useEffect, useCallback } from 'react'
import { useAdminStore } from '@/store/admin/useAdminStore'
import ModalLoading from '../modals/alertModals/ModalLoading'

// ¿Who can access reports? 🤷‍♂️
import {
  // 1 Reporte de Transacciones Diarias/Mensuales
  canAccessDailyMonthlyTransactionsReport,
  // 2 Reporte de Transacciones por Tarjeta - Pendiente
  canAccessCardTransactionsReport,
  // 3 Reporte de Saldos por Cuenta
  canAccessAccountBalancesReport,
  // 4 Reporte de Estado de Tarjetas
  canAccessCardStatusReport,
  // 5 Reporte de Nuevas Tarjetas Emitidas
  canAccessNewCardsIssuedReport,
  // 6 Reporte de Lotes de Embossing
  canAccessEmbossingBatchesReport,
  // 7 Reporte de Costos y Comisiones
  canAccessCostsCommissionsReport,
  // 8 Reporte de Creación y Desactivación de Alias Empresas
  canAccessCompanyAliasReport,
  // 9 Reporte de liquidaciones
  // canAccessSettlementsReport,
  // 10 Reporte de Movimientos CONVENIA
  canAccessConveniaMovementsReport,
  // Rporte de Cierres contables
  canAccessPeriodClosingsReport,
  // Reporte de uso de servicios
  canAccessServiceUsageReport,
  shouldSendAdminId,
} from '@/permissions/access'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import { RoleName } from '@/constants/roles'

const Content = () => {
  const { getUserRoleName, user } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()
  const roleName = getUserRoleName()
  const isClient = shouldSendAdminId(
    (user?.relUserRoleAdmins?.[selectedAdminIndex]?.role?.name as RoleName) || null
  )

  const store = useAdminStore()
  const { basicAdmins, adminUsers, fetchBasicAdmins, fetchAdminUsers } = store

  // Extraemos el adminId “crudo” de la store
  const rawInternalAdminId = user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.id
  const internalAdminId = rawInternalAdminId != null ? String(rawInternalAdminId) : ''

  // State manejado en Content
  const [selectedAdminId, setSelectedAdminId] = useState<string>(isClient ? internalAdminId : '')
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('')

  // Efecto para cargar administradores al montar el componente
  useEffect(() => {
    fetchBasicAdmins()
  }, [fetchBasicAdmins])

  // Efecto para cargar usuarios cuando se selecciona un administrador
  useEffect(() => {
    if (selectedAdminId) {
      fetchAdminUsers(selectedAdminId, { page: 1, limit: 1000 })
    }
  }, [selectedAdminId, fetchAdminUsers])

  useEffect(() => {
    if (isClient && internalAdminId && internalAdminId !== selectedAdminId) {
      setSelectedAdminId(internalAdminId)
    }
  }, [isClient, internalAdminId, selectedAdminId])

  const handleLoadingChange = useCallback((loading: boolean, msg?: string) => {
    setIsLoading(loading)
    setLoadingMessage(msg || '')
  }, [])

  return (
    <>
      <div className={styles.container}>
        <Header title="Reportería" />
        <p className={styles.label}>Elige el reporte que deseas descargar:</p>
        <section className={styles.content}>
          {canAccessCardStatusReport(roleName) && (
            <Collapse title="Estado de Tarjetas">
              <CardStatusReport
                isClient={!!isClient}
                selectedAdminId={selectedAdminId}
                onAdminChange={setSelectedAdminId}
                admins={basicAdmins}
                onLoadingChange={handleLoadingChange}
              />
            </Collapse>
          )}

          {canAccessConveniaMovementsReport(roleName) && (
            <Collapse title="Movimientos CONVENIA">
              <ConveniaMovementsReport
                isClient={!!isClient}
                selectedAdminId={selectedAdminId}
                onAdminChange={setSelectedAdminId}
                admins={basicAdmins}
                onLoadingChange={handleLoadingChange}
              />
            </Collapse>
          )}

          {canAccessAccountBalancesReport(roleName) && (
            <Collapse title="Saldos por Cuenta">
              <AmountsReport
                isClient={!!isClient}
                selectedAdminId={selectedAdminId}
                onAdminChange={setSelectedAdminId}
                admins={basicAdmins}
                onLoadingChange={handleLoadingChange}
                users={adminUsers}
              />
            </Collapse>
          )}

          {canAccessDailyMonthlyTransactionsReport(roleName) && (
            <Collapse title="Transacciones Diarias/Mensuales">
              <DiariasMensuales
                isClient={!!isClient}
                selectedAdminId={selectedAdminId}
                onAdminChange={setSelectedAdminId}
                admins={basicAdmins}
                onLoadingChange={handleLoadingChange}
                users={adminUsers}
              />
            </Collapse>
          )}

          {canAccessCostsCommissionsReport(roleName) && (
            <Collapse title="Costos y comisiones">
              <CostosComisiones onLoadingChange={handleLoadingChange} />
            </Collapse>
          )}

          {canAccessCompanyAliasReport(roleName) && (
            <Collapse title="Historial de Alias">
              <AliasHistoryReport onLoadingChange={handleLoadingChange} />
            </Collapse>
          )}

          {canAccessEmbossingBatchesReport(roleName) && (
            <Collapse title="Lotes de Embossing">
              <EmbossingBatchesReport onLoadingChange={handleLoadingChange} />
            </Collapse>
          )}

          {canAccessServiceUsageReport(roleName) && (
            <Collapse title="Uso de Servicios">
              <ServiceUsageReport onLoadingChange={handleLoadingChange} />
            </Collapse>
          )}

          {canAccessNewCardsIssuedReport(roleName) && (
            <Collapse title="Nuevas tarjetas">
              <IssuedCardsReport
                isClient={!!isClient}
                selectedAdminId={selectedAdminId}
                onAdminChange={setSelectedAdminId}
                admins={basicAdmins}
                onLoadingChange={handleLoadingChange}
              />
            </Collapse>
          )}

          {canAccessPeriodClosingsReport(roleName) && (
            <Collapse title="Cierres contables">
              <PeriodClosingsReport onLoadingChange={handleLoadingChange} />
            </Collapse>
          )}

          {canAccessCardTransactionsReport(roleName) && (
            <Collapse title="Transacciones por Tarjeta">
              <CardTransactionsReport
                isClient={!!isClient}
                selectedAdminId={selectedAdminId}
                onAdminChange={setSelectedAdminId}
                admins={basicAdmins}
                onLoadingChange={handleLoadingChange}
              />
            </Collapse>
          )}
        </section>
      </div>
      <ModalLoading open={isLoading} title={loadingMessage || 'Generando reporte...'} />
    </>
  )
}

export default Content
