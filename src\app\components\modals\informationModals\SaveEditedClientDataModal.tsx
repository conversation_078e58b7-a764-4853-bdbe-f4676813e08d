// este modal se muestra cuando se guardan los datos de un nuevo cliente
import React from 'react'
import InfoModal from '../InfoModal'
import { useRouter } from 'next/navigation'
import { BaseModalProps } from '@/types/types'

const SaveEditedClientDataModal: React.FC<BaseModalProps> = ({ open, onClose }) => {
  const router = useRouter()
  return (
    <InfoModal
      title="Datos editados correctamente"
      message="Hemos recibido y almacenado los datos correctamente. "
      open={open}
      onPressPrimaryBtn={() => {
        router.push('/home')
        onClose()
      }}
      onClose={onClose}
    />
  )
}

export default SaveEditedClientDataModal
