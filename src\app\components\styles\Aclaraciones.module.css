.actionsTableContainer {
  width: 100%;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-direction: column-reverse;
  align-items: flex-start;
  margin-top: 32px;
}

/* acalracion */

.frame {
  margin-top: 32px;
}

.textArea {
  /* Input */

  box-sizing: border-box;

  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 10px 14px;
  gap: 8px;

  width: 100%;
  height: 96px;

  background: #ffffff;
  border: 1px solid #d0d5dd;
  /* Shadows/shadow-xs */
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
  border-radius: 8px;
  resize: none;
}

.buttonsContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 24px;
}

.label {
  margin-top: 24px;

  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;

  color: #4a4b55;
}

.labelEvidencia {
  margin-top: 40px;
}

.statusContainer {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-top: 36px;
}

.statusItem {
  display: flex;
  width: 126px;
  justify-content: space-between;
  align-items: center;
}

.statusText {
  /* Text md/Regular */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  /* identical to box height, or 150% */

  color: #000000;
}

.filesWrapper {
  display: flex;
  margin-top: 16px;
  margin-bottom: 24px;
  width: 100%;
  gap: 24px;
  overflow-x: scroll;
  justify-content: start;
  align-items: center;
}

.textAreaAlertContent {
  margin-top: 16px;
  position: relative;
  z-index: 10;
}

.labelTextAreaAlertContent {
  /* Text sm/Medium */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */

  color: #344054;
  margin-bottom: 6px;
}


@media screen and (min-width: 650px) {
  .actionsTableContainer {
  	flex-direction: row;
  }

  .filesWrapper {
  justify-content: center;
  }
}
