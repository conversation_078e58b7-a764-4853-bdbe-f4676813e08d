import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

type Props = BaseModalProps & {
  onPressBtn: () => void
  aliasContacto: string
}

const ModalDeleteContactTransfer: React.FC<Props> = ({
  open,
  onClose,
  onPressBtn,
  aliasContacto,
}) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title={`¿Estás seguro de que deseas eliminar este contacto ${aliasContacto}?`}
      message="Esta acción no se puede deshacer."
      onClose={onClose}
      onPressBtn={onPressBtn}
    />
  )
}

export default ModalDeleteContactTransfer
