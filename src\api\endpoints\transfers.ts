/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  BulkTransferData,
  BulkTransferError,
  BulkTransferResponse,
  GetBanksResponse,
  TransferWithDockData,
  TransferWithDockResponse,
} from '@/types/transfers/types'
import apiClient from '../client'

export const transfer = async (data: TransferWithDockData): Promise<TransferWithDockResponse> => {
  const response = await apiClient.post('/transfer/dock', data)
  return response.data
}

export const getBanks = async (): Promise<GetBanksResponse> => {
  const response = await apiClient.get('/transfer-orders/get-banks')
  return response.data
}

export const getTransferOrderById = async (id: string): Promise<TransferWithDockResponse> => {
  const response = await apiClient.get(`/transfer-orders/get-transfer-by-id/${id}`)
  return response.data
}

// Massive Transfer
export const uploadFile = async (file: File): Promise<{ fileId: string }> => {
  const formData = new FormData()
  formData.append('file', file)

  const response = await apiClient.post('/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })

  return response.data
}

export const createBulkTransfer = async (
  fileId: string,
  data: BulkTransferData[]
): Promise<BulkTransferResponse & { httpStatus: number }> => {
  const response = await apiClient.post('/transfer/dock/bulk', {
    file: fileId,
    data,
  })

  return { ...response.data, httpStatus: response.status }
}

export const getBulkTransferById = async (
  id: string
): Promise<BulkTransferResponse & { file: { id: string; key: string; url: string } }> => {
  const response = await apiClient.get(`/transfer/dock/bulk/${id}`)
  return response.data
}

export const getUserBulkTransfers = async (
  userId: string,
  page = 1,
  limit = 10
): Promise<{ data: BulkTransferResponse[]; count: number }> => {
  const response = await apiClient.get(`/transfer/dock/user/${userId}`, {
    params: { page, limit },
  })
  return response.data
}

export const getBulkTransferErrors = async (bulkId: string): Promise<BulkTransferError[]> => {
  const response = await apiClient.get(`/transfer/dock/bulk/${bulkId}/errors`)
  return response.data
}
