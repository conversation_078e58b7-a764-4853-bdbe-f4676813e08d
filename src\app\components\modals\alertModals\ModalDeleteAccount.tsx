import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

interface Props extends BaseModalProps {
  accountNumber: string
  onConfirm: (accountNumber: string) => void
}

const ModalDeleteAccount: React.FC<Props> = ({ open, onClose, accountNumber, onConfirm }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title={`¿Estás seguro que deseas eliminar esta cuenta ${accountNumber}?`}
      onClose={onClose}
      onPressBtn={() => onConfirm(accountNumber)}
    />
  )
}

export default ModalDeleteAccount
