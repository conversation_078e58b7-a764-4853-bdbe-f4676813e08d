import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

interface ModalBlockProps extends BaseModalProps {
  cardLast4: string
  status: string
  onConfirm: () => void
}

const ModalCardLock: React.FC<ModalBlockProps> = ({
  cardLast4,
  open,
  onClose,
  onConfirm,
  status,
}) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title={`¿Estás seguro que deseas ${status === 'BLOCKED' ? 'desbloquear' : 'bloquear'} la tarjeta con número de tarjeta [**** **** **** ${cardLast4}]?`}
      onClose={onClose}
      onPressBtn={onConfirm}
    />
  )
}

export default ModalCardLock
