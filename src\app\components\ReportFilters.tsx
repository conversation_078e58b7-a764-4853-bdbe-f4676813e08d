import { HTMLProps } from 'react'
import styles from './styles/ReportFilters.module.css'
import { HiRefresh } from "react-icons/hi"
// import DateFilter from '../DateFilter'
import ButtonOutlined from './ButtonOutlined';

interface ReportFiltersProps extends HTMLProps<HTMLFormElement> {
  onReset: () => void;
  onSubmit: (e: React.FormEvent) => void;
  children: React.ReactNode;
  activeFilters?: string[];
  isLoading?: boolean;
  icon?: React.ReactNode;
  title?: string;
  disabled?: boolean;
  downloadOptions?: {
    onPdfDownload?: () => void;
    onExcelDownload?: () => void;
    onlyExcel?: boolean;
  };
}

const ReportFilters: React.FC<ReportFiltersProps> = ({
  onReset,
  onSubmit,
  children,
  activeFilters = [],
  isLoading = false,
  downloadOptions,
  disabled,
  ...props
}) => {
  return (
    <div className={styles.formContainer}>
      <h5 className={styles.label}>Configurar filtros para descargar tu reporte:</h5>
      <form onSubmit={onSubmit} {...props}>
        <ButtonOutlined
          text="Restablecer filtros"
          icon={<HiRefresh />}
          className={styles.resetButton}
          onClick={onReset}
          type="button"
          disabled={isLoading}
        />

        {activeFilters.length > 0 && (
          <div className={styles.formGroupButtons}>
            {activeFilters.map((filter, index) => (
              <label key={index} htmlFor="badge" className={styles.badge}>
                {filter}
              </label>
            ))}
          </div>
        )}

        <div className={styles.formGroup}>
          {children}
        </div>

        {downloadOptions && (
          <div className={styles.formGroupButtons}>
            {downloadOptions.onPdfDownload && (
              <ButtonOutlined
                text="Descargar en PDF"
                onClick={downloadOptions.onPdfDownload}
                type="button"
                disabled={disabled || isLoading}
              />
            )}
            {downloadOptions.onExcelDownload && (
              <ButtonOutlined
                text="Descargar en excel"
                onClick={downloadOptions.onExcelDownload}
                type="button"
                disabled={disabled || isLoading}
              />
            )}
          </div>
        )}
      </form>
    </div>
  )
}

export default ReportFilters
