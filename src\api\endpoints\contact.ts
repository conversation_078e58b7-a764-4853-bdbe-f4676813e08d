import apiClient from '@/api/client'
import { ContactFormData } from '@/types/contact/types'

export const saveContact = async (data: ContactFormData) => {
  const res = await apiClient.post('/contact/save', data)
  return res.data
}

export const getContactsByUser = async (userId: string, email: string) => {
  const res = await apiClient.get(`/contact/findByUser`, {
    params: { idUser: userId, email },
  })
  return res.data.data
}

export const updateContact = async (
  id: string,
  data: Partial<Omit<ContactFormData, 'email' | 'userId'>>
) => {
  const res = await apiClient.patch(`/contact/${id}`, data)
  return res.data
}

export const deleteContact = async (id: string) => {
  const res = await apiClient.delete(`/contact/${id}`)
  return res.data
}
