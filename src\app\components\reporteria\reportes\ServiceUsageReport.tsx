'use client'
import { useEffect, useState } from 'react'
import { MdQueryStats } from 'react-icons/md'
import ReportFilters from '@/app/components/ReportFilters'
import TextInput from '@/app/components/Input'
import { getServiceUsageReport } from '@/api/endpoints/reports'
import { downloadFileFromApi } from '@/utils/downloadFileFromApi'
import type { ServiceUsageReportParams } from '@/types/reports/types'

interface ServiceUsageReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
}

const ServiceUsageReport = ({ onLoadingChange }: ServiceUsageReportProps) => {
  const [debtorKey, setDebtorKey] = useState('')
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('')

  /** Descarga del reporte con burbujas */
  const handleDownload = async () => {
    try {
      setIsLoading(true)
      setLoadingMessage('Generando reporte de uso de servicios...')

      const params: ServiceUsageReportParams = {}
      const filters: string[] = []

      // Agrega debtorKey si existe
      if (debtorKey) {
        params.debtor_key = debtorKey
        filters.push(`Debtor key: ${debtorKey}`)
      }

      setActiveFilters(filters)

      // Llamada a la API
      const blob = await getServiceUsageReport(params)
      downloadFileFromApi(blob, 'uso_servicios.xlsx')
    } catch (err) {
      console.error('Error al generar el reporte:', err)
    } finally {
      setIsLoading(false)
      setLoadingMessage('')
    }
  }

  /** Manejo del loading global */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  /** Reset de filtros */
  const handleReset = () => {
    setDebtorKey('')
    setActiveFilters([])
  }

  /** Actualiza burbuja de Debtor Key dinámicamente */
  useEffect(() => {
    setActiveFilters(prev => {
      // Elimina cualquier burbuja anterior de Debtor Key
      const base = prev.filter(f => !f.startsWith('Debtor Key:'))

      // Si hay debtorKey, agrega la nueva burbuja actualizada
      return debtorKey ? [...base, `Debtor Key: ${debtorKey}`] : base
    })
  }, [debtorKey])

  return (
    <>
      <ReportFilters
        title="Uso de Servicios"
        icon={<MdQueryStats />}
        activeFilters={activeFilters}
        isLoading={isLoading}
        onSubmit={e => e.preventDefault()}
        onReset={handleReset}
        downloadOptions={{ onlyExcel: true, onExcelDownload: handleDownload }}
      >
        {/* Input estandarizado */}
        <TextInput
          label="Debtor key"
          placeholder="Escribe el debtor key"
          value={debtorKey}
          onChange={e => setDebtorKey(e.target.value)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              // Lógica adicional si se desea forzar la búsqueda con Enter sin submit global
            }
          }}
        />
      </ReportFilters>
    </>
  )
}

export default ServiceUsageReport
