import { FileUploadResponse } from '@/types/files/types'
import apiClient from '../client'

export const uploadFile = async (file: File): Promise<FileUploadResponse> => {
  const formData = new FormData()
  formData.append('file', file) // 'file' es la key que espera tu backend

  const response = await apiClient.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  return response.data
}

export const deleteFile = async (adminId: string, fileId: number): Promise<void> => {
  await apiClient.delete(`/admin/${adminId}/document/${fileId}`)
}
