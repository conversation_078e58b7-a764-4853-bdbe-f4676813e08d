/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { Suspense, useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Header from '@/app/components/Header'
import EditUserAdmin from './EditUserAdmin'
import EditedUserSuccess from '../../modals/informationModals/EditedUserSuccess'
import { ContentProps } from '@/types/types'
import { useUserStore } from '@/store/user/useUserStore'

const HomePage = () => {
  const router = useRouter()
  const [openModal, setOpenModal] = useState(false)
  const { clearSelected } = useUserStore()

  const handleCloseModal = () => {
    clearSelected()
    router.push(`/gestion-usuarios`)
  }

  return (
    <Suspense fallback={<p>Cargando...</p>}>
      <Content
        setOpenModal={setOpenModal}
        openModal={openModal}
        handleCloseModal={handleCloseModal}
      />
    </Suspense>
  )
}

const Content = ({ setOpenModal, openModal, handleCloseModal }: ContentProps) => {
  const { userSelected } = useUserStore()
  const router = useRouter()

  useEffect(() => {
    if (!userSelected) {
      router.push('/gestion-usuarios') // redirige si no hay data
    }
  }, [userSelected])

  if (!userSelected) {
    return <p style={{ padding: '1rem' }}>Cargando datos del usuario...</p>
  }

  return (
    <>
      <Header title={`Editar ${userSelected.name}`} />
      <EditUserAdmin onOpenModal={() => setOpenModal(true)} userData={userSelected} isEditing={true} />
      <EditedUserSuccess open={openModal} onClose={handleCloseModal} />
    </>
  )
}

export default HomePage
