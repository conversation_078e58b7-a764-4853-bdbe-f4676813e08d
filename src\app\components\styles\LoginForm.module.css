.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.logoContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.welcome {
  font-weight: 400;
  font-family: Inter;
  font-size: 36px;
  line-height: 43.57px;
  margin: 0;
  color: #232429;
}

.separator {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
}

.separator hr {
  flex: 1; /* Hace que las líneas se expandan */
  border: none; /* Elimina el borde por defecto */
  border-top: 1px solid #d6d8e8; /* Crea una línea horizontal */
}

.separator span {
  color: #666;
  font-family: Inter;
  font-size: 0.9rem;
  text-transform: uppercase; /* Opcional: Cambia el texto a mayúsculas */
}

.form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.inputsContainer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.inputGroup {
  text-align: left;
}

.inputGroup label {
  display: block;
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 16.94px;
  color: #000;
  background-color: white;
  margin-bottom: 6px;
}

.inputGroup input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #d0d5dd;
  border-radius: 5px;
  font-weight: 400;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  color: #000000;
  background-color: #fff;
}

.inputGroup input:focus {
  outline: none;
  border-color: #e9d688; /* Color dorado */
  box-shadow: 0 0 5px rgba(180, 145, 95, 0.5);
}

.passwordWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.eyeButton {
  background: none;
  border: none;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  width: 16px;
  height: 16px;
  color: #000000;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eyeButton:focus {
  outline: none;
}

.eyeButton svg {
  width: 16px;
  height: 16px;
}

.error {
  color: red;
  font-size: 0.875rem;
  margin-top: 4px;
}
