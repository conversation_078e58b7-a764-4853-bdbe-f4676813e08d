.appbar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #000;
  color: #fff;
  display: flex;
  height: 60px;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  z-index: 1000;
}

.dropdown {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: calc(100vh - 60px);
  background: #000;
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 2;
  padding: 2rem 1.5rem;
  overflow-y: none;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE y Edge */
}

.dropdown::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.dropdown ul {
  list-style: none;
  padding: 0;
}

.menu {
  flex: 1;
  overflow-y: auto;
  min-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #b4915f transparent;
}

.menu::-webkit-scrollbar {
  width: 6px;
}

.menu::-webkit-scrollbar-track {
  background: transparent;
}

.menu::-webkit-scrollbar-thumb {
  background-color: #b4915f;
  border-radius: 3px;
}

.menuItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.8rem 1rem;
  border-radius: 10px; /* Bordes redondeados */
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
}

.menuItem:hover {
  background-color: #333; /* Fondo más claro al pasar el cursor */
}

.active {
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #232429;
}

.icon {
  color: #fff; /* Color blanco para los iconos */
}

.iconActive {
  color: #232429;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow: hidden;
}

.userInfo p {
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
  width: 100%;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 1;
}

.logoutButton {
  background: none;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-top: 2px solid gray !important;
  border: none;
  color: #fff; /* Blanco para el icono */
  cursor: pointer;
  transition: color 0.3s ease;
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
}

.companySelector {
  margin-top: 2rem;
  border-top: 1px solid #333;
  padding-top: 1rem;
}

.sectionTitle {
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  color: #fff;
  margin: 0 0 1rem 0.5rem;
}

.companyList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.companyItem {
  padding: 0.8rem 1rem;
  cursor: pointer;
  border-radius: 10px;
  transition: background-color 0.3s ease;
  font-weight: 600;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
  color: #fff;
}

.companyItem:hover {
  background-color: #333;
}

.activeCompany {
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #232429;
}

@media screen and (min-width: 1150px) {
  .appbar {
    display: none;
  }
}
