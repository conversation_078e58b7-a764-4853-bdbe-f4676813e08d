/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect, useRef } from 'react'
import * as XLSX from 'xlsx'
import styles from '../../styles/ModalMassiveLoad.module.css'
import { AiOutlineClose } from 'react-icons/ai'
import Image from 'next/image'
import Button from '../../Button'
import Modal from '../Modal'
import { massiveLoadValidationRules } from '@/utils/validationRules'
import { createMassiveAdmin } from '@/api/endpoints/admin'
import { getAdminByRFC } from '@/api/endpoints/admin'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { Cities, States } from '@/types/catalogs/types'
import { getCitiesByStateId, getStates } from '@/api/endpoints/catalogs'
import { useRouter } from 'next/navigation'
import MassiveLoadLoading from './MassiveLoadLoading'
import MassiveLoadError from './MassiveLoadError'

type MassiveLoadProps = {
  onClose: () => void
  open: boolean
}

type ClienteExcel = {
  'Nombre de la compañia (obligatorio)': string
  'Alias (obligatorio)': string
  'RFC (obligatorio)': string
  'Estado (obligatorio)': string
  'Ciudad (obligatorio)': string
  'Teléfono (obligatorio)': string | number
  'Correo electrónico (obligatorio)': string
  'Calle (obligatorio)': string
  'Código postal (obligatorio)': string | number
  'Número exterior (opcional)': string | number
  'Colonia (obligatorio)': string
  'Nombre del responsable (obligatorio)': string
  'Contraseña (obligatorio)': string
  'Spei in comision % (obligatorio)': string | number
  'Spei out comision $ (obligatorio)': string | number
  'Fondeo de tarjetas % (obligatorio)': string | number
  'Embajador (obligatorio)': string
}

const MassiveLoad = ({ onClose, open }: MassiveLoadProps) => {
  const user = useAuthStore(state => state.user)
  const router = useRouter()
  const [file, setFile] = useState<File | null>(null)
  const [fileError, setFileError] = useState<string | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const [loading, setLoading] = useState(false)

  const [estadosApi, setEstadosApi] = useState<States[]>([])
  const [ciudadesApi, setCiudadesApi] = useState<Cities[]>([])

  const [errorModalOpen, setErrorModalOpen] = useState(false)
  const [errorMessages, setErrorMessages] = useState<string[]>([])

  useEffect(() => {
    if (open) {
      getStates().then(setEstadosApi)
    }
  }, [open])

  useEffect(() => {
    if (open && estadosApi.length > 0) {
      Promise.all(estadosApi.map(e => getCitiesByStateId(String(e.id)))).then(results =>
        setCiudadesApi(results.flat())
      )
    }
  }, [open, estadosApi])

  useEffect(() => {
    if (!open) {
      setFile(null)
      setFileError(null)
    }
  }, [open])

  const handleDownloadFormat = () => {
    const link = document.createElement('a')
    link.href = '/formato-carga-masiva-clientes.xlsx'
    link.download = 'Formato_Carga_Masiva_Clientes.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0] || null
    const error = massiveLoadValidationRules.file(uploadedFile)

    if (error) {
      setFileError(error)
      setFile(null)
    } else {
      setFile(uploadedFile)
      setFileError(null)
    }
  }

  const handleDeleteFile = () => {
    setFile(null)
    setFileError(null)
    if (inputRef.current) {
      inputRef.current.value = '' // limpia el input de tipo file
    }
  }

  const handleLabelClick = () => {
    if (file) {
      const fileURL = URL.createObjectURL(file)
      const link = document.createElement('a')
      link.href = fileURL
      link.download = file.name // Aquí forzamos el nombre original
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      inputRef.current?.click()
    }
  }

  const handleUpload = async () => {
    if (!file) {
      setFileError('Debe seleccionar un archivo antes de cargar.')
      return
    }

    // Leer archivo
    let clientes: ClienteExcel[] = []
    if (file.name.endsWith('.csv')) {
      const text = await file.text()
      const workbook = XLSX.read(text, { type: 'string', codepage: 65001 })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      clientes = XLSX.utils.sheet_to_json(worksheet)
    } else {
      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      clientes = XLSX.utils.sheet_to_json(worksheet)
    }

    // Validar y mapear datos
    const mappedClients = await Promise.all(
      clientes.map(async (cliente: ClienteExcel) => {
        const estadoObj = estadosApi.find(
          e =>
            e.name.trim().toUpperCase() ===
            String(cliente['Estado (obligatorio)']).trim().toUpperCase()
        )
        const ciudadObj = ciudadesApi.find(
          c =>
            c.name.trim().toUpperCase() ===
            String(cliente['Ciudad (obligatorio)']).trim().toUpperCase()
        )
        const rawPhone = cliente['Teléfono (obligatorio)']
        const phoneClean = rawPhone ? String(rawPhone).replace(/\D/g, '') : ''
        const phoneNumber = phoneClean ? Number(phoneClean) : undefined

        // Check if RFC already exists
        const rfc = cliente['RFC (obligatorio)']
        let is_sucursal = false
        try {
          const existingAdmin = await getAdminByRFC(rfc)
          is_sucursal = true // Always set to true if RFC exists
        } catch (error) {
          // If no admin found with this RFC, is_sucursal remains false
          is_sucursal = false
        }

        return {
          company_name: cliente['Nombre de la compañia (obligatorio)'],
          alias: cliente['Alias (obligatorio)'],
          rfc: rfc,
          is_sucursal: is_sucursal,
          num_asigned_cards: 0,
          address: {
            state: estadoObj?.id ? String(estadoObj.id) : '',
            city: ciudadObj?.id ? String(ciudadObj.id) : '',
            street: cliente['Calle (obligatorio)'],
            zip_code:
              cliente['Código postal (obligatorio)'] !== undefined
                ? String(cliente['Código postal (obligatorio)'])
                : '',
            num_ext:
              cliente['Número exterior (opcional)'] !== undefined
                ? String(cliente['Número exterior (opcional)'])
                : '',
            colonia: cliente['Colonia (obligatorio)'],
          },
          user: {
            name: cliente['Nombre del responsable (obligatorio)'],
            email: cliente['Correo electrónico (obligatorio)'],
            phone: phoneNumber,
            password: cliente['Contraseña (obligatorio)'],
            created_by: user?.id,
          },
          spei_in: cliente['Spei in comision % (obligatorio)']
            ? parseFloat(String(cliente['Spei in comision % (obligatorio)']))
            : 0,
          spei_out: cliente['Spei out comision $ (obligatorio)']
            ? parseFloat(String(cliente['Spei out comision $ (obligatorio)']))
            : 0,
          ambassador: cliente['Embajador (obligatorio)'] || '',
          target_refound: cliente['Fondeo de tarjetas % (obligatorio)']
            ? parseFloat(String(cliente['Fondeo de tarjetas % (obligatorio)']))
            : 0,
        }
      })
    )

    const payload = {
      creatorEmail: user?.email || '',
      name: user?.name || '',
      admins: mappedClients,
    }

    setLoading(true)
    try {
      const response = await createMassiveAdmin(payload)

      if (response.failCount > 0) {
        const extractedErrors = response.errors.map((item, index) => {
          const msg = item?.error?.response?.message || 'Error desconocido'
          return `${msg}`
        })

        setErrorMessages(extractedErrors)
        setErrorModalOpen(true)
        return // evitamos continuar hacia el success
      }

      router.push('/clientes/clients-massive-success')
      onClose()
    } catch (err) {
      setErrorMessages(['Ocurrió un error inesperado al cargar los datos.'])
      setErrorModalOpen(true)
    } finally {
      setLoading(false)
    }
  }

  const handleCloseError = () => {
    setErrorModalOpen(false)
    setErrorMessages([])
    setFile(null)
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  if (loading) {
    return <MassiveLoadLoading open={true} title="Carga masiva de clientes" />
  }

  if (errorModalOpen) {
    return (
      <MassiveLoadError open={errorModalOpen} onClose={handleCloseError} errors={errorMessages} />
    )
  }

  return (
    <Modal open={open}>
      <div className={styles.modalOverlay}>
        <div className={styles.modalContainer}>
          <button className={styles.closeButton} onClick={onClose}>
            <AiOutlineClose />
          </button>
          <div className={styles.header}>
            <h2 className={styles.title}>Carga masiva de clientes</h2>
            <div className={styles.description}>
              <strong>Formato de los Campos:</strong>
              <ul>
                <li>
                  Teléfono de 10 dígitos, código postal de 5 dígitos, correo electrónico válido.
                </li>
              </ul>
              <strong>Datos Únicos:</strong>
              <ul>
                <li>Verifica que el nombre de la empresa, el correo y RFC no estén duplicados.</li>
              </ul>
              <strong>Confirmación de Contraseña:</strong>
              <ul>
                <li>Contraseñas deben coincidir y ser seguras.</li>
              </ul>
              <strong>Spei in comision y Fondeo de tarjetas:</strong>
              <ul>
                <li>Un valor entre 0 y 100</li>
              </ul>
              <strong>Estado y Ciudad:</strong>
              <ul>
                <li>
                  Utiliza exactamente los nombres de estado y ciudad que aparecen en la pestaña{' '}
                  <b>&quot;Estados y Ciudades&quot;</b> del archivo de ejemplo. Copia y pega los
                  nombres tal como están escritos para evitar errores.
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.actions}>
            <button className={styles.downloadButton} onClick={handleDownloadFormat}>
              Descargar formato
            </button>

            <div>
              <label
                className={!file ? styles.uploadContainer : styles.uploadedContainer}
                onClick={handleLabelClick}
                style={{ cursor: 'pointer' }}
              >
                <div className={styles.uploadIcon}>
                  <Image
                    src="/images.svg"
                    alt="Icono"
                    width={!file ? 24 : 60}
                    height={!file ? 24 : 60}
                  />
                </div>
                <div className={styles.uploadText} title={file?.name}>
                  {file ? file.name : 'Cargar archivo'}
                </div>
              </label>
              <input
                ref={inputRef}
                type="file"
                accept=".csv, .xls, .xlsx"
                style={{ display: 'none' }}
                onChange={handleFileUpload}
              />
              {fileError && <p className={styles.errorText}>{fileError}</p>}
              {file && (
                <button className={styles.deleteButton} onClick={handleDeleteFile}>
                  Eliminar archivo
                </button>
              )}
            </div>
            <Button text="Cargar" fullWidth onClick={handleUpload} />
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default MassiveLoad
