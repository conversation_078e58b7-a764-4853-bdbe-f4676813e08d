import { useState } from 'react';
import { getTransactionsDailyMonthlyReport } from '@/api/endpoints/reports';
import { useDownload } from '../useDownload';
import { DailyMonthlyReportParams } from '@/types/reports/types';

export const useTransactionsDailyMonthlyReport = () => {
  const [error, setError] = useState<string | null>(null);
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: (err) => setError(err.message),
    loadingMessage: 'Generando reporte de transacciones diarias/mensuales...'
  });

  const downloadReport = async (params: DailyMonthlyReportParams) => {
    const filename = `reporte-diaria-mensual-${new Date().toISOString().split('T')[0]}.xlsx`;
    await downloadExcel(
      () => getTransactionsDailyMonthlyReport(params),
      filename
    );
  };

  return {
    isLoading,
    loadingMessage,
    error,
    downloadReport
  };
};
