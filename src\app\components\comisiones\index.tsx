/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useState, useEffect } from 'react'
import Stats from '../home/<USER>'
import Header from '@/app/components/Header'
import CommissionsTable from './components/CommissionsTable'
import styles from '../styles/Commissions.module.css'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useUserStore } from '@/store/user/useUserStore'

const Content = () => {
  const [loadingBalance, setLoadingBalance] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [filter, setFilter] = useState({
    sort: 'ASC',
    transfer_way: 'IN',
  })
  const [limit] = useState(20)

  const totalBalance = useUserStore(state => state.totalBalance)
  const commissions = useUserStore(state => state.commissions)
  const totalCommissionsTransfer = useUserStore(state => state.totalCommissionsTransfer)

  const fetchUserCommissionBalance = useUserStore(state => state.fetchUserCommissionBalance)
  const fetchUserCommissionTransfers = useUserStore(state => state.fetchUserCommissionTransfers)

  const totalPages = Math.ceil(totalCommissionsTransfer / limit)

  const { getRoleType } = useAuthStore()
  const isConveniaUser = getRoleType() === 'CONVENIA'

  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoadingBalance(true)

        if (isConveniaUser) {
          return await Promise.all([
            fetchUserCommissionBalance().finally(() => setLoadingBalance(false))
          ])
        }
      } catch (error) {
        console.error('Error al cargar stats:', error)
      }
    }

    loadStats()
  }, [])

  useEffect(() => {
    if (isConveniaUser) {
      fetchUserCommissionTransfers({ page: currentPage, sort: filter.sort, transfer_way: filter.transfer_way})
    }
  }, [currentPage, filter.sort, filter.transfer_way])

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('es-MX', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`
  }

  const stats = [
    {
      label: 'Saldo total',
      value: formatCurrency(Number(totalBalance)) || 0,
      loading: loadingBalance,
    }
  ]

  return (
    <>
      <Header
        title="Cuenta comisiones"
      />
      <Stats stats={stats} />
      <div className={styles.content}>
        <p className={styles.title}>Movimientos de comisiones</p>
        {/* Tabla de comisiones */}
        <CommissionsTable
          commissions={commissions}
          filter={{
            ...filter,
            setFilter
          }}
          pagination={{
            totalPages,
            currentPage,
            setCurrentPage
          }}/>
      </div>
      
    </>
  )
}

export default Content
