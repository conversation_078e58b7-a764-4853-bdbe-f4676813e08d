import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

const ModalDeleteClarification: React.FC<BaseModalProps> = ({ open, onClose }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title="¿Estás seguro de que deseas eliminar esta aclaración?"
      message="Esta acción no se puede deshacer."
      onClose={onClose}
      onPressBtn={() => null}
    />
  )
}

export default ModalDeleteClarification
