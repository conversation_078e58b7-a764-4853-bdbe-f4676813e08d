<svg width="336" height="336" viewBox="0 0 336 336" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2780_218207" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="336" height="336">
<rect width="336" height="336" fill="url(#paint0_radial_2780_218207)"/>
</mask>
<g mask="url(#mask0_2780_218207)">
<circle cx="168" cy="168" r="47.5" stroke="#EAECF0"/>
<circle cx="168" cy="168" r="47.5" stroke="#EAECF0"/>
<circle cx="168" cy="168" r="71.5" stroke="#EAECF0"/>
<circle cx="168" cy="168" r="95.5" stroke="#EAECF0"/>
<circle cx="168" cy="168" r="119.5" stroke="#EAECF0"/>
<circle cx="168" cy="168" r="143.5" stroke="#EAECF0"/>
<circle cx="168" cy="168" r="167.5" stroke="#EAECF0"/>
</g>
<defs>
<radialGradient id="paint0_radial_2780_218207" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(168 168) rotate(90) scale(168 168)">
<stop/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
