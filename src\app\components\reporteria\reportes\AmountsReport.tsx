/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useEffect, useMemo, useState } from 'react'
import { BsCurrencyDollar } from 'react-icons/bs'
import { useAmountsReport } from '@/hooks/reportes/useAmountsReport'
import ReportFilters from '@/app/components/ReportFilters'
import Select from '@/app/components/Select'

import { AmountsReportFilters } from '@/types/reports/amounts'
import { AdminBasic } from '@/types/admin/types'
import { AdminUserResponse } from '@/types/user/types'

interface UseAmountReportFilters {
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
  admins: AdminBasic[]
  users: AdminUserResponse[]
}

function useAmountsReportFilters({
  isClient,
  selectedAdminId,
  onAdminChange,
  admins,
  users,
}: UseAmountReportFilters) {
  const [filters, setFilters] = useState<AmountsReportFilters>({
    adminId: selectedAdminId || undefined,
    userId: undefined,
  })

  const activeFilters = useMemo(() => {
    const list: string[] = []

    const adminLabel = filters.adminId
      ? admins.find(a => a.id === filters.adminId)?.alias ?? filters.adminId
      : 'Todas las empresas'
    list.push(`Empresa: ${adminLabel}`)

    const userLabel = filters.userId
      ? (() => {
        const u = users.find(u => u.id === filters.userId)
        return u ? `${u.name} (${u.email})` : filters.userId
      })()
      : 'Todos los usuarios'
    list.push(`Usuario: ${userLabel}`)

    return list
  }, [filters, admins, users])


  const handleFilterChange = (name: keyof AmountsReportFilters, value: string | undefined) => {
    if (isClient && name === 'adminId') return
    setFilters(prev => ({ ...prev, [name]: value }))
    if (name === 'adminId') {
      onAdminChange(value || '')
    }
  }

  /** Reset de filtros y burbujas */
  const handleReset = () => {
    if (isClient) {
      onAdminChange(selectedAdminId)
      setFilters({
        adminId: selectedAdminId,
        userId: undefined,
      })
    } else {
      onAdminChange('') // limpia selección en padre
      setFilters({
        adminId: undefined,
        userId: undefined,
      })
    }
  }

  useEffect(() => {
    if (!selectedAdminId || admins.length === 0) return
    setFilters(prev => ({ ...prev, adminId: selectedAdminId || undefined }))
  }, [selectedAdminId, admins])

  return {
    filters,
    activeFilters,
    handleFilterChange,
    handleReset
  }
}

interface AmountsReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
  admins: AdminBasic[]
  users: AdminUserResponse[]
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
}

const AmountsReport = ({
  admins,
  users,
  isClient,
  selectedAdminId,
  onAdminChange,
  onLoadingChange,
}: AmountsReportProps) => {
  const { isLoading, loadingMessage, downloadReport } = useAmountsReport()

  const {
    filters,
    activeFilters,
    handleFilterChange,
    handleReset
  } = useAmountsReportFilters({
    isClient,
    selectedAdminId,
    onAdminChange,
    admins,
    users,
  })

  const handleSubmit = (e: React.FormEvent) => e.preventDefault()

  /** Descarga de reporte */
  const handleDownload = async () => {
    const raw = {
      ...filters,
      ...(isClient ? { adminId: selectedAdminId } : {}),
    }
    const params = Object.fromEntries(
      Object.entries(raw).filter(([, v]) => v != null && v !== '')
    )
    await downloadReport(params)
  }

  /** loading en el padre */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onSubmit={handleSubmit}
      onReset={handleReset}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Saldos por Cuenta"
      icon={<BsCurrencyDollar />}
      downloadOptions={{ onlyExcel: true, onExcelDownload: handleDownload }}
    >
      {/* Empresa */}
      <Select
        name="adminId"
        label="Empresa"
        value={filters.adminId ?? ''}
        onChange={e => handleFilterChange('adminId', e.target.value)}
        disabled={isClient}
      >
        <option value="">Todas las empresas</option>
        {admins.map(a => (
          <option key={a.id} value={a.id}>
            {a.alias || a.companyName}
          </option>
        ))}
      </Select>

      {/* Usuario */}
      <Select
        name="userId"
        label="Usuario"
        value={filters.userId ?? ''}
        onChange={e => handleFilterChange('userId', e.target.value)}
        disabled={!filters.adminId}
      >
        <option value="">Todos los usuarios</option>
        {users.map(u => (
          <option key={u.id} value={u.id}>
            {u.name} ({u.email})
          </option>
        ))}
      </Select>
    </ReportFilters>
  )
}

export default AmountsReport
