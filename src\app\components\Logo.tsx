import Image from 'next/image'
import styles from './styles/Logo.module.css'

type LogoProps = {
  size?: 'small' | 'large'
}

const Logo: React.FC<LogoProps> = ({ size = 'large' }) => {
  const textSize = size === 'small' ? styles.smallText : styles.largeText

  return (
    <div className={styles.logo}>
      <Image
        src="/logo-convenia.svg"
        alt="CONVENIA"
        width={size === 'small' ? 100 : 150}
        height={size === 'small' ? 50 : 70}
      />
      <p className={textSize}>CONVENIA</p>
    </div>
  )
}

export default Logo
