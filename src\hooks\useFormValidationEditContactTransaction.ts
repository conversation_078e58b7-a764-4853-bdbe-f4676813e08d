import { useState } from 'react'
import { editContactValidationRules } from '@/utils/validationRules'

export type EditContactFormData = {
  cuentaDestino: string
  nombreDelBeneficiario: string
  rfcBeneficiario: string
  correo: string
  aliasName: string
}

export type EditContactErrors = Partial<Record<keyof EditContactFormData, string>>

const useFormValidationEditContactTransaction = (initialData?: Partial<EditContactFormData>) => {
  const [formData, setFormData] = useState<EditContactFormData>({
    cuentaDestino: initialData?.cuentaDestino || '',
    nombreDelBeneficiario: initialData?.nombreDelBeneficiario || '',
    rfcBeneficiario: initialData?.rfcBeneficiario || '',
    correo: initialData?.correo || '',
    aliasName: initialData?.aliasName || '',
  })

  const [errors, setErrors] = useState<EditContactErrors>({})
  const [touched, setTouched] = useState<Record<keyof EditContactFormData, boolean>>(
    {} as Record<keyof EditContactFormData, boolean>
  )

  // Validate a single field
  const validateField = (name: keyof EditContactFormData, value: string): string => {
    const validationFn = editContactValidationRules[name as keyof typeof editContactValidationRules]
    if (validationFn) {
      return validationFn(value)
    }
    return ''
  }

  // Validate all fields or specific fields
  const validate = (fieldsToValidate?: (keyof EditContactFormData)[]): boolean => {
    const newErrors: EditContactErrors = {}
    const fields =
      fieldsToValidate || (Object.keys(editContactValidationRules) as (keyof EditContactFormData)[])

    fields.forEach(field => {
      if (field in editContactValidationRules) {
        const value = formData[field] || ''
        const error = validateField(field, typeof value === 'string' ? value : String(value))
        if (error) {
          newErrors[field] = error
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle input changes with validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    const typedName = name as keyof EditContactFormData

    setFormData(prev => ({ ...prev, [typedName]: value }))

    // Mark field as touched
    setTouched(prev => ({ ...prev, [typedName]: true }))

    // Validate the field
    const error = validateField(typedName, value)
    setErrors(prev => ({ ...prev, [typedName]: error }))
  }

  // Mark a field as touched (for blur events)
  const handleBlur = (name: keyof EditContactFormData) => {
    setTouched(prev => ({ ...prev, [name]: true }))

    const value = formData[name] || ''
    const error = validateField(name, typeof value === 'string' ? value : String(value))
    setErrors(prev => ({ ...prev, [name]: error }))
  }

  // Reset the form to initial state or empty
  const resetForm = () => {
    setFormData({
      cuentaDestino: '',
      nombreDelBeneficiario: '',
      rfcBeneficiario: '',
      correo: '',
      aliasName: '',
    })
    setErrors({})
    setTouched({} as Record<keyof EditContactFormData, boolean>)
  }

  // Check if the form is valid
  const isFormValid = (): boolean => {
    // Create a temporary errors object without updating state
    const newErrors: EditContactErrors = {}
    const fields = Object.keys(editContactValidationRules) as (keyof EditContactFormData)[]

    fields.forEach(field => {
      if (field in editContactValidationRules) {
        const value = formData[field] || ''
        const error = validateField(field, typeof value === 'string' ? value : String(value))
        if (error) {
          newErrors[field] = error
        }
      }
    })

    return Object.keys(newErrors).length === 0
  }

  return {
    formData,
    errors,
    touched,
    setFormData,
    handleInputChange,
    handleBlur,
    validate,
    resetForm,
    isFormValid,
  }
}

export default useFormValidationEditContactTransaction
