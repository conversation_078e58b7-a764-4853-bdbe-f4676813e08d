'use client'
import { useRouter } from 'next/navigation'
import Button from '@/app/components/Button'
import ButtonOutlined from '@/app/components/ButtonOutlined'
import styles from '../../styles/OTPResetPassword.module.css'
import InputDigit from '@/app/components/InputDigit'
import { useEffect, useRef, useState } from 'react'
import GoBackButton from '../../GoBackButton'
import { generateOTPCode, verifyOTPCode } from '@/api/endpoints/user'
import { toast } from 'react-toastify'
import { useOtpStore } from '@/store/reset-password/useOtpStore'
import LoaderFull from '../../loader/LoaderFull'

const OTPResetPassword: React.FC = () => {
  const router = useRouter()
  const { otpData, setOtpData } = useOtpStore()
  const email = otpData?.email ?? ''
  const token = otpData?.token ?? ''

  // Estados internos
  const [loading, setLoading] = useState(false)
  const [code, setCode] = useState(['', '', '', ''])
  const [error, setError] = useState(false)
  const [tokenState, setTokenState] = useState<string | null>(token)

  const inputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ]

  useEffect(() => {
    if (!loading && inputRefs[0].current) {
      inputRefs[0].current.focus()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading])

  const handleCodeChange = (value: string, index: number) => {
    const newCode = [...code]
    newCode[index] = value
    setCode(newCode)

    if (value && index < 3) {
      inputRefs[index + 1].current?.focus()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'ArrowRight' && index < 3) {
      inputRefs[index + 1].current?.focus()
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs[index - 1].current?.focus()
    } else if (e.key === 'Backspace' && index > 0 && code[index] === '') {
      inputRefs[index - 1].current?.focus()
    } else if (e.key === 'Enter') {
      handleContinue()
    }
  }

  const handleContinue = async () => {
    setLoading(true)
    const otpCode = code.join('')
    try {
      const response = await verifyOTPCode({
        email: Array.isArray(email) ? email[0] : email,
        code: otpCode,
        access: tokenState ?? '',
      })

      if (response.statusCode === 200) {
        setError(false)
        router.push('/reset-password/new-password')
      } else {
        setError(true)
        setLoading(false)
      }
    } catch (error) {
      console.error('Error verifying OTP:', error)
      setError(true)
      setLoading(false) // Finaliza el estado de carga
    }
  }

  const handleResendCode = async () => {
    setLoading(true) // Inicia el estado de carga
    try {
      const response = await generateOTPCode(email)
      if (response.statusCode === 200) {
        const token = response.data.token
        setTokenState(token)
        setOtpData({ email, token })
        toast.success('El código ha sido reenviado exitosamente.', {
          position: 'top-right',
          autoClose: 3000,
          pauseOnHover: true,
          draggable: true,
        })
        setLoading(false)
      } else {
        console.error('Error reenviando el código')
        toast.error('Error al enviar el código.', {
          position: 'top-right',
          autoClose: 3000,
          pauseOnHover: true,
          draggable: true,
        })
        setLoading(false)
      }
    } catch (error) {
      console.error('Error al intentar reenviar el código:', error)
      setLoading(false) // Finaliza el estado de carga
    }
  }

  return (
    <div className={styles.container}>
      {loading && <LoaderFull />}
      <>
        <GoBackButton onPress={() => router.back()} />
        <div className={styles.headingParent}>
          <h1 className={styles.heading} style={{ fontWeight: 500 }}>
            Revisa tu correo
          </h1>
          <div className={styles.text}>
            <div className={styles.teHemosEnviado}>Te hemos enviado un correo a:</div>
            <div className={styles.teHemosEnviado}>{email}</div>
          </div>
        </div>
        <div className={styles.contentDescriptionWrapper}>
          <div className={styles.contentDescription}>
            Ingresa el código de verificación que se envió a tu correo electrónico.
          </div>
        </div>
        <div className={styles.codeWrapper}>
          <div className={styles.codeContainer}>
            <InputDigit
              value={code[0]}
              error={error}
              onChange={value => handleCodeChange(value, 0)}
              onKeyDown={e => handleKeyDown(e, 0)}
              inputRef={inputRefs[0]}
              autoFocus={true}
            />
            <InputDigit
              value={code[1]}
              error={error}
              onChange={value => handleCodeChange(value, 1)}
              onKeyDown={e => handleKeyDown(e, 1)}
              inputRef={inputRefs[1]}
            />
            <InputDigit
              value={code[2]}
              error={error}
              onChange={value => handleCodeChange(value, 2)}
              onKeyDown={e => handleKeyDown(e, 2)}
              inputRef={inputRefs[2]}
            />
            <InputDigit
              value={code[3]}
              error={error}
              onChange={value => handleCodeChange(value, 3)}
              onKeyDown={e => handleKeyDown(e, 3)}
              inputRef={inputRefs[3]}
            />
          </div>
          {error && <p className={styles.textError}>Código incorrecto</p>}
        </div>
        <div className={styles.footer}>
          <Button
            text="Continuar"
            fullWidth
            disabled={code.some(digit => digit === '') || loading}
            onClick={handleContinue}
          />
          <p className={styles.textEmail}>¿No has recibido el correo?</p>
          <ButtonOutlined
            text="Reenviar código"
            fullWidth
            onClick={handleResendCode}
            disabled={loading}
          />
          <div className={styles.text2}>
            <span>
              Si tienes algún problema con tu cuenta y ya intentaste reenviar el código, contacta a
              soporte técnico a este correo electrónico{' '}
            </span>
            <span className={styles.contactoconveniamx}><EMAIL></span>
          </div>
        </div>
      </>
    </div>
  )
}

export default OTPResetPassword
