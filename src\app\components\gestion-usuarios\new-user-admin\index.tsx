'use client'
import Header from '@/app/components/Header'
import NewUserAdmin from './NewUserAdmin'
import { useState } from 'react'
import RegisterUserSuccess from '@/app/components/modals/informationModals/RegisterUserSuccess'
import { useRouter } from 'next/navigation'
import LoaderFull from '../../loader/LoaderFull'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { ROLES } from "@/constants/roles"

const HomePage = () => {
  const router = useRouter()
  const [openModal, setOpenModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const { getUserRoleName } = useAuthStore()

  const roleName = getUserRoleName()

  const handleCloseModal = async () => {
    setOpenModal(false)
    router.push(`/gestion-usuarios`)
  }

  const title = roleName === ROLES.CLIENTE_EMPRESA_ADMIN ? "Crear nuevo usuario" : "Crear nuevo administrador"

  return (
    <>
      {isLoading && <LoaderFull />}
      <Header title={title} />
      <NewUserAdmin onOpenModal={() => setOpenModal(true)} isLoading={setIsLoading} />
      <RegisterUserSuccess open={openModal} onClose={handleCloseModal} />
    </>
  )
}

export default HomePage
