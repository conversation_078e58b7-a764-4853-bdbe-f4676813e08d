/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react'
import { <PERSON><PERSON>heck } from 'react-icons/lu'
import styles from '../../styles/MultiCompanySelector.module.css'
import { Errors } from '@/hooks/useFormValidationClient'
import MultiCompanySelectFields from './MultiCompanySelectFields'
import { useAdminStore } from '@/store/admin/useAdminStore'

// const companyOptions = ['Bimbo', 'Oxxo', 'Coca-Cola', 'Soriana']

interface MultiCompanySelectorProps {
  value: string[]
  enabled: boolean
  onChange: (companies: string[]) => void
  onToggle: (enabled: boolean) => void
  errors: Errors
}

export default function MultiCompanySelector({
  value,
  enabled,
  onChange,
  onToggle,
  errors,
}: MultiCompanySelectorProps) {
  const [companies, setCompanies] = useState<string[]>(
    value.length ? value.filter(v => v !== undefined) : ['']
  )
  const { fetchBasicAdmins, basicAdmins } = useAdminStore()

  const handleAddCompany = () => {
    const updated = [...companies, '']
    setCompanies(updated)
    onChange(updated)
  }

  const handleRemoveCompany = (index: number) => {
    const updated = companies.filter((_, i) => i !== index)
    setCompanies(updated)
    onChange(updated)
  }

  const handleCompanyChange = (index: number, value: string) => {
    const updated = [...companies]
    updated[index] = value
    setCompanies(updated)
    onChange(updated)
  }

  // traemos las compañias basicas del admin que seran companyOptions

  useEffect(() => {
    fetchBasicAdmins()
  }, [fetchBasicAdmins])

  useEffect(() => {
    if (!enabled) {
      setCompanies([]) // limpia el estado interno
      onChange([]) // notifica al padre que también limpie extraCompanies
    }
  }, [enabled])

  return (
    <div className={styles.container}>
      <label className={styles.label}>
        ¿Quieres habilitar esta cuenta para gestionar múltiples empresas?
      </label>
      <div className={styles.toggle}>
        <div className={styles.toggleLabel}>
          <span className={styles.labelText}>Sí</span>
          <button
            className={`${styles.toggleBtn} ${enabled ? styles.active : ''}`}
            onClick={() => onToggle(true)}
          >
            <LuCheck size={24} className={styles.checkIcon} />
          </button>
        </div>
        <div className={styles.toggleLabel}>
          <span className={styles.labelText}>No</span>
          <button
            className={`${styles.toggleBtn} ${!enabled ? styles.active : ''}`}
            onClick={() => onToggle(false)}
          >
            <LuCheck size={24} className={styles.checkIcon} />
          </button>
        </div>
      </div>

      {enabled && (
        <MultiCompanySelectFields
          companies={companies}
          companyOptions={basicAdmins}
          errors={errors}
          handleCompanyChange={handleCompanyChange}
          handleRemoveCompany={handleRemoveCompany}
          handleAddCompany={handleAddCompany}
        />
      )}
    </div>
  )
}
