/* eslint-disable react-hooks/exhaustive-deps */
'use user'
import styles from '../styles/RecentClients.module.css'
import UsersManagementTable from '../gestion-usuarios/UsersManagementTable'
import ModalDeleteAdmin from '../modals/alertModals/ModalDeleteAdmin'
import { useEffect, useState } from 'react'
import { useUserStore } from '@/store/user/useUserStore'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { shouldSendAdminId } from '@/permissions/access'
import { RoleName } from '@/constants/roles'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import LoaderFull from '../loader/LoaderFull'
import RecordsSelector from '../RecordsSelector'

function RecentUsers() {
  const [openModal, setOpenModal] = useState(false)
  const [selectedAdminName, setSelectedAdminName] = useState<string | null>(null)
  const [limit, setLimit] = useState(5)
  const { fetchConveniaUsers, deleteUser } = useUserStore()
  const { user } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()
  const [loadingDelete, setLoadingDelete] = useState(false)

  const handleOpenDeleteClientModal = (adminName: string) => {
    setSelectedAdminName(adminName)
    setOpenModal(true)
  }

  const handleCloseDeleteClientModal = () => {
    setOpenModal(false)
    setSelectedAdminName(null)
  }

  const roleName = user?.relUserRoleAdmins?.[selectedAdminIndex]?.role?.name || null
  const admin = shouldSendAdminId(roleName as RoleName | null)
    ? user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.id || null
    : null

  useEffect(() => {
    fetchConveniaUsers({ page: 1, limit, q: '', admin })
  }, [fetchConveniaUsers, admin, limit])

   const handleDeleteUser = async () => {
    if (!selectedAdminName) return
    try {
      setLoadingDelete(true)
      await deleteUser(selectedAdminName, admin)
      await fetchConveniaUsers({ page: 1, limit, q: '', admin })
      handleCloseDeleteClientModal()
      setLoadingDelete(false)
    } catch (error) {
      setLoadingDelete(false)
      console.error('Error al eliminar el usuario:', error)
    }
  }

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit)
  }

  return (
    <div className={styles.recentClients}>
      <div className={styles.header}>
        <div className={styles.title}>Usuarios recientes</div>
      </div>

      <UsersManagementTable handleDeleteUserModal={handleOpenDeleteClientModal} />
      <RecordsSelector 
        currentLimit={limit}
        onLimitChange={handleLimitChange}
      />
      <ModalDeleteAdmin
        open={openModal}
        onClose={handleCloseDeleteClientModal}
        name={selectedAdminName || ''}
        onConfirm={handleDeleteUser}
      />
      {
        (loadingDelete) && <LoaderFull />
      }
    </div>
  )
}

export default RecentUsers
