import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

interface ModalDeleteAliasProps extends BaseModalProps {
  name: string
  onConfirm: (name: string) => void
}

const ModalDeleteAlias: React.FC<ModalDeleteAliasProps> = ({ name, open, onClose, onConfirm }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title={`¿Estás seguro de que deseas eliminar este alias ${name}?`}
      message="Esta acción no se puede deshacer."
      onClose={onClose}
      onPressBtn={() => onConfirm(name)}
    />
  )
}

export default ModalDeleteAlias
