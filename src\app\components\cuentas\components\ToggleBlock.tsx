'use client'
import styles from '../../styles/ToggleBlock.module.css'

type Props = {
  value: boolean
  onToggle: () => void
}

const ToggleBlock = ({ value, onToggle }: Props) => {
  return (
    <div className={styles.container}>
      {!value && <span className={styles.label}>Bloqueado</span>}
      <div className={`${styles.toggle} ${value ? styles.active : ''}`} onClick={onToggle}>
        <div className={styles.circle} />
      </div>
      {value && <span className={styles.label}>Desbloqueado</span>}
    </div>
  )
}

export default ToggleBlock
