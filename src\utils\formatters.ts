export function formatCurrency(value: string): string {
  const number = parseFloat(value.replace(/[^0-9.]/g, ''))
  if (isNaN(number)) return ''
  return number.toLocaleString('es-MX', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2,
  })
}

export const formatDate = (isoDate: string): string => {
  const date = new Date(isoDate)
  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0') // Los meses son 0-indexados
  const year = date.getFullYear()
  return `${day}/${month}/${year}`
}

export const formatTime = (isoDate: string): string => {
  const date = new Date(isoDate)
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${hours}:${minutes}hrs`
}

export const formatDateForComparing = (isoDate: string): string => {
  const date = new Date(isoDate)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // Los meses son 0-indexados
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}` // Formato YYYY-MM-DD
}

export const formatClarificationDate = (dateString: string) => {
  const date = new Date(dateString)
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  }

  return `${new Intl.DateTimeFormat('es-MX', options).format(date)} hrs`
}
