/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import Stats from './Stats'
import StatsLink from './StatsLink'
import RecentClients from './RecentClients'
import { useEffect, useMemo, useState } from 'react'
import Header from '@/app/components/Header'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { useUserStore } from '@/store/user/useUserStore'
import { RoleName, ROLES } from '@/constants/roles'
import RecentUsers from './RecentUsers'
import LastAccountsCreated from './LastAccountsCreated'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'

type RenderTableByRoleProps = {
  userRole: RoleName | null
}

const RenderTableByRole = ({ userRole }: RenderTableByRoleProps) => {
  switch (userRole) {
    case ROLES.CLIENTE_EMPRESA_ADMIN:
      return <RecentUsers />
    case ROLES.CLIENTE_EMPRESA_TESORERO:
      return <LastAccountsCreated />
    default:
      return <RecentClients />
  }
}

const HomePage = () => {
  const [loadingAmount, setLoadingAmount] = useState(true)
  const [loadingAmountDispersado, setLoadingAmountDispersado] = useState(true)
  const [loadingDock, setLoadingDock] = useState(true)
  const [loadingCards, setLoadingCards] = useState(true)
  const [loadingAmountTransfer, setLoadingAmountTransfer] = useState(true)
  const [rol_type] = useState('convenia')

  const { selectedAdminIndex } = useAdminUiStore()
  const user = useAuthStore(state => state.user)

  const adminId =
    user?.relUserRoleAdmins[selectedAdminIndex]?.role?.type?.toLocaleLowerCase() === rol_type
      ? null
      : user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.id || null

  const managerId = user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.managerId || null;

  const balance_adminId = user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.id || null

  const amount = useAdminStore(state => state.amount)
  const fetchAmount = useAdminStore(state => state.fetchAmount)

  const amountDispersado = useAdminStore(state => state.amountDispersado)
  const fetchAmountDispersado = useAdminStore(state => state.fetchAmountDispersado)

  const amountTransfer = useAdminStore(state => state.amountTransfer)
  const fetchAmountTransfer = useAdminStore(state => state.fetchAmountTransfer)

  const totalAccountsDock = useUserStore(state => state.totalAccountsDock)
  const fetchTotalAccountsDock = useUserStore(state => state.fetchTotalAccountsDock)
  const fetchUserCredencialsBank = useUserStore(state => state.fetchUserCredencialsBank)
  const userCredentials = useUserStore(state => state.credencialsBank)

  const totalActiveCards = useAdminStore(state => state.totalActiveCards)
  const fetchTotalActiveCards = useAdminStore(state => state.fetchTotalActiveCards)

  const { getUserRoleName, getRoleType } = useAuthStore()
  const isConveniaUser = getRoleType() === 'CONVENIA'

  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoadingAmount(true)
        setLoadingDock(true)
        setLoadingCards(true)
        setLoadingAmountTransfer(true)

        if (isConveniaUser) {
          return await Promise.all([
            fetchAmount({ adminId: balance_adminId }).finally(() => setLoadingAmount(false)),
            fetchTotalAccountsDock(adminId).finally(() => setLoadingDock(false)),
            fetchAmountTransfer().finally(() => setLoadingAmountTransfer(false)),
            fetchAmountDispersado().finally(() => setLoadingAmountDispersado(false))
          ])
        }
        await Promise.all([
          fetchUserCredencialsBank(managerId).finally(() => setLoadingAmount(false)),
          fetchTotalAccountsDock(adminId).finally(() => setLoadingDock(false)),
          fetchTotalActiveCards(adminId).finally(() => setLoadingCards(false)),
          fetchAmount({ adminId: balance_adminId }).finally(() => setLoadingAmount(false)),
        ])
      } catch (error) {
        console.error('Error al cargar stats:', error)
      }
    }

    loadStats()
  }, [selectedAdminIndex])

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('es-MX', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`
  }

  const stats = [
    {
      label: isConveniaUser ? 'Cuentas' : 'Cuentas creadas',
      value: totalAccountsDock || 0,
      loading: loadingDock,
    },
    {
      label: isConveniaUser ? 'Saldo Total Transfer' : 'Tarjetas activas',
      value: isConveniaUser ? formatCurrency(amountTransfer || 0) : totalActiveCards || 0,
      loading: isConveniaUser ? loadingAmountTransfer : loadingCards,
    },
    {
      label: isConveniaUser ? 'Saldo Total Dock' : 'Saldo total',
      value: formatCurrency(amount || 0),
      loading: loadingAmount,
    },
    // Solo mostrar 'Total Dispersado' si es usuario Convenia
    ...(isConveniaUser ? [{
      label: 'Saldo Total Dispersado',
      value: formatCurrency(amountDispersado || 0),
      loading: loadingAmountDispersado,
    }] : []),
  ]

  const statsLinks = useMemo(() => {
    if (!userCredentials) return []
    return [
      {
        label: 'Número de Afiliación',
        value: userCredentials.member_ship,
      },
      {
        label: 'CLABE interbancaria',
        value: userCredentials.clabe,
      },
      {
        label: 'Número de cuenta',
        value: userCredentials.convenia_account,
      },
    ]
  }, [userCredentials])

  const filteredStats = stats.filter(
    stat => !(stat.label === 'Cuentas' && !stat.loading && (stat.value === 0 || stat.value === '0'))
  )
  return (
    <>
      <Header
        title="Home"
        userName={user?.relUserRoleAdmins[selectedAdminIndex]?.admin?.alias || 'Usuario'}
      />
      <Stats stats={filteredStats} />
      {!isConveniaUser && <StatsLink stats={statsLinks} />}
      <RenderTableByRole
        userRole={getUserRoleName()}
      />

    </>
  )
}

export default HomePage
