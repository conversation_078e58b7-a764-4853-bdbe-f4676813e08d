/**
 * Utilidad para mapear errores HTTP específicos a mensajes amigables para el usuario
 */

export interface ApiError {
  response?: {
    status: number
    statusCode?: number
    data?: {
      message?: string
      error?: string | object
      errors?: string[]
      statusCode?: number
      data?: {
        statusCode?: number
        message?: string
        error?: string
      }
    }
  }
  code?: string
  message?: string
  name?: string
  status?: number
}

/**
 * Mapea errores HTTP específicos a mensajes amigables para el usuario
 * @param error - Error capturado de la API
 * @param context - Contexto específico del error (ej: 'crear cliente', 'actualizar usuario')
 * @returns Mensaje de error amigable para mostrar al usuario
 */
export const getErrorMessage = (error: unknown, context: string = 'realizar la operación'): string => {
  // Verificar si es un error de Axios con respuesta HTTP
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as ApiError
    const status = axiosError.response?.status || axiosError.response?.statusCode || axiosError.status

    // Extraer mensaje de error de diferentes estructuras posibles del backend
    let message = ''
    const responseData = axiosError.response?.data

    if (responseData) {
      // Estructura estándar de ResponseUtil.error
      message = responseData.message || ''

      // Si no hay mensaje directo, buscar en la estructura anidada
      if (!message && responseData.data?.message) {
        message = responseData.data.message
      }

      // Si el error es un objeto, intentar extraer el mensaje
      if (!message && responseData.error) {
        if (typeof responseData.error === 'string') {
          message = responseData.error
        } else if (typeof responseData.error === 'object' && responseData.error !== null && 'message' in responseData.error) {
          message = (responseData.error as { message: string }).message
        }
      }
    }

    const errors = responseData?.errors || []
    const messageStr = typeof message === 'string' ? message : ''

    if (!status) {
      return `Error inesperado al ${context}. Por favor intenta más tarde.`
    }

    switch (status) {
      case 400:
        // Errores de validación o datos incorrectos
        if (messageStr.toLowerCase().includes('rfc')) {
          return 'El RFC proporcionado no es válido o ya está registrado.'
        }
        if (messageStr.toLowerCase().includes('email') || messageStr.toLowerCase().includes('correo')) {
          return 'El correo electrónico ya está registrado o no es válido.'
        }
        if (messageStr.toLowerCase().includes('phone') || messageStr.toLowerCase().includes('teléfono')) {
          return 'El número de teléfono no es válido.'
        }
        if (messageStr.toLowerCase().includes('password') || messageStr.toLowerCase().includes('contraseña')) {
          return 'La contraseña no cumple con los requisitos mínimos.'
        }
        if (messageStr.toLowerCase().includes('file') || messageStr.toLowerCase().includes('archivo')) {
          return 'Uno o más archivos no son válidos o están corruptos.'
        }
        if (messageStr.toLowerCase().includes('validation') || messageStr.toLowerCase().includes('validación')) {
          // Si hay errores específicos de validación, mostrarlos
          if (errors.length > 0) {
            return `Errores de validación: ${errors.join(', ')}`
          }
        }
        // Errores específicos del backend de Finberry
        if (messageStr.includes('Failed to find the alias core')) {
          return 'No se pudo encontrar el alias principal. Intenta nuevamente más tarde.'
        }
        if (messageStr.includes('Failed to find the physical card cvv') || messageStr.includes('Failed to find the virtual card cvv')) {
          return 'No se pudo obtener el CVV de la tarjeta. Intenta nuevamente más tarde.'
        }
        if (messageStr.includes('Failed to change the status of the dock card')) {
          return 'No se pudo cambiar el estado de la tarjeta. Intenta nuevamente más tarde.'
        }
        if (messageStr.includes('Failed to change the pin of the dock card') || messageStr.includes('Failed to find the pin of the dock card')) {
          return 'Error al gestionar el PIN de la tarjeta. Intenta nuevamente más tarde.'
        }
        if (messageStr.includes('Failed to list the dock cards') || messageStr.includes('Failed to delete the dock card')) {
          return 'Error al gestionar las tarjetas. Intenta nuevamente más tarde.'
        }
        return messageStr || 'Los datos proporcionados no son válidos. Por favor revisa la información.'
        
      case 401:
        // Errores específicos de autenticación
        if (messageStr.includes('Contraseña inválida')) {
          return 'La contraseña ingresada es incorrecta.'
        }
        if (messageStr.includes('Usuario eliminado')) {
          return 'Esta cuenta ha sido desactivada. Contacta al administrador.'
        }
        return 'Tu sesión ha expirado. Por favor inicia sesión nuevamente.'

      case 403:
        return `No tienes permisos para ${context}. Contacta al administrador.`
        
      case 404:
        return 'El recurso solicitado no fue encontrado.'
        
      case 409:
        // Conflictos - datos duplicados
        if (messageStr.toLowerCase().includes('rfc')) {
          return 'Ya existe un cliente registrado con este RFC.'
        }
        if (messageStr.toLowerCase().includes('email') || messageStr.toLowerCase().includes('correo')) {
          return 'Ya existe un usuario registrado con este correo electrónico.'
        }
        if (messageStr.toLowerCase().includes('phone') || messageStr.toLowerCase().includes('teléfono')) {
          return 'Ya existe un usuario registrado con este número de teléfono.'
        }
        // Errores específicos de tarjetas
        if (messageStr.includes('This card is already assigned to another user')) {
          return 'Esta tarjeta ya está asignada a otro usuario.'
        }
        if (messageStr.includes('This card has expired and is no longer valid')) {
          return 'Esta tarjeta ha expirado y ya no es válida.'
        }
        return messageStr || 'Ya existe un registro con esta información.'

      case 404:
        // Recursos no encontrados
        if (messageStr.includes('The user could not be found')) {
          return 'El usuario no pudo ser encontrado. Verifica la información proporcionada.'
        }
        if (messageStr.includes('The card was not found')) {
          return 'La tarjeta no fue encontrada. Verifica los detalles e intenta nuevamente.'
        }
        if (messageStr.includes('The account could not be found')) {
          return 'La cuenta no pudo ser encontrada. Verifica la información.'
        }
        return messageStr || 'El recurso solicitado no fue encontrado.'

      case 422:
        // Errores de validación específicos
        if (errors.length > 0) {
          return `Errores de validación: ${errors.join(', ')}`
        }
        return messageStr || 'Los datos proporcionados no cumplen con el formato requerido.'
        
      case 429:
        return 'Has realizado demasiadas solicitudes. Por favor espera un momento e intenta nuevamente.'
        
      case 500:
        return 'Error interno del servidor. Por favor intenta nuevamente más tarde.'
        
      case 502:
      case 503:
      case 504:
        return 'El servicio no está disponible temporalmente. Por favor intenta más tarde.'
        
      default:
        return messageStr || `Error del servidor (${status}). Por favor intenta nuevamente.`
    }
  }
  
  // Si es un error de red
  if (error && typeof error === 'object' && 'code' in error) {
    const networkError = error as { code: string; message?: string }
    if (networkError.code === 'NETWORK_ERROR' || networkError.message?.includes('Network Error')) {
      return 'Error de conexión. Verifica tu conexión a internet e intenta nuevamente.'
    }
    if (networkError.code === 'ECONNABORTED' || networkError.message?.includes('timeout')) {
      return 'La operación tardó demasiado tiempo. Por favor intenta nuevamente.'
    }
  }
  
  // Error genérico
  if (error && typeof error === 'object' && 'message' in error) {
    const genericError = error as { message: string }
    return genericError.message || `Ocurrió un error inesperado al ${context}. Por favor intenta más tarde.`
  }
  
  return `Ocurrió un error inesperado al ${context}. Por favor intenta más tarde.`
}

/**
 * Mensajes de error específicos para diferentes contextos
 */
export const ERROR_CONTEXTS = {
  CREATE_CLIENT: 'crear el cliente',
  UPDATE_CLIENT: 'actualizar el cliente',
  DELETE_CLIENT: 'eliminar el cliente',
  CREATE_USER: 'crear el usuario',
  UPDATE_USER: 'actualizar el usuario',
  DELETE_USER: 'eliminar el usuario',
  VALIDATE_RFC: 'validar el RFC',
  UPLOAD_FILE: 'subir el archivo',
  SEND_EMAIL: 'enviar el correo',
  VERIFY_OTP: 'verificar el código OTP',
  LOGIN: 'iniciar sesión',
  LOGOUT: 'cerrar sesión',
  FETCH_DATA: 'obtener los datos',
  SAVE_DATA: 'guardar los datos',
} as const

/**
 * Función específica para errores de creación de cliente
 * Usar para toast notifications simples
 */
export const getClientCreationErrorMessage = (error: unknown): string => {
  return getErrorMessage(error, ERROR_CONTEXTS.CREATE_CLIENT)
}

/**
 * Función específica para obtener mensajes de error de actualización de cliente
 * Usar para toast notifications simples
 */
export const getClientUpdateErrorMessage = (error: unknown): string => {
  return getErrorMessage(error, ERROR_CONTEXTS.UPDATE_CLIENT)
}

/**
 * Función específica para errores de validación de RFC
 * Usar para toast notifications simples
 */
export const getRFCValidationErrorMessage = (error: unknown): string => {
  return getErrorMessage(error, ERROR_CONTEXTS.VALIDATE_RFC)
}

/**
 * Función específica para errores de creación de usuario
 * Usar para toast notifications simples
 */
export const getUserCreationErrorMessage = (error: unknown): string => {
  return getErrorMessage(error, ERROR_CONTEXTS.CREATE_USER)
}

/**
 * Función específica para errores de actualización de usuario
 * Usar para toast notifications simples
 */
export const getUserUpdateErrorMessage = (error: unknown): string => {
  return getErrorMessage(error, ERROR_CONTEXTS.UPDATE_USER)
}

/**
 * Interfaz para errores detallados
 */
export interface DetailedError {
  message: string
  errors: string[]
}

/**
 * Obtiene información detallada del error para mostrar en modales
 * @param error - Error capturado de la API
 * @param context - Contexto específico del error
 * @returns Objeto con mensaje, errores específicos
 */
export const getDetailedErrorInfo = (error: unknown, context: string = 'realizar la operación'): DetailedError => {
  const result: DetailedError = {
    message: '',
    errors: [],
  }

  // Verificar si es un error de Axios con respuesta HTTP
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as ApiError
    const status = axiosError.response?.status || axiosError.response?.statusCode || axiosError.status
    const responseData = axiosError.response?.data

    // Extraer mensaje de error
    let message = ''
    if (responseData) {
      message = responseData.message || ''
      if (!message && responseData.data?.message) {
        message = responseData.data.message
      }
      if (!message && responseData.error) {
        if (typeof responseData.error === 'string') {
          message = responseData.error
        } else if (typeof responseData.error === 'object' && responseData.error !== null && 'message' in responseData.error) {
          message = (responseData.error as { message: string }).message
        }
      }
    }

    const errors = responseData?.errors || []
    const messageStr = typeof message === 'string' ? message : ''

    if (!status) {
      result.message = `Error inesperado al ${context}`
      result.errors = ['No se pudo determinar la causa del error']
      return result
    }

    switch (status) {
      case 400:
        result.message = 'Datos de entrada inválidos'
        if (errors.length > 0) {
          result.errors = errors
        } else if (messageStr) {
          result.errors = [messageStr]
        } else {
          result.errors = ['Los datos proporcionados no son válidos']
        }
        break

      case 401:
        result.message = 'Sesión expirada'
        result.errors = ['Tu sesión ha expirado o no tienes autorización']
        break

      case 403:
        result.message = 'Permisos insuficientes'
        result.errors = [`No tienes permisos para ${context}`]
        break

      case 409:
        result.message = 'Conflicto de datos'
        if (messageStr.toLowerCase().includes('rfc')) {
          result.errors = ['Ya existe un cliente registrado con este RFC']
        } else if (messageStr.toLowerCase().includes('email')) {
          result.errors = ['Ya existe un usuario registrado con este correo electrónico']
        } else {
          result.errors = [messageStr || 'Ya existe un registro con esta información']
        }
        break

      case 422:
        result.message = 'Errores de validación'
        result.errors = errors.length > 0 ? errors : [messageStr || 'Los datos no cumplen con el formato requerido']
        break

      case 500:
        result.message = 'Error interno del servidor'
        result.errors = ['Ocurrió un error en el servidor']
        break

      default:
        result.message = `Error del servidor (${status})`
        result.errors = [messageStr || 'Error desconocido del servidor']
    }
  } else {
    // Error genérico
    result.message = `Error al ${context}`
    result.errors = ['Error de conexión o problema inesperado']
  }

  return result
}

/**
 * Función específica para obtener errores detallados de creación de cliente
 */
export const getDetailedClientCreationError = (error: unknown): DetailedError => {
  return getDetailedErrorInfo(error, ERROR_CONTEXTS.CREATE_CLIENT)
}

/**
 * Función específica para obtener errores detallados de actualización de usuario
 */
export const getDetailedUserUpdateError = (error: unknown): DetailedError => {
  return getDetailedErrorInfo(error, ERROR_CONTEXTS.UPDATE_USER)
}
