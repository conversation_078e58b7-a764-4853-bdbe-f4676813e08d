import { ConveniaMovementsFilters, TypeCommission } from '@/types/reports/convenia-movements'
import { AmountsReportFilters } from './amounts'
import { CardStatusFilters } from './card-status'
import { IssuedCardsReportFilters } from './issued-cards'


export type CardStatusReportParams = CardStatusFilters
export type ConveniaMovementsReportParams = ConveniaMovementsFilters
export type AmountsReportParams = AmountsReportFilters
export type IssuedCardsReportParams = IssuedCardsReportFilters

export type AliasHistoryReportParams = {
  email: string
  startDate?: string
  endDate?: string
}

export interface CardTransactionsReportParams {
  start: string;
  end: string;
  adminId?: string;
  pan?: string;
}

export type DailyMonthlyReportParams = {
	email: string
  startDate: string;
  endDate: string;
  status: string;
  transactionType: string;
  amount: string;
}

export type CostCommissionsReportParams = {
	aliasCompany: string
  dateFrom: string;
  dateTo: string;
  typeCommission: TypeCommission;
}

export type CostCommissionsReportPayload = {
	alias_company?: string
  date_from?: Date;
  date_to?: Date;
  typeCommission?: TypeCommission;
}

export interface EmbossingBatchesReportParams {
  status?: string
  embosser_id?: string
  creation_date?: string
  page?: string
  limit?: string
}

export interface ServiceUsageReportParams {
  debtor_key?: string
}

export interface PeriodClosingsReportParams {
  year: number;
  month: string;
  day: string;
}
