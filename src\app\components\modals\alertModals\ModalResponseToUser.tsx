import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'
import styles from '../../styles/Aclaraciones.module.css'

const ModalResponseToUser: React.FC<BaseModalProps> = ({ open, onClose }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title="Responder a usuario"
      message="Tu respuesta llegará al correo electrónico del usuario que realizo la aclarcación"
      textBtn="Enviar"
      renderMessage={() => (
        <div className={styles.textAreaAlertContent}>
          <p className={styles.labelTextAreaAlertContent}>Escribe tu mensaje</p>
          <textarea
            className={styles.textArea}
            style={{
              height: '149px',
            }}
            placeholder="Escribe aquí"
          ></textarea>
        </div>
      )}
      onClose={onClose}
      onPressBtn={() => null}
    />
  )
}

export default ModalResponseToUser
