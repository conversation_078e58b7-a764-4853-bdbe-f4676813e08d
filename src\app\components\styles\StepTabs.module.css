.container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0px;
}

.tabContainer {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  align-self: stretch;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  align-self: flex-start;
  font-weight: 400;
  font-family: Inter;
  font-size: 14px;
  background-color: #fff;
  border: none;
  color: #000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon {
  color: #000; /* Color del icono */
  transition: color 0.3s ease;
}

.title {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;

  color: #101828;
}

.tab,
.activeTab {
  display: flex;
  width: 240px;
  height: 44px;
  padding: 12px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  background-color: #fff;
  border: none;
  outline: none;
}

.activeTab {
  border-bottom: 2px solid #000;
  background: linear-gradient(
    90deg,
    #dcb992 1.9%,
    #dab890 24.9%,
    #d5b289 45.9%,
    #cca87c 68.4%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #000;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.tab {
  color: #6f7280;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
  border-bottom: 2px solid #eaecf0;
}
