import React, { useState } from 'react'
import styles from '../../styles/NewClientAdmin.module.css'
import { GoEye, GoEyeClosed } from 'react-icons/go'
import { Errors, FormData } from '@/hooks/useFormValidationClient'
import { handleNumberOnlyInput } from '@/utils/inputRestrictions'

type ContactFormProps = {
  formData: FormData
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  errors: Errors
  isEditing?: boolean
}

const ContactForm = ({ formData, onChange, errors, isEditing }: ContactFormProps) => {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const togglePasswordVisibility = () => setShowPassword(prev => !prev)
  const toggleConfirmPasswordVisibility = () => setShowConfirmPassword(prev => !prev)

  return (
    <div className={styles.formContainer}>
      <div className={styles.inputGroup}>
        <label>Teléfono*</label>
        <input
          type="text"
          name="phone"
          placeholder="00 0000 0000"
          maxLength={10}
          value={formData.phone}
          onChange={onChange}
          onKeyDown={handleNumberOnlyInput}
          className={errors.phone ? styles.error : ''}
          autoComplete="off"
        />
        {errors.phone && <span className={styles.error}>{errors.phone}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Correo electrónico*</label>
        <input
          type="email"
          name="email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={onChange}
          className={errors.email ? styles.error : ''}
          autoComplete="off"
        />
        {errors.email && <span className={styles.error}>{errors.email}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Calle*</label>
        <input
          type="text"
          name="street"
          placeholder="Privada de mina"
          value={formData.street}
          onChange={onChange}
          className={errors.street ? styles.error : ''}
          autoComplete="off"
        />
        {errors.street && <span className={styles.error}>{errors.street}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Código postal*</label>
        <input
          type="text"
          name="zipCode"
          placeholder="12345"
          value={formData.zipCode}
          onChange={onChange}
          maxLength={5}
          onKeyDown={handleNumberOnlyInput}
          className={errors.zipCode ? styles.error : ''}
          autoComplete="off"
        />
        {errors.zipCode && <span className={styles.error}>{errors.zipCode}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Número exterior</label>
        <input
          type="text"
          name="exteriorNumber"
          placeholder="Ej. 123, SN, 45A"
          maxLength={10}
          value={formData.exteriorNumber}
          onChange={onChange}
          // onKeyDown={handleNumberOnlyInput}
          className={errors.exteriorNumber ? styles.error : ''}
          autoComplete="off"
        />
        {errors.exteriorNumber && <span className={styles.error}>{errors.exteriorNumber}</span>}
      </div>

      <div className={styles.inputGroup}>
        <label>Colonia*</label>
        <input
          type="text"
          name="neighborhood"
          placeholder="Centro"
          value={formData.neighborhood}
          onChange={onChange}
          className={errors.neighborhood ? styles.error : ''}
          autoComplete="off"
        />
        {errors.neighborhood && <span className={styles.error}>{errors.neighborhood}</span>}
      </div>

      {/* este es un input oculto para bloquear el auto completado de los navegadores */}
      <input
        type="text"
        name="fakeEmailTrap"
        autoComplete="email"
        style={{ position: 'absolute', top: '-9999px', height: 0, width: 0, opacity: 0 }}
      />
      {/* fin del input iculto */}
      {!isEditing && (
        <>
          <div className={styles.inputGroup}>
            <label>Contraseña*</label>
            <div className={styles.passwordWrapper}>
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="passwordClient"
                maxLength={12}
                placeholder="********"
                value={formData.passwordClient}
                onChange={onChange}
                className={errors.passwordClient ? styles.error : ''}
                autoComplete="new-password"
                autoCorrect="off"
                disabled={isEditing}
                style={{
                  cursor: isEditing ? 'not-allowed' : 'auto',
                  opacity: isEditing ? 0.5 : 1,
                }}
              />
              {formData.passwordClient && (
                <button
                  type="button"
                  className={styles.eyeButton}
                  onClick={togglePasswordVisibility}
                  aria-label={showPassword ? 'Ocultar contraseña' : 'Mostrar contraseña'}
                >
                  {showPassword ? <GoEye size={15} /> : <GoEyeClosed size={15} />}
                </button>
              )}
            </div>
            {errors.passwordClient && <span className={styles.error}>{errors.passwordClient}</span>}
          </div>

          <div className={styles.inputGroup}>
            <label>Confirmar contraseña*</label>
            <div className={styles.passwordWrapper}>
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirmPassword"
                name="confirmPassword"
                maxLength={12}
                placeholder="********"
                value={formData.confirmPassword}
                onChange={onChange}
                className={errors.confirmPassword ? styles.error : ''}
                disabled={isEditing}
                style={{
                  cursor: isEditing ? 'not-allowed' : 'auto',
                  opacity: isEditing ? 0.5 : 1,
                }}
              />
              {formData.confirmPassword && (
                <button
                  type="button"
                  className={styles.eyeButton}
                  onClick={toggleConfirmPasswordVisibility}
                  aria-label={showConfirmPassword ? 'Ocultar contraseña' : 'Mostrar contraseña'}
                >
                  {showConfirmPassword ? <GoEye size={15} /> : <GoEyeClosed size={15} />}
                </button>
              )}
            </div>
            {errors.confirmPassword && <span className={styles.error}>{errors.confirmPassword}</span>}
          </div>
        </>
      )}
    </div>
  )
}

export default ContactForm
