// este modal se muestra cuando se guardan los datos de un nuevo cliente
import React from 'react'
import InfoModal from '../InfoModal'
import { useRouter } from 'next/navigation'
import { BaseModalProps } from '@/types/types'
import styles from '../../styles/NewClientAdminModal.module.css'

interface SaveNewClientDataModalProps extends BaseModalProps {
  numberAfiliation: string
}

const SaveNewClientDataModal: React.FC<SaveNewClientDataModalProps> = ({
  numberAfiliation,
  open,
  onClose,
}) => {
  const router = useRouter()
  return (
    <InfoModal
      title="Datos guardados correctamente"
      renderBody={() => (
        <div className={styles.content}>
          <p className={styles.subtitle}>Se genero tu número de afiliación</p>
          <p className={styles.numberAfiliation}>{numberAfiliation}</p>
          <p>Hemos recibido y almacenado los datos correctamente.</p>
        </div>
      )}
      open={open}
      onPressPrimaryBtn={() => {
        router.push('/home')
        onClose()
      }}
      onClose={onClose}
    />
  )
}

export default SaveNewClientDataModal
