import React, { ReactNode, useRef } from 'react';
import { IoIosArrowForward } from "react-icons/io";
import styles from './styles/Collapse.module.css';
import Image from 'next/image';

// IMPORTANTE: Es necesario que el componente collapse use el arrowIcon de tipo forward que es similar a este ">" y se puede rotar 90 grados para indicar si el contenido está abierto o cerrado. En caso de ser necesario, se puede cambiar el icono por otro que cumpla la misma función visual. El componente debe ser flexible para aceptar diferentes iconos y tamaños de flecha.
// La rotación funciona en contra de las manecillas del reloj para indicar que el contenido está abierto y en sentido horario para indicar que está cerrado. El componente debe ser capaz de mostrar el contenido colapsado o expandido al hacer clic en el encabezado, y debe tener un estado por defecto que permita iniciar el componente abierto o cerrado según se desee.

interface CollapseProps {
  title: string | ReactNode;
  children: ReactNode;
  defaultOpen?: boolean;
  arrowIcon?: ReactNode;
  avatarIcon?: string;
  showArrow?: boolean;
  showAvatar?: boolean;
  arrowSize?: string;
}

const Collapse: React.FC<CollapseProps> = ({
  title, children,
  defaultOpen = false,
  arrowIcon = <IoIosArrowForward />,
  arrowSize = '1.5rem',
  showArrow = true,
  showAvatar = true,
  avatarIcon = '/outline-price-change.svg'
}) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);
  // id estable por instancia
  const idRef = useRef<string>(
    [...Array(8)].map(() => Math.random().toString(36)[2]).join('')
  );
  const id = idRef.current;

  const toggleCollapse = (): void => {
    setIsOpen((prev) => !prev);
  }

  return (
    <aside className={styles.collapse} id={`collapse-${id}`}>
      <header className={styles.collapseHeader} onClick={toggleCollapse} >
        <div className={styles.collapseTitle}>
          {showAvatar &&
            <Image src={avatarIcon} alt="collapse-avatar" width={30} height={30} />
          }
          <h4>{title}</h4>
        </div>
        {showArrow && (
          <span
            id={`arrowIcon-${id}`}
            style={{
              fontSize: arrowSize,
              display: 'flex',
              transform: isOpen ? 'rotate(-90deg)' : 'rotate(90deg)',
              transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            }}
          >
            {arrowIcon}
          </span>
        )}
      </header>
      <footer
        id={`collapseContent-${id}`}
        style={{
          overflow: 'hidden',
          transition: 'max-height 0.4s ease, padding 0.3s',
          maxHeight: isOpen ? 1000 : 0,
          height: isOpen ? 'fit-content' : '0px',
          padding: isOpen ? '1rem' : '0rem',
          display: isOpen ? 'block' : 'none',
        }}
      >
        {children}
      </footer>
    </aside>
  );
};

export default Collapse;