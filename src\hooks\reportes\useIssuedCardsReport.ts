import { useState } from 'react'
import { getIssuedCardsReport } from '@/api/endpoints/reports'
import { IssuedCardsReportParams } from '@/types/reports/types'
import { useDownload } from '../useDownload'

export const useIssuedCardsReport = () => {
  const [error, setError] = useState<string | null>(null)
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: (err) => setError(err.message),
    loadingMessage: 'Generando reporte de Nuevas tarjetas...'
  })

  const downloadReport = async (params: IssuedCardsReportParams) => {
    const filename = `reporte-nuevas-tarjetas-${new Date().toISOString().split('T')[0]}.xlsx`
    await downloadExcel(
      () => getIssuedCardsReport(params),
      filename
    )
  }

  return {
    isLoading,
    loadingMessage,
    error,
    downloadReport
  }
}
