.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  /* Espaciado entre el icono y el texto */
  align-self: flex-start;
  font-weight: 500;
  font-family: Inter;
  font-size: 14px;
  background-color: #fff;
  border: none;
  color: #232429;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 1%;
}

.backButton:hover {
  color: #a67c52;
}

.icon {
  color: #232429;
  /* Color del icono */
  transition: color 0.3s ease;
}

.backButton:hover .icon {
  color: #a67c52;
  /* Cambio de color del icono al pasar el mouse */
}

.accountNumbers {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  /* Espaciado entre los números de cuenta */
  align-self: stretch;
  margin-left: 1%;
}

.subtitle {
  align-self: stretch;
  color: #101828;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  /* 160% */
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  margin-right: 1%;
  position: relative;
}

.container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin: 0 1%;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 0 1%;
}

.title {
  align-self: stretch;
  color: #101828;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
}

.notFoundText {
  margin-top: 20px; /* Ajusta el valor según el espacio que desees */
  margin-left: 20px;
}
