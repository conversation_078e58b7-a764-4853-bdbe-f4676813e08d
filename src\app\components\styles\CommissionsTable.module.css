.table {
  width: 100%;
  border-collapse: collapse;
  font-family: Inter;
  font-size: 0.9rem;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  margin-top: 12px;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eaecf0;
}

.table th {
  background-color: #ffffff;
  font-weight: 500;
  font-family: Inter;
  font-size: 12px;
  line-height: 14.52px;
  color: #475467;
  text-align: center;
  white-space: normal;
  word-wrap: break-word;
}

.table th:nth-child(1) {
  text-align: left;
}

.table th:nth-child(2n) {
  background-color: #f9fafb;
}

.table td {
  vertical-align: middle;
  color: #4a4b55;
  font-weight: 400;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  text-rendering: geometricPrecision;
}

.table td:first-child {
  text-align: left;
  color: #101828;
}

.table tr:hover td {
  background-color: #f9f9f9;
}

.table td strong {
  display: block;
  color: #000000;
  font-weight: 500;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
}

.table td span {
  font-family: Inter;
  font-size: 0.85rem;
  color: #4a4b55;
}

.header {
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 1rem;
}

.filterContainer {
  display: flex;
  flex-direction: column;
  align-items: end;
  width: 100%;
  gap: 1rem;
}


.inputGroup {
  display: flex;
  align-items: center;
  border-radius: 5px;
  overflow: hidden;
}

.btnGroup {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  border: none;
  font-weight: 600;
  cursor: pointer;
  font-family: Inter;
  font-size: 0.875rem;
  color: #111827;
  min-width: 100px;
}

.btnGroupActive {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #111827;
  border: none;
  font-weight: 600;
  cursor: pointer;
  font-family: Inter;
  font-size: 0.875rem;
  color: #f3f4f6;
  min-width: 100px;

}

@media screen and (min-width: 550px) {
  .filterContainer {
 		flex-direction: row;
   justify-content: end;
  }
}

@media screen and (min-width: 650px) {
  .header {
 		flex-direction: row;
  }
}
