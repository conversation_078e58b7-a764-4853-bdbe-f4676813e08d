import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface PDFGeneratorOptions {
  templateHTML: string
  fileName: string
  pageConfig?: {
    itemsPerFirstPage?: number
    itemsPerPage?: number
  }
  pageCallback?: (pageNumber: number) => string
  pageSize?: {
    width: number
    height: number
  }
}

export class PDFGenerator {
  private pdf: jsPDF
  private options: PDFGeneratorOptions

  constructor(options: PDFGeneratorOptions) {
    this.pdf = new jsPDF('p', 'mm', 'a4')
    this.options = {
      ...options,
      pageConfig: {
        itemsPerFirstPage: options.pageConfig?.itemsPerFirstPage || 5,
        itemsPerPage: options.pageConfig?.itemsPerPage || 17
      },
      pageSize: {
        width: options.pageSize?.width || 210,
        height: options.pageSize?.height || 297
      }
    }
  }

  private async addPageToPDF(
    htmlContent: string,
    isFirstPage: boolean
  ): Promise<void> {
    // Crear elemento temporal
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlContent
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.width = `${this.options.pageSize?.width}mm`
    tempDiv.style.height = 'auto'
    tempDiv.style.minHeight = `${this.options.pageSize?.height}mm`
    document.body.appendChild(tempDiv)

    try {
      // Esperar a que se carguen las imágenes
      await new Promise(resolve => setTimeout(resolve, 500))

      // Convertir a canvas
      const canvas = await html2canvas(tempDiv, {
        height: Math.round(this.options.pageSize!.height * 3.78), // convertir mm a pixels (96 DPI)
        width: Math.round(this.options.pageSize!.width * 3.78),
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      // Agregar nueva página si no es la primera
      if (!isFirstPage) {
        this.pdf.addPage()
      }

      const imgData = canvas.toDataURL('image/png')
      this.pdf.addImage(
        imgData,
        'PNG',
        0,
        0,
        this.options.pageSize!.width,
        this.options.pageSize!.height
      )
    } finally {
      // Limpiar
      document.body.removeChild(tempDiv)
    }
  }

  public async generatePDF<T>(items?: T[]): Promise<void> {
    try {
      if (!items || items.length === 0) {
        // Si no hay items, generar una sola página
        const content = this.options.pageCallback
          ? this.options.pageCallback(1)
          : this.options.templateHTML
        await this.addPageToPDF(content, true)
      } else {
        // Primera página
        const firstPageContent = this.options.pageCallback
          ? this.options.pageCallback(1)
          : this.options.templateHTML
        await this.addPageToPDF(firstPageContent, true)

        // Páginas adicionales
        const totalItems = items.length
        const itemsPerFirstPage = this.options.pageConfig?.itemsPerFirstPage || 5
        const itemsPerPage = this.options.pageConfig?.itemsPerPage || 17
        const itemsAfterFirstPage = totalItems - itemsPerFirstPage
        const remainingPages = Math.ceil(itemsAfterFirstPage / itemsPerPage)

        for (let page = 0; page < remainingPages; page++) {
          const pageNumber = page + 2 // Empezamos desde página 2
          const pageContent = this.options.pageCallback
            ? this.options.pageCallback(pageNumber)
            : this.options.templateHTML
          await this.addPageToPDF(pageContent, false)
        }
      }

      // Guardar el PDF
      this.pdf.save(this.options.fileName)
    } catch (error) {
      console.error('Error generando PDF:', error)
      throw error
    }
  }
}
