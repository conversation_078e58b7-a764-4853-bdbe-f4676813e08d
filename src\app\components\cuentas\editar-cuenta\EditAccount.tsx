/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import { useRouter } from 'next/navigation'
import styles from '../../styles/EditClientAdmin.module.css'
import { FaArrowLeft } from 'react-icons/fa'
import Button from '@/app/components/Button'
import { useFormValidationAccount } from '@/hooks/useFormValidationAccount'
import { AccountEditProps } from '@/types/types'
import InformationForm from '../components/InformationForm'
import { useAccountStore } from '@/store/account/useAccountStore'
import { useState } from 'react'
import { toast } from 'react-toastify'

const EditAccount = ({ onOpenModal, account, companies }: AccountEditProps) => {
  const router = useRouter()
  const time = new Date(account.admin.createdAt).toISOString().split('T')[0]
  const { updateAccount } = useAccountStore()

  const { formData, errors, handleChange, validateAllFields } = useFormValidationAccount({
    name: account.name,
    email: account.email,
    phone: account.phone,
    registrationDate: time,
    affiliationNumber: account.admin.membershipNumber.toString(),
    companyName: account.admin.companyName,
  })
  const [companyId, setCompanyId] = useState<string>(account.admin.id || '')

  const handleBack = () => {
    router.push('/cuentas')
  }

  const handleSave = async () => {
    const isValid = validateAllFields()
    if (!isValid) {
      console.warn('❌ Hay errores en el formulario')
      return
    }

    try {
      await updateAccount(account.id, {
        name: formData.name,
        email: formData.email,
        phone: Number(formData.phone),
        adminId: companyId || account.admin.id, // Usar adminId seleccionado o el actual por defecto
      })

      onOpenModal()
    } catch (error: any) {
      toast.error('Hubo un error al editar la cuenta. Intenta más tarde.')
      console.error('❌ Error en updateAccount:', error)
    }
  }

  const isFormModified = (original: typeof account, current: typeof formData): boolean => {
    return (
      original?.name !== current.name ||
      original?.email !== current.email ||
      original?.phone !== current.phone ||
      time !== current.registrationDate ||
      original?.admin.membershipNumber.toString() !== current.affiliationNumber ||
      original?.admin.companyName !== current.companyName
    )
  }

  const modified = isFormModified(account, formData)

  return (
    <div className={styles.container}>
      {/* Botón Atrás */}
      <button className={styles.backButton} onClick={handleBack}>
        <FaArrowLeft size={16} className={styles.icon} /> Atrás
      </button>

      {/* Títulos dinámicos */}
      <p className={styles.text}>Por favor proporciona los siguientes datos</p>
      <div className={styles.tabContainer}>
        <div className={styles.tabContent}>
          <button className={styles.activeTab}>Información</button>
        </div>
      </div>

      <InformationForm
        formData={formData}
        onChange={handleChange}
        errors={errors}
        companies={companies}
        onSetCompanyId={(id: string) => setCompanyId(id)}
      />

      {/* Botón Siguiente o Guardar */}
      <div className={styles.buttonContainer}>
        <Button text={'Guardar'} onClick={handleSave} fullWidth disabled={!modified} />
      </div>
    </div>
  )
}

export default EditAccount
