'use client';

import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";

import { BiMenu } from "react-icons/bi";
import { useAuthStore } from "@/store/auth/useAuthStore";
import { getVisibleMenuItemsByRole, MENU_ROUTES } from '@/constants/menu'
import styles from "./styles/AppBar.module.css";
import { useAdminUiStore } from "@/store/ui/useAdminUiStore";
import { FiLogOut } from "react-icons/fi";

interface AppBarProps {
  onLogoutClick: () => void;
}

const AppBar = ({ onLogoutClick }: AppBarProps) => {
  const router = useRouter()
  const pathname = usePathname() // Obtiene la ruta actual

  const [dropdown, setDropdown] = useState<boolean>(false);
  const { user, getUserRoleName } = useAuthStore()

  const roleName = getUserRoleName()
  const visibleMenuItems = getVisibleMenuItemsByRole(roleName)
  const { selectedAdminIndex, setSelectedAdminIndex, setSelectedAdmin } = useAdminUiStore()

  const setToogleDropDown = () => {
    setDropdown(!dropdown);
  }

  const handleNavigationChange = () => {
    // Obtener la ruta base actual
    const currentBasePath = Object.values(MENU_ROUTES).find(route => 
      typeof route === 'string' && pathname.startsWith(route)
    ) as string | undefined;

    // Si estamos en una subruta, redirigir a la ruta base
    if (currentBasePath && pathname !== currentBasePath) {
      router.push(currentBasePath);
    }
  };

  const handleCompanyChange = (index: number) => {
    setSelectedAdminIndex(index);
    setSelectedAdmin(user?.relUserRoleAdmins?.[index]?.admin);
    handleNavigationChange();
    setDropdown(false); // Cerrar el menú después de cambiar de empresa
  };

  const handleLogout = () => {
    onLogoutClick()
  }

  useEffect(() => {
    const element = document.getElementById('dropdown');
    if (element) {
      element.style.bottom = `-${element.offsetHeight - 1}px`; // Ajusta la posición del dropdown por su altura
    }
  }, [dropdown]);

  return (
    <header className={styles.appbar}>
      <Image src="/logo-convenia.svg" alt="Icono" width={60} height={60} />
      <BiMenu size={32} onClick={setToogleDropDown} />

      {dropdown && (
        <nav className={styles.dropdown} id="dropdown">
          <div className={styles.userInfo}>
            <Image
              src="/icon-convenia-user.svg"
              alt="Icono"
              className={styles.icon}
              width={40}
              height={40}
            />
            <p title={user?.email}>{user?.email || 'sin correo'}</p>
          </div>
          <ul className={styles.menu}>
            {visibleMenuItems.map(({ path, label, icon: Icon }) => {
              // Comprobamos si el pathname comienza con el path para detectar subrutas
              const isActive = pathname.startsWith(path)

              return (
                <li
                  key={path}
                  className={`${styles.menuItem} ${isActive ? styles.active : ''}`}
                  onClick={() => router.push(path)}
                >
                  <Icon size={24} className={`${styles.icon} ${isActive ? styles.iconActive : ''}`} />
                  {label}
                </li>
              )
            })}
            {/* Selector de empresas */}
            {(user?.relUserRoleAdmins?.length ?? 0) > 1 && (
              <div className={styles.companySelector}>
                <p className={styles.sectionTitle}>Empresas</p>
                <ul className={styles.companyList}>
                  {user?.relUserRoleAdmins?.map((rel, index) => {
                    if (!rel.admin) return null
                    return (
                      <li
                        key={rel.admin.id}
                        className={`${styles.companyItem} ${index === selectedAdminIndex ? styles.activeCompany : ''}`}
                        onClick={() => handleCompanyChange(index)}
                      >
                        {rel.admin.alias}
                      </li>
                    )
                  })}
                </ul>
              </div>
            )}
          </ul>
          <button className={styles.logoutButton} onClick={handleLogout}>
            Cerrar sesión
            <FiLogOut size={20} />
          </button>
        </nav>
      )}
    </header>
  )
}
export default AppBar;
