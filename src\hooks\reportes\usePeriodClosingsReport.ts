import { useState } from 'react'
import { PeriodClosingsReportParams } from '@/types/reports/types'
import { getPeriodClosingsReport } from '@/api/endpoints/reports'
import { useDownload } from '../useDownload'

export const usePeriodClosingsReport = () => {
  const [error, setError] = useState<string | null>(null)
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: (err) => setError(err.message),
    loadingMessage: 'Generando reporte de cierres contables...'
  })

  const downloadReport = async (params: PeriodClosingsReportParams) => {
    const filename = `reporte-cierres-contables-${params.year}-${params.month}-${params.day}.xlsx`
    await downloadExcel(
      () => getPeriodClosingsReport(params),
      filename
    )
  }

  return {
    isLoading,
    loadingMessage,
    error,
    downloadReport
  }
}
