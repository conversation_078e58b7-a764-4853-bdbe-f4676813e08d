import React, { useState, useRef, useEffect } from 'react'
import styles from '../../styles/FilterButton.module.css'
import { IoFilter } from 'react-icons/io5'
import { IoMdSearch } from 'react-icons/io'

const FilterButton = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null)
  const [selectedSubFilter, setSelectedSubFilter] = useState<string | null>(null)
  const filterRef = useRef<HTMLDivElement>(null)

  // Options for each filter type with ID and text
  const filterSelectionOptions: Record<string, Array<{ id: string; text: string }>> = {
    'Número de cuenta': [
      { id: 'acc1', text: '**** 1234' },
      { id: 'acc2', text: '**** 1234' },
      { id: 'acc3', text: '**** 1234' },
      { id: 'acc4', text: '**** 1234' },
      { id: 'acc5', text: '**** 1234' },
      { id: 'acc6', text: '**** 1234' },
    ],
    Importe: [
      { id: 'imp1', text: 'Mayor a' },
      { id: 'imp2', text: 'Menor a' },
      { id: 'imp3', text: 'Igual a' },
    ],
    Empresa: [
      { id: 'emp1', text: 'Fintech' },
      { id: 'emp2', text: 'Comercial' },
      { id: 'emp3', text: 'Oxxo' },
      { id: 'emp4', text: 'Bimbo' },
    ],
    Concepto: [
      { id: 'con1', text: 'Ingreso' },
      { id: 'con2', text: 'Egreso' },
    ],
    'Tipo de dispersión': [
      { id: 'disp1', text: 'Individuales' },
      { id: 'disp2', text: 'Masivas' },
    ],
  }

  // Placeholders for each filter type
  const placeholders: Record<string, string> = {
    'Número de cuenta': 'Cuenta',
    Importe: 'Cantidad',
    Empresa: 'Empresa',
    Concepto: 'Concepto',
    'Tipo de dispersión': 'Tipo',
  }

  const handleFilterSelect = (filter: string) => {
    setSelectedFilter(filter)
  }

  // Handle click outside to close the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  // Update the selectedSubFilter state to store the ID instead of text
  const handleSubFilterSelect = (id: string) => {
    setSelectedSubFilter(id)
  }

  return (
    <div className={styles.filterContainer} ref={filterRef}>
      <button className={styles.toggleButton} onClick={() => setIsOpen(!isOpen)}>
        <IoFilter size={20} /> Filtro
      </button>

      {isOpen && (
        <div className={styles.dropdownContainer}>
          <div className={styles.filterOptions}>
            <ul>
              {Object.keys(filterSelectionOptions).map((filter, index) => (
                <li
                  key={index}
                  onClick={() => handleFilterSelect(filter)}
                  className={selectedFilter === filter ? styles.selected : ''}
                >
                  {filter}
                </li>
              ))}
            </ul>
          </div>

          {selectedFilter && (
            <div className={styles.accountOptions}>
              <div className={styles.searchContainer}>
                <input type="text" placeholder={placeholders[selectedFilter] || 'Buscar'} />
                <div className={styles.searchIconContainer}>
                  <IoMdSearch size={22.6} className={styles.searchIcon} color="#6C737F" />
                </div>
              </div>
              <ul>
                {filterSelectionOptions[selectedFilter]?.map(option => (
                  <li
                    key={option.id}
                    className={selectedSubFilter === option.id ? styles.selected : ''}
                    onClick={() => handleSubFilterSelect(option.id)}
                  >
                    {option.text}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default FilterButton
