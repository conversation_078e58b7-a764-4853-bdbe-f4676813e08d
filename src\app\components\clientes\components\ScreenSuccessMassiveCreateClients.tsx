'use client'
import React from 'react'
import styles from '../../../components/styles/Transaction.module.css'
import InfoModalContentBase from '@/app/components/modals/InfoModal/InfoModalContentBase'
import { useRouter } from 'next/navigation'

const ScreenSuccessMassiveCreateClients: React.FC = () => {
  const router = useRouter()
  return (
    <div>
      <h1 className={styles.title}>Crear nuevo administrador</h1>
      <div className={styles.successEditedtContactWrapper}>
        <InfoModalContentBase
          title="La carga masiva de clientes se realizó con éxito."
          message={'Hemos recibido y almacenado los datos correctamente.'}
          onPressPrimaryBtn={() => router.push('/clientes')}
          onClose={() => null}
        />
      </div>
    </div>
  )
}

export default ScreenSuccessMassiveCreateClients
