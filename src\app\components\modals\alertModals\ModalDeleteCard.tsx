import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

type Props = BaseModalProps & {
  onPressBtn: () => void
}

const ModalDeleteCard: React.FC<Props> = ({ open, onClose, onPressBtn }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title="¿Estás seguro de que deseas eliminar esta tarjeta?"
      message="Esta acción no se puede deshacer."
      onClose={onClose}
      onPressBtn={onPressBtn}
    />
  )
}

export default ModalDeleteCard
