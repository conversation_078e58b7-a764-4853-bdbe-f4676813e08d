import Loader from '../loader/Loader'
import styles from '../styles/Stats.module.css'

interface Stat {
  value: string | number
  label: string
  loading?: boolean
}

const Stats = ({ stats = [] }: { stats: Stat[] }) => {
  const isSingle = stats.length === 1

  return (
    <div className={styles.stats}>
      {stats.map((stat, index) => (
        <div
          key={index}
          className={isSingle ? styles.aloneStat : styles.stat}
        >
          {stat.loading ? (
            <div className={styles.loaderWrapper}>
              <Loader size={28} />
            </div>
          ) : (
            <p>{stat.value}</p>
          )}
          <span>{stat.label}</span>
        </div>
      ))}
    </div>
  )
}


export default Stats
