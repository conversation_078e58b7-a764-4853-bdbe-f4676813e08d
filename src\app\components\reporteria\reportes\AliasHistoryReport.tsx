'use client'
import { useState, useEffect } from 'react'
import { MdAlternateEmail } from 'react-icons/md'
import ReportFilters from '@/app/components/ReportFilters'
import DateFilter from '@/app/components/DateFilter'
import { getAliasHistoryReport } from '@/api/endpoints/reports'
import { downloadFileFromApi } from '@/utils/downloadFileFromApi'
import { AliasHistoryReportParams } from '@/types/reports/types'
import TextInput from '@/app/components/Input'

interface AliasHistoryReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
}

const AliasHistoryReport = ({ onLoadingChange }: AliasHistoryReportProps) => {
  const [email, setEmail] = useState<string>('')
  const [startDate, setStartDate] = useState<string | undefined>()
  const [endDate, setEndDate] = useState<string | undefined>()
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('')
  const [dateClearKey, setDateClearKey] = useState(0)

  /** Descarga del reporte */
  const handleDownload = async () => {
    if (!email) {
      console.error('Email del usuario no disponible')
      return
    }

    try {
      setIsLoading(true)
      setLoadingMessage('Generando reporte de historial de alias...')

      const params: AliasHistoryReportParams = { email }
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate

      const blob = await getAliasHistoryReport(params)
      downloadFileFromApi(blob, 'historial_alias.xlsx')
    } catch (err) {
      console.error('Error al generar el reporte:', err)
    } finally {
      setIsLoading(false)
      setLoadingMessage('')
    }
  }

  /** Reset de filtros */
  const handleReset = () => {
    setEmail('')
    setStartDate(undefined)
    setEndDate(undefined)
    setActiveFilters([])
    setDateClearKey(k => k + 1)
  }

  /** Manejo de fechas (una o dos) con burbuja inmediata */
  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      setStartDate(undefined)
      setEndDate(undefined)

      // 🔹 Quitamos cualquier burbuja de fecha
      setActiveFilters(prev => prev.filter(f => !f.startsWith('Fecha')))
      return
    }

    if (dates.length === 1) {
      setStartDate(dates[0])
      setEndDate(dates[0])

      // 🔹 Mostramos una burbuja con la única fecha
      const [y, m, d] = dates[0].split('-')
      const formatted = `${d}/${m}/${y}`
      setActiveFilters(prev => [
        ...prev.filter(f => !f.startsWith('Fecha')),
        `Fecha: ${formatted}`,
      ])
    } else if (dates.length === 2) {
      setStartDate(dates[0])
      setEndDate(dates[1])

      // 🔹 Mostramos una burbuja con el rango
      const formatDate = (dateStr: string) => {
        const [y, m, d] = dateStr.split('-')
        return `${d}/${m}/${y}`
      }
      const formattedStart = formatDate(dates[0])
      const formattedEnd = formatDate(dates[1])

      setActiveFilters(prev => [
        ...prev.filter(f => !f.startsWith('Fecha')),
        `Fecha inicio: ${formattedStart} – Fecha fin: ${formattedEnd}`,
      ])
    }
  }

  /** Mantiene loading en el padre */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  /** Actualiza burbujas dinámicamente si email cambia */
  useEffect(() => {
    setActiveFilters(prev => {
      // Limpia burbujas de email
      const base = prev.filter(f => !f.startsWith('Email:'))
      return email ? [...base, `Email: ${email}`] : base
    })
  }, [email])

  return (
    <ReportFilters
      title="Historial de Alias"
      icon={<MdAlternateEmail />}
      activeFilters={activeFilters}
      isLoading={isLoading}
      onSubmit={(e) => e.preventDefault()}
      disabled={!email}
      onReset={handleReset}
      downloadOptions={{ onlyExcel: true, onExcelDownload: handleDownload }}
    >
      <TextInput
        label="Correo electrónico"
        placeholder="<EMAIL>"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault()
            // Lógica adicional si se desea forzar la búsqueda con Enter sin submit global
          }
        }}
      />

      {/* Un solo DateFilter que maneja rango o una fecha */}
      <DateFilter
        mode="range"
        label="Rango de fechas"
        placeholder="Selecciona una o dos fechas"
        onDateChange={handleDateChange}
        clearTrigger={dateClearKey}
      />
    </ReportFilters>
  )
}

export default AliasHistoryReport
