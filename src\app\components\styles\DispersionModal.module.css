.codeWrapper {
  display: flex;
  flex-direction: column;
  margin: 24px auto 32px;
  width: fit-content;
}
.codeContainer {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.footer {
  display: flex;
  flex-direction: column;
}

.textEmail {
  /* Tittle/Small */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  /* identical to box height */
  text-align: center;

  color: #4a4b55;
  margin: 12px 0px 24px;
}

.textError {
  margin-top: 8px;

  /* material-theme/body/medium */
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */
  letter-spacing: 0.25px;

  /* Error */
  color: #f04438;
  text-rendering: geometricPrecision;
}
