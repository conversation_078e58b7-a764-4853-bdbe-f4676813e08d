/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import { useEffect, useMemo, useState } from 'react'
import { BsCardList } from 'react-icons/bs'
import { useCardStatusReport } from '@/hooks/reportes'
import ReportFilters from '@/app/components/ReportFilters'
import Select from '@/app/components/Select'
import DateFilter from '@/app/components/DateFilter'
import Input from '@/app/components/Input'

import { CardStatus, CardStatusFilters } from '@/types/reports/card-status'
import { AdminBasic } from '@/types/admin/types'
import { formatRangeDate, getAdminLabel, getStatusLabel } from './report.utils'

const STATUS_OPTIONS = [
  { value: CardStatus.NORMAL, label: 'Normal' },
  { value: CardStatus.BLOCKED, label: 'Bloqueada' },
  { value: CardStatus.CANCELED, label: 'Cancelada' },
]

interface CardStatusReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
  admins: AdminBasic[]
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
}

interface CardStatusFiltersParams {
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
  admins: AdminBasic[]
}

export function useCardStatusFilters({
  isClient,
  selectedAdminId,
  onAdminChange,
  admins,
}: CardStatusFiltersParams) {
  const [filters, setFilters] = useState<CardStatusFilters>({})

  const [dateClearKey, setDateClearKey] = useState(0)

  const activeFilters = useMemo(() => {
    const list: string[] = []

    list.push(
      `Empresa: ${getAdminLabel(
        filters.adminId,
        isClient,
        admins,
        selectedAdminId
      )}`
    )

    list.push(`Estado: ${getStatusLabel(filters.status)}`)
    if (filters.startDate && filters.endDate) {
      list.push(formatRangeDate(new Date(filters.startDate), new Date(filters.endDate)))
    }
    if (filters.holder) {
      list.push(`Titular: ${filters.holder}`)
    }
    return list
  }, [filters, admins, isClient, selectedAdminId])

  const handleFilterChange = (name: keyof CardStatusFilters, value: string | Date | undefined) => {
    if (isClient && name === 'adminId') return
    setFilters(prev => ({ ...prev, [name]: value }))

    if (name === 'adminId') {
      onAdminChange(value as string)
    }
  }

  const handleReset = () => {
    if (isClient) {
      onAdminChange(selectedAdminId)
      setFilters({
        adminId: selectedAdminId,
        status: undefined,
        startDate: undefined,
        endDate: undefined,
        holder: '',
      })
    } else {
      onAdminChange('')
      setFilters({
        adminId: undefined,
        status: undefined,
        startDate: undefined,
        endDate: undefined,
        holder: '',
      })
    }
    setDateClearKey(k => k + 1)
  }

  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      handleFilterChange('startDate', undefined)
      handleFilterChange('endDate', undefined)
      return
    }

    if (dates.length === 1) {
      handleFilterChange('startDate', dates[0])
      handleFilterChange('endDate', dates[0])
    } else if (dates.length === 2) {
      handleFilterChange('startDate', dates[0])
      handleFilterChange('endDate', dates[1])
    }
  }

  useEffect(() => {
    if (!selectedAdminId || admins.length === 0) return
    setFilters(prev => ({ ...prev, adminId: selectedAdminId || undefined }))
  }, [selectedAdminId, admins])

  return {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleDateChange,
    handleReset,
  }
}


const CardStatusReport = ({
  admins,
  onLoadingChange,
  isClient,
  selectedAdminId,
  onAdminChange,
}: CardStatusReportProps) => {
  const { isLoading, loadingMessage, downloadReport } = useCardStatusReport()
  const {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleDateChange,
    handleReset,
  } = useCardStatusFilters({
    isClient,
    selectedAdminId,
    onAdminChange,
    admins,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
  }

  const handleDownload = async () => {
    const raw = {
      ...filters,
      ...(isClient ? { adminId: selectedAdminId } : {}),
    }
    const params = Object.fromEntries(Object.entries(raw).filter(([, v]) => v != null && v !== ''))
    await downloadReport(params)
  }

  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onSubmit={handleSubmit}
      onReset={handleReset}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Estado de Tarjetas"
      icon={<BsCardList />}
      downloadOptions={{
        onlyExcel: true,
        onExcelDownload: handleDownload,
      }}
    >
      <Select
        name="adminId"
        label="Empresa"
        value={filters.adminId || ''}
        onChange={e => handleFilterChange('adminId', e.target.value)}
        disabled={isClient}
      >
        <option value="">Todas las empresas</option>
        {admins.map(admin => (
          <option key={admin.id} value={admin.id}>
            {admin.alias || admin.companyName}
          </option>
        ))}
      </Select>

      <Select
        name="status"
        label="Estado de la tarjeta"
        value={filters.status || ''}
        onChange={e => handleFilterChange('status', e.target.value)}
      >
        <option value="">Todos los estados</option>
        {STATUS_OPTIONS.map(opt => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </Select>

      <DateFilter
        mode="range"
        onDateChange={handleDateChange}
        label="Rango de fechas"
        placeholder="Selecciona el rango de fechas"
        clearTrigger={dateClearKey}
      />

      <Input
        type="text"
        name="holder"
        label="Buscar por titular"
        placeholder="Nombre o correo del titular"
        value={filters.holder || ''}
        onChange={e => handleFilterChange('holder', e.target.value)}
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault()
            // Lógica adicional si se desea forzar la búsqueda con Enter sin submit global
          }
        }}
      />
    </ReportFilters>
  )
}

export default CardStatusReport
