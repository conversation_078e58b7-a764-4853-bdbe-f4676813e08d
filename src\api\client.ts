import axios from 'axios'
import { getToken } from '@/utils/token'

// const isDevEnv = typeof window !== 'undefined'
//   ? window.location.hostname.includes('localhost') || window.location.hostname.includes('dev-finberry.therocketcode.com')
//   : process.env.HOST?.includes('localhost') || process.env.HOST?.includes('dev-finberry.therocketcode.com')

const apiClient = axios.create({
  baseURL: 'https://dev-api-finberry.therocketcode.com',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Interceptor para incluir el token en cada request
apiClient.interceptors.request.use(
  config => {
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// Interceptor para manejar errores globales
apiClient.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      console.warn('No autorizado o sesión expirada')
    }
    return Promise.reject(error)
  }
)

export default apiClient
