.container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.label {
  border: 1px solid #d1d5db;
  border-radius: 999px;
  padding: 4px 12px;
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
  background-color: white;
  cursor: default;
}

.toggle {
  width: 48px;
  height: 26px;
  border-radius: 999px;
  background-color: #f2f4f7;
  position: relative;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.toggle.active {
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
}

.circle {
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 3px;
  left: 3px;
  transition: transform 0.3s ease;
}

.toggle.active .circle {
  transform: translateX(22px);
}
