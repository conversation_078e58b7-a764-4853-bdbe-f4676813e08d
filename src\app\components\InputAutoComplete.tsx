import styles from './styles/NewClientAdmin.module.css'
import inputStyles from './styles/InputAutoComplete.module.css'
import { useState, useEffect, useRef } from 'react'
import React, { InputHTMLAttributes } from 'react'

type Props = InputHTMLAttributes<HTMLInputElement> & {
  isError?: boolean
  errorText?: string
  label?: string
  suggestions?: string[]
  onSelectSuggestion?: (value: string) => void
}

const InputAutoComplete: React.FC<Props> = ({
  isError,
  errorText,
  className,
  label,
  suggestions = [],
  onSelectSuggestion,
  value,
  onChange,
  ...rest
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [inputValue, setInputValue] = useState(value?.toString() || '')
  const wrapperRef = useRef<HTMLDivElement>(null)

  // Add click outside handler
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Update local state when prop value changes
  useEffect(() => {
    if (value?.toString() !== inputValue) {
      setInputValue(value?.toString() || '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    setShowSuggestions(true)

    // Call the original onChange if provided
    if (onChange) {
      onChange(e)
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion)
    setShowSuggestions(false)

    if (onSelectSuggestion) {
      onSelectSuggestion(suggestion)
    }

    // Simulate an onChange event to keep parent components in sync
    const simulatedEvent = {
      target: { value: suggestion },
    } as React.ChangeEvent<HTMLInputElement>

    if (onChange) {
      onChange(simulatedEvent)
    }
  }

  // Filter suggestions based on input value
  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(inputValue.toLowerCase())
  )

  return (
    <div
      className={styles.inputGroup}
      style={{
        position: 'relative',
      }}
      ref={wrapperRef}
    >
      {label && <label>{label}</label>}
      <div className={inputStyles.inputContainerHelperSuggestions}>
        <input
          className={`${isError ? styles.error : ''} ${inputStyles.input} ${className || ''}`}
          onFocus={() => setShowSuggestions(true)}
          value={inputValue}
          onChange={handleInputChange}
          {...rest}
        />
        {showSuggestions && filteredSuggestions.length > 0 && (
          <div className={inputStyles.suggestionsContainer}>
            {filteredSuggestions.map((suggestion, index) => (
              <div
                key={index}
                className={inputStyles.suggestionItem}
                onMouseDown={() => handleSuggestionClick(suggestion)}
              >
                {suggestion}
              </div>
            ))}
          </div>
        )}
      </div>
      {isError && errorText && <p className={styles.error}>{errorText}</p>}
    </div>
  )
}

export default InputAutoComplete
