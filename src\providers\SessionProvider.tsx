'use client'

import { ReactNode, useEffect, useState } from 'react'
import { useSessionManager } from '@/lib/session/sessionManager'
import { useAuthStore } from '@/store/auth/useAuthStore'
import LoaderFull from '@/app/components/loader/LoaderFull'

interface SessionProviderProps {
  children: ReactNode
}

const SessionProvider = ({ children }: SessionProviderProps) => {
  const { hasTriedLogin, checkSession } = useAuthStore()
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => setHasMounted(true), [])

  useEffect(() => {
    if (!hasMounted) return
    if (hasTriedLogin) return

    checkSession()
  }, [hasMounted, checkSession, hasTriedLogin])

  useSessionManager()

  if (!hasTriedLogin || !hasMounted) return <LoaderFull />

  return <>{children}</>
}

export default SessionProvider
