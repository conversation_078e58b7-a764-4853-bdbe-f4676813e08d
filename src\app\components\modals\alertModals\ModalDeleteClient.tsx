import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

type ModalDeleteClientProps = BaseModalProps & {
  onConfirm?: () => void // Función opcional para manejar la confirmación de eliminación
}

const ModalDeleteClient: React.FC<ModalDeleteClientProps> = ({ open, onClose, onConfirm }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title="¿Estás seguro de que deseas eliminar este cliente?"
      message="Esta acción no se puede deshacer."
      onClose={onClose}
      onPressBtn={onConfirm || (() => {})}
    />
  )
}

export default ModalDeleteClient
