import React from 'react'
import styles from './styles/GoldSwitch.module.css'

interface GoldSwitchProps {
  value: boolean
  onToggle: () => void
}

const GoldSwitch: React.FC<GoldSwitchProps> = ({ value, onToggle }) => {
  return (
    <div className={`${styles.toggle} ${value ? styles.active : ''}`} onClick={onToggle}>
      <div className={styles.circle} />
    </div>
  )
}

export default GoldSwitch
