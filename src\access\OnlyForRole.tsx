/**
 * 📄 Componente: OnlyForRole
 *
 * Este componente controla la visibilidad de su contenido según el rol del usuario actual.
 * Ideal para condicionar secciones en la UI que deben mostrarse solo a ciertos perfiles o tipos de usuario.
 *
 * Props:
 * - `roleNames?: RoleName[]` → Lista de nombres de rol permitidos (usa constantes de `ROLES`)
 * - `roleTypes?: RoleType[]` → Lista de tipos de rol permitidos (usa constantes de `ROLE_TYPES`)
 * - `not?: boolean`          → Si es true, invierte la lógica (oculta si el usuario tiene alguno de los roles)
 *
 * 🧠 Notas:
 * - Si no se proporciona ningún `roleName` o `roleType`, el contenido se mostrará por defecto.
 * - Las listas son interpretadas como condiciones OR (si cumple con **alguno**, se muestra).
 * - Para negar el acceso explícitamente, usa la prop `not`.
 *
 * 🎯 Ejemplos de uso:
 *
 * ✅ Mostrar solo para un nombre de rol específico
 * import { ROLES } from "@/constants/roles"
 *
 * <OnlyForRole roleNames={[ROLES.ADMIN_CONVENIA]}>
 *   <AdminDashboard />
 * </OnlyForRole>
 *
 * ✅ Mostrar solo para múltiples nombres de rol
 * <OnlyForRole roleNames={[ROLES.CLIENTE_TESORERO, ROLES.CLIENTE_EMPRESA_ADMIN]}>
 *   <FinanzasCliente />
 * </OnlyForRole>
 *
 * ✅ Mostrar solo si el usuario es de tipo CLIENTE
 * import { ROLE_TYPES } from "@/constants/roles"
 *
 * <OnlyForRole roleTypes={[ROLE_TYPES.CLIENTE]}>
 *   <PanelCliente />
 * </OnlyForRole>
 *
 * ❌ Ocultar para un rol específico
 * <OnlyForRole roleNames={[ROLES.ADMIN_CONVENIA_CONSULTOR]} not>
 *   <AccesoRestringido />
 * </OnlyForRole>
 *
 * ❌ Ocultar para varios tipos
 * <OnlyForRole roleTypes={[ROLE_TYPES.CLIENTE]} not>
 *   <ContenidoPrivado />
 * </OnlyForRole>
 *
 * ✅ Ejemplo completo:
 * import { OnlyForRole } from "@/permissions/OnlyForRole"
 *
 * <OnlyForRole roleTypes={[ROLE_TYPES.CONVENIA]} roleNames={[ROLES.ADMIN_CONVENIA]}>
 *   <SidebarMenu />
 * </OnlyForRole>
 */

'use client'
import { ReactNode } from 'react'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { RoleName, RoleType } from '@/constants/roles'

type OnlyForRoleProps = {
  children: ReactNode
  roleNames?: RoleName[]
  roleTypes?: RoleType[]
  not?: boolean
}

export const OnlyForRole = ({ children, roleNames, roleTypes, not = false }: OnlyForRoleProps) => {
  const hasRoleName = useAuthStore(s => s.hasRoleName)
  const hasRoleType = useAuthStore(s => s.hasRole)

  let visible = true

  if (roleNames && !roleNames.some(name => hasRoleName(name))) {
    visible = false
  }

  if (roleTypes && !roleTypes.some(type => hasRoleType(type))) {
    visible = false
  }

  return (not ? !visible : visible) ? <>{children}</> : null
}
