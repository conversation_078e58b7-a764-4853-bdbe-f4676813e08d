export enum CardStatus {
  NORMAL = 'NORMAL',
  BLOCKED = 'BLOCKED',
  CANCELED = 'CANCELED',
}

export interface CardStatusFilters {
  startDate?: string
  endDate?: string
  status?: CardStatus
  adminId?: string
  holder?: string
}

export interface CardStatusReport {
  cardNumber: string
  holderName: string
  company: string
  status: CardStatus
  issueDate: Date
  events: CardEvent[]
  accountAlias: string
}

export interface CardEvent {
  date: Date
  type: string
  source: string
  description: string
}
