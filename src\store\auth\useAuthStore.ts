/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import { signIn, renewToken } from '@/api/endpoints/auth'
import { AuthActions, AuthState } from '@/types/auth/types'
import { setToken, removeToken, getToken, removeLastActivity } from '@/utils/token'
import Cookies from 'js-cookie'
import { ROLE_TYPES, RoleName, ROLES } from '@/constants/roles'
import { getUser, removeUser, setUser } from '@/utils/user'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'

export const useAuthStore = create<AuthState & AuthActions>()((set, get) => ({
  user: null,
  token: null,
  loading: false,
  error: null,
  hasTriedLogin: false,

  signIn: async (email: string, password: string) => {
    set({ loading: true, error: null })
    try {
      const { token, user } = await signIn({ email, password, origin: 'web' })
      const { selectedAdminIndex } = useAdminUiStore.getState()

      if (!user.relUserRoleAdmins[selectedAdminIndex]?.role) {
        throw new Error('Usuario sin rol asignado. No puede iniciar sesión.')
      }

      set({ token, user })
      setToken(token)
      setUser(user)
      Cookies.set('token', token, { path: '/', expires: 1 })
    } catch (err: any) {
      set({
        error: err?.response?.data?.message || err?.message || 'Error de autenticación',
      })
    } finally {
      set({ loading: false })
    }
  },

  renewSession: async () => {
    set({ loading: true })
    try {
      const { token } = await renewToken()
      const user = getUser()
      set({ token, user })
      setToken(token)
    } catch {
      get().logout()
    } finally {
      set({ loading: false, hasTriedLogin: true })
    }
  },

  logout: () => {
    // clear localStorage
    removeToken()
    removeUser()
    useAdminUiStore.getState().removeSelectedAdminIndex()
    // clear sessionStorage
    removeLastActivity()

    // clear cookies
    Cookies.remove('token')
    Cookies.remove('user')
    set({ token: null, user: null })
  },

  checkSession: async () => {
    const existingToken = getToken() || Cookies.get('token')
    if (!existingToken) {
      set({ hasTriedLogin: true })
      return
    }
    try {
      console.warn('Token found, renewing session...')
      const { token } = await renewToken()
      const user = getUser()
      set({ token, user })

      // LocalStorage
      setToken(token)
      setUser(user)

      // Cookies
      Cookies.set('token', token, { path: '/' })
      Cookies.set('user', JSON.stringify(user), { path: '/' })
    } catch {
      console.warn('Error renewing session, logging out...')
      get().logout()
    } finally {
      set({ hasTriedLogin: true })
    }
    return
  },

  getUserRole: () => {
    const user = get().user
    const { selectedAdminIndex } = useAdminUiStore.getState()
    return user?.relUserRoleAdmins?.[selectedAdminIndex]?.role || null
  },

  hasRole: (roleType: string) => {
    const user = get().user
    return user?.relUserRoleAdmins?.some(r => r.role.type === roleType) || false
  },
  getUserRoleType: () => {
    const role = get().getUserRole()
    return role?.type || null
  },

  getUserRoleName: () => {
    const role = get().getUserRole()
    return (role?.name as RoleName) ?? ROLES.CLIENTE_LECTOR
  },

  hasRoleName: (roleName: string) => {
    const user = get().user
    return user?.relUserRoleAdmins?.some(r => r.role.name === roleName) || false
  },

  getRoleType: () => {
    const role = get().getUserRoleName()
    if (role?.toLowerCase().includes('cliente')) return ROLE_TYPES.CLIENTE
    return ROLE_TYPES.CONVENIA
  },
}))
