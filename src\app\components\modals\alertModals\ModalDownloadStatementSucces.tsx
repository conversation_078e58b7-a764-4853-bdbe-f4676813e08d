import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

interface Props extends BaseModalProps {
  cardNumber: string
}

const ModalDownloadStatementSucces: React.FC<Props> = ({ open, onClose, cardNumber }) => {
  return (
    <AlertModal
      open={open}
      type="success"
      title={`Se descargo con éxito el estado de cuenta de la tarjeta ${cardNumber}`}
      onClose={onClose}
      onPressBtn={onClose}
    />
  )
}

export default ModalDownloadStatementSucces
