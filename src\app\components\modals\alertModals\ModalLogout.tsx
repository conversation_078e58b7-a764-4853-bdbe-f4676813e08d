import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

type ModalLogoutProps = BaseModalProps & {
  onConfirm: () => void
}

const ModalLogout: React.FC<ModalLogoutProps> = ({ open, onClose, onConfirm }) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title="¿Estás seguro de que deseas cerrar sesión?"
      textBtn="Cerrar sesión"
      onClose={onClose}
      onPressBtn={onConfirm}
    />
  )
}

export default ModalLogout
