import {
  AccountData,
  AccountResponse,
  AccountListParams,
  AccountDetail,
  GetCardResponse,
  BlockCardResponse,
} from '@/types/account/types'
import apiClient from '../client'

export const getAccounts = async (
  params: AccountListParams
): Promise<{ userAccounts: AccountResponse[]; count: number }> => {
  const response = await apiClient.get('/user-account', { params })
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener las cuentas')
  }
  return response.data
}

export const getAccountById = async (id: string): Promise<AccountDetail> => {
  const response = await apiClient.get(`/user-account/${id}`)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener la cuenta')
  }
  return response.data
}

export const updateAccount = async (
  id: string,
  data: Partial<AccountData>
): Promise<AccountResponse> => {
  const response = await apiClient.patch(`/user-account/${id}`, data)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al actualizar la cuenta')
  }
  return response.data
}

export const updateAccountSpei = async (
  id: string,
  data: { speiIn: boolean; speiOut: boolean }
): Promise<AccountResponse> => {
  const response = await apiClient.patch(`/user-account/${id}/spei`, data)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al actualizar la configuración SPEI de la cuenta')
  }
  return response.data
}

export const updateAccountStatus = async (id: string): Promise<AccountResponse> => {
  const response = await apiClient.patch(`/user-account/${id}/status`)
  const statusCode = response.status
  if (statusCode !== 200) {
    throw new Error('Error al actualizar el estado de la cuenta')
  }
  return response.data
}

export const getCardsById = async (id: string): Promise<GetCardResponse[]> => {
  const response = await apiClient.get(`/user-account/${id}/cards`)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener las tarjetas')
  }
  return response.data
}

export const blockCard = async (data: {
  card_dock_id: string
  card_status: string
}): Promise<BlockCardResponse> => {
  const response = await apiClient.put(`/dock-cards/control-card-status`, data)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al bloquear la tarjeta')
  }
  return response.data
}

export const deleteAccount = async (id: string): Promise<{ message: string }> => {
  const response = await apiClient.delete(`/user-account/${id}`)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al eliminar la cuenta')
  }
  return response.data
}
