'use client'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Header from '@/app/components/Header'
import GoBackButton from '@/app/components/GoBackButton'
import styles from '../../styles/Aclaraciones.module.css'
import formStyles from '../../styles/NewClientAdmin.module.css'
import GoldCheckbox from '../../GoldCheckbox'
import { useEffect, useMemo, useState } from 'react'
import SingleFileDownload from '../../SingleFileDownload'
import Button from '@/app/components/Button'
import ButtonOutlined from '../../ButtonOutlined'
import ModalResponseToUser from '../../modals/alertModals/ModalResponseToUser'
import ModalResponseToUserSuccess from '../../modals/alertModals/ModalResponseToUserSuccess'
import { useClarificationStore } from '../store/useClarificationStore'
import { formatClarificationDate } from '@/utils/formatters'
import debounce from 'lodash.debounce'
import { ClarificationStatus } from '@/types/types'

const statusOptions: ClarificationStatus[] = ['Abierto', 'Pendiente', 'Resuelto']

const Content = () => {
  const params = useParams()
  const router = useRouter()
  const clarificationNumber = params.trackingNumber
  const [openModal, setOpenModal] = useState(false)
  const [openModalSuccess, setOpenModalSuccess] = useState(false)
  const {
    selectedClarification: clarification,
    fetchClarificationById,
    updateClarificationStatus,
  } = useClarificationStore()

  const handleChecked = useMemo(
    () =>
      debounce(async (status: ClarificationStatus) => {
        if (clarification?.trackingNumber) {
          await updateClarificationStatus(clarification.trackingNumber, status)
        }
      }, 500),
    [clarification?.trackingNumber, updateClarificationStatus]
  )

  useEffect(() => {
    if (!clarification || clarification.trackingNumber !== clarificationNumber) {
      fetchClarificationById(String(clarificationNumber))
    }
  }, [clarificationNumber, fetchClarificationById, clarification])

  return (
    <div>
      <Header title={`Aclaración ${clarificationNumber}`} />
      <div className={styles.frame}>
        <GoBackButton onPress={() => router.back()} />
      </div>
      <div className={formStyles.formContainer} style={{ marginTop: '52px' }}>
        <div className={formStyles.inputGroup}>
          <label>Nombre de usuario</label>
          <input
            type="text"
            name="userName"
            placeholder=""
            value={clarification?.user.name}
            disabled
          />
        </div>
        <div className={formStyles.inputGroup}>
          <label>Tipo de aclaración</label>
          <input type="text" name="type" placeholder="" value={clarification?.type} disabled />
        </div>
        <div className={formStyles.inputGroup}>
          <label>Fecha y hora</label>
          <input
            type="text"
            name="type"
            placeholder=""
            value={clarification?.createdAt ? formatClarificationDate(clarification.createdAt) : ''}
            disabled
          />
        </div>
        <div className={formStyles.inputGroup}>
          <label>Número de reporte</label>
          <input
            type="text"
            name="type"
            placeholder=""
            value={clarification?.trackingNumber}
            disabled
          />
        </div>
      </div>
      <div className={formStyles.inputGroup} style={{ marginTop: '16px' }}>
        <label>Observaciones</label>
        <textarea
          className={styles.textArea}
          name="observations"
          placeholder=""
          value={clarification?.description}
          disabled
        />
      </div>
      <p className={styles.label}>Estatus de la aclaración:</p>
      <div className={styles.statusContainer}>
        {statusOptions.map(status => (
          <div key={status} className={styles.statusItem}>
            <p className={styles.statusText}>{status}</p>
            <GoldCheckbox
              checked={clarification?.status === status}
              onChange={() => handleChecked(status)}
            />
          </div>
        ))}
      </div>

      <p className={`${styles.label} ${styles.labelEvidencia}`}>Evidencia del usuario</p>
      <div className={styles.filesWrapper}>
        {clarification?.files.map(file => (
          <SingleFileDownload key={file?.id} fileUrl={file?.file_url} label={file?.file_name} />
        ))}
      </div>
      <div className={styles.buttonsContainer}>
        <Button text="Terminar" fullWidth onClick={() => router.back()} />

        <ButtonOutlined text="Responder a usuario" fullWidth onClick={() => setOpenModal(true)} />
      </div>
      <ModalResponseToUser
        open={openModal}
        onClose={() => {
          setOpenModal(false)
          setOpenModalSuccess(true)
        }}
      />
      <ModalResponseToUserSuccess
        open={openModalSuccess}
        onClose={() => setOpenModalSuccess(false)}
      />
    </div>
  )
}
export default Content
