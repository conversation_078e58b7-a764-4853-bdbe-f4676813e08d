.searchBar {
  display: flex;
  max-width: 327px;
  height: 48px;
  width: 100%;
  align-items: center;
  background-color: #fff;
  border: 1px solid #d1d5db; /* Color de borde */
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Sombra ligera */
  transition: border-color 0.3s ease;
}

.searchBar:hover {
  border-color: #d0d5dd; /* Cambia el color del borde al pasar el cursor */
}

.searchBar:focus-within {
  border-color: #6b7280; /* Cambia el borde al enfocarse */
}

.icon {
  color: #667085; /* Color del ícono */
  font-family: Inter;
  font-size: 20px;
  margin-right: 8px;
}

.input {
  background-color: transparent; /* Fondo transparente */
  border: none;
  outline: none;
  flex: 1; /* Ocupa todo el espacio disponible */
  font-family: Inter;
  font-size: 16px;
  color: #667085; /* Color del texto */
  font-family: 'Inter', sans-serif;
}

.input::placeholder {
  color: #667085; /* Color del placeholder */
}
