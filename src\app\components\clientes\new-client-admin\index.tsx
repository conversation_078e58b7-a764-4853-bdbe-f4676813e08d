'use client'
import Header from '@/app/components/Header'
import NewClientAdmin from './NewClientAdmin'
import Button from '@/app/components/Button'
import styles from '../../styles/NewClientIndex.module.css'
import SaveNewClientDataModal from '@/app/components/modals/informationModals/SaveNewClientDataModal'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import MassiveLoad from '@/app/components/modals/massiveLoadModals/MassiveLoad'
import { OnlyForRole } from '@/access/OnlyForRole'
import { ROLES } from '@/constants/roles'

const HomePage = () => {
  const router = useRouter()
  const [openModal, setOpenModal] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const handleCloseModal = () => router.push(`/clientes`)
  const [membershipNumber, setMembershipNumber] = useState('')

  return (
    <>
      <Header title="Nuevo Cliente Administrador" />
      <div className={styles.header}>
        <div className={styles.buttonContainer}>
          <OnlyForRole roleNames={[ROLES.ADMIN_CONVENIA]}>
            <Button text="Carga masiva de clientes" onClick={() => setShowModal(true)} />
          </OnlyForRole>
        </div>
      </div>
      <NewClientAdmin
        onOpenModal={() => setOpenModal(true)}
        setMembershipNumber={setMembershipNumber}
      />
      <SaveNewClientDataModal
        numberAfiliation={membershipNumber}
        open={openModal}
        onClose={handleCloseModal}
      />
      <MassiveLoad open={showModal} onClose={() => setShowModal(false)} />
    </>
  )
}

export default HomePage
