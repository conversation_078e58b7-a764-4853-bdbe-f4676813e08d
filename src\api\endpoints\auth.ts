import { SignInData } from '@/types/auth/types'
import apiClient from '../client'
import { getToken } from '@/utils/token'

export const signIn = async (data: SignInData) => {
  const response = await apiClient.post('/auth/signin', data)
  return response.data // { token: string, user: object }
}

export const renewToken = async () => {
  const token = getToken() // tu función para obtener el token actual del storage
  const response = await apiClient.post(
    '/auth/renew-token',
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  )

  return {
    token: response.data.data.token,
  }
}
