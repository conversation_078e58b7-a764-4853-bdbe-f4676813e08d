.tableTitle {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  line-height: 2rem;
  color: #000000;

  margin-bottom: 24px;
  margin-top: 6px;
}

.tableWrapper {
  margin-top: 2rem;
  height: fit-content;
  /* max-height: 30vh; */
  overflow-y: auto;
}

.loader {
  width: 48px;
  height: 48px;
  border: 5px solid #fff;
  border-bottom-color: #1570ef;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes rotation {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.userContent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.imageContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.userTextContainer {
  display: flex;
  flex-direction: column;
}

.tag {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  gap: 8px;

  width: fit-content;
  height: 36px;

  /* Terciary */
  border-radius: 26px;
  color: #ffffff;

  margin: 0 auto;
}

.Abierto {
  background: rgba(139, 101, 57, 0.8);
}
.Pendiente {
  background: #e3c506;
}
.Resuelto {
  background: #08a045;
}
