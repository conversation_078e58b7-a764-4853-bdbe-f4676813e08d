/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client'
import React, { useState, useRef } from 'react'
import styles from '../components/styles/SucessTransfer.module.css'
import Button from '@/app/components/Button'
import ButtonOutlined from '@/app/components/ButtonOutlined'
import { useRouter } from 'next/navigation'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { canDownloadReceipts } from '@/permissions/access'
import { useAuthStore } from '@/store/auth/useAuthStore'
import LoaderFull from '@/app/components/loader/LoaderFull'

const DetalleMovimiento = () => {
  const router = useRouter()
  const detalleMovimiento =
    typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('detalleMovimiento') || '{}')
      : {}

  const containerRef = useRef<HTMLDivElement>(null)
  const { getUserRoleName } = useAuthStore()
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [loading, setLoading] = useState(false)

  const title = 'Comprobante de la operación'

  const handleDownloadPDF = async () => {
    if (!containerRef.current) return

    try {
      // Crear un nuevo documento PDF
      const pdf = new jsPDF('p', 'mm', 'a4')
      const pdfWidth = pdf.internal.pageSize.getWidth()

      // Configuración mejorada para html2canvas para capturar todo el contenido
      const canvas = await html2canvas(containerRef.current, {
        scale: 2, // Mayor escala para mejor calidad
        useCORS: true, // Permitir imágenes de otros dominios
        logging: false,
        scrollY: -window.scrollY, // Ajustar por el scroll
        windowHeight: document.documentElement.offsetHeight,
        height: containerRef.current.scrollHeight + 100, // Capturar altura completa
        width: 360, // Establecer el ancho exacto del contenedor
        onclone: document => {
          // Asegurarse de que todos los elementos sean visibles en el clon
          const container = document.querySelector('[data-html2canvas-ignore]')
          if (container) {
            container.removeAttribute('data-html2canvas-ignore')
          }
        },
      })

      const imgData = canvas.toDataURL('image/png')

      // Agregar el logo al PDF
      const logoPath = '/logo-finberry.png'

      // Convertir píxeles a milímetros para el PDF
      const pxToMm = 0.264583 // Factor de conversión: 1px = 0.264583mm
      const containerWidthMm = 360 * pxToMm // Ancho del contenedor en mm

      // Dimensiones del logo
      const logoWidthPx = 280
      const logoHeightPx = 124
      const logoWidth = logoWidthPx * pxToMm
      const logoHeight = logoHeightPx * pxToMm

      // Centrar el logo
      const logoX = (pdfWidth - logoWidth) / 2

      // Añadir el logo al PDF
      pdf.addImage(logoPath, 'PNG', logoX, 10, logoWidth, logoHeight)

      // Calcular dimensiones para mantener la proporción
      const contentWidth = Math.min(containerWidthMm, pdfWidth - 20) // No exceder el ancho de la página
      const contentX = (pdfWidth - contentWidth) / 2 // Centrar el contenido
      const contentHeight = (canvas.height * contentWidth) / canvas.width

      // Posición vertical después del logo
      const pxToMmForSpacing = 0.264583 // Factor de conversión: 1px = 0.264583mm
      const spacingAfterLogo = 50 * pxToMmForSpacing // 24px convertidos a mm
      const contentY = logoHeight + spacingAfterLogo // Espacio exacto de 24px después del logo

      // Verificar si el contenido es demasiado grande para una página
      const maxHeightOnFirstPage = pdf.internal.pageSize.getHeight() - contentY - 10 // 10mm de margen inferior

      if (contentHeight <= maxHeightOnFirstPage) {
        // Si cabe en una página, añadirlo normalmente
        pdf.addImage(imgData, 'PNG', contentX, contentY, contentWidth, contentHeight)
      } else {
        // Si no cabe en una página, dividirlo en múltiples páginas
        let heightLeft = contentHeight
        const position = contentY
        let page = 1

        // Añadir la primera parte en la primera página
        pdf.addImage(imgData, 'PNG', contentX, position, contentWidth, contentHeight, '', 'FAST')
        heightLeft -= maxHeightOnFirstPage

        // Añadir el resto en páginas adicionales si es necesario
        while (heightLeft > 0) {
          pdf.addPage()
          page++

          const heightOnThisPage = Math.min(heightLeft, pdf.internal.pageSize.getHeight() - 20) // 20mm de margen total

          pdf.addImage(
            imgData,
            'PNG',
            contentX,
            10 - maxHeightOnFirstPage * (page - 1),
            contentWidth,
            contentHeight,
            '',
            'FAST'
          )

          heightLeft -= heightOnThisPage
        }
      }

      // Descargar el PDF
      pdf.save('comprobante-transferencia.pdf')
    } catch (error) {
      console.error('Error al generar el PDF:', error)
    }
  }

  return (
    <div>
      {loading && <LoaderFull />}
      <h1 className={styles.titleHead}>Detalle del movimiento</h1>
      <div className={styles.container} ref={containerRef}>
        <h1 className={styles.title}>{title}</h1>

        <div className={styles.infoDescription}>
          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Monto</span>
            <span className={styles.infoValue}>{detalleMovimiento.amount}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Concepto</span>
            <span className={styles.infoValue}>{detalleMovimiento.description}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Tipo de operación</span>
            <span className={styles.infoValue}>{detalleMovimiento.payment_type}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Empresa</span>
            <span className={styles.infoValue}>{detalleMovimiento.aliasEmpresa}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Destinatario</span>
            <span className={styles.infoValue}>{detalleMovimiento.beneficiary_name}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Clabe del destinatario</span>
            <span className={styles.infoValue}>{detalleMovimiento.beneficiary_account}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Banco receptor</span>
            <span className={styles.infoValue}>{detalleMovimiento.bank_name}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Fecha y hora de operación</span>
            <span className={styles.infoValue}>
              {detalleMovimiento.dateFormatted} {detalleMovimiento.timeFormatted}
            </span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Clave de rastreo</span>
            <span className={styles.infoValue}>{detalleMovimiento.id}</span>
          </div>
        </div>
      </div>

      <div
        className={styles.buttonsContainer}
        style={{
          marginTop: '32px',
        }}
      >
        <Button text="Ir al inicio" onClick={() => router.replace('/home')} />
        {canDownloadReceipts(getUserRoleName()) && (
          <ButtonOutlined text="Descargar comprobante" onClick={handleDownloadPDF} />
        )}
      </div>
    </div>
  )
}

export default DetalleMovimiento
