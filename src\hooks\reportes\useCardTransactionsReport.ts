import { useState } from 'react'
import { getCardTransactionsReport } from '@/api/endpoints/reports'
import { CardTransactionsReportParams } from '@/types/reports/types'
import { useDownload } from '../useDownload'

export const useCardTransactionsReport = () => {
  const [error, setError] = useState<string | null>(null)
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: (err) => setError(err.message),
    loadingMessage: 'Generando reporte de transacciones por tarjeta...'
  })

  const downloadReport = async (params: CardTransactionsReportParams) => {
    const filename = `reporte-transacciones-tarjeta-${new Date().toISOString().split('T')[0]}.xlsx`
    await downloadExcel(
      () => getCardTransactionsReport(params),
      filename
    )
  }

  return {
    isLoading,
    loadingMessage,
    error,
    downloadReport
  }
}
