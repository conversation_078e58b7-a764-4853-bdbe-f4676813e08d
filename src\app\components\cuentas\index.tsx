'use client'
import React, { useEffect, useState } from 'react'
// import accountsData from "@/app/components/data/accounts_data.json"
import Header from '../Header'
import Accounts from './Accounts'
import ModalActiveAccount from '../modals/alertModals/ModalActiveAccount'
import ModalDeleteAccount from '../modals/alertModals/ModalDeleteAccount'
import ModalSpei from '../modals/alertModals/ModalSpei'
// import { getAccounts } from '@/api/endpoints/account'
import LoaderFull from '../loader/LoaderFull'
import { useAccountStore } from '@/store/account/useAccountStore'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { shouldSendAdminId } from '@/permissions/access'
import { RoleName } from '@/constants/roles'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'

function Content() {
  const [itemsPerPage, setItemsPerPage] = useState(5)
  // const [loadingAccounts, setLoadingAccounts] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedAccountNumber, setSelectedAccountNumber] = useState('')
  const [selectedAccountId, setSelectedAccountId] = useState('')
  const [selectedAccountIsActive, setSelectedAccountIsActive] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [accountToDelete, setAccountToDelete] = useState('')
  const [userIdToDelete, setUserIdToDelete] = useState('')
  const [speiModalOpen, setSpeiModalOpen] = useState(false)
  const [selectedSpeiType, setSelectedSpeiType] = useState<'in' | 'out'>('in')
  const [selectedSpeiAccount, setSelectedSpeiAccount] = useState<{
    accountNumber: string
    updateData: { speiIn: boolean; speiOut: boolean }
    id: string
  } | null>(null)

  const {
    fetchAccounts,
    accountCount,
    accounts,
    isLoading: loading,
    updateAccountSpei,
    updateAccountStatus,
    clearSelected,
    deleteAccount,
    clearAccountDetails
  } = useAccountStore()

  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('') // Estado para manejar la búsqueda
  const [selectedDates, setSelectedDates] = useState<string[]>([])
  const { user } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()

  const handleOpenModal = (id: string, accountNumber: string, isActive: boolean) => {
    setSelectedAccountNumber(accountNumber)
    setSelectedAccountIsActive(isActive)
    setSelectedAccountId(id)
    setModalOpen(true)
  }

  const handleConfirmAction = () => {
    updateAccountStatus(selectedAccountId, {
      limit: itemsPerPage,
      page: currentPage,
      q: searchTerm,
      initDate: selectedDates[0],
      endDate: selectedDates[1],
      adminId
    }).then(() => {
      setModalOpen(false)
    })
  }

  const handleOpenDeleteModal = (accountNumber: string, userId: string) => {
    setAccountToDelete(accountNumber)
    setUserIdToDelete(userId)
    setDeleteModalOpen(true)
  }

  const handleDeleteAccount = async () => {
    try {
      await deleteAccount(userIdToDelete)
      // Refetch accounts after successful deletion
      await fetchAccounts({
        limit: itemsPerPage,
        page: currentPage,
        q: searchTerm,
        initDate: selectedDates[0],
        finalDate: selectedDates[1],
        adminId
      })
      setDeleteModalOpen(false)
    } catch (error) {
      console.error('Error deleting account:', error)
    }
  }

  const handleOpenSpeiModal = (
    accountNumber: string,
    updateData: { speiIn: boolean; speiOut: boolean },
    type: 'in' | 'out',
    id: string
  ) => {
    setSelectedSpeiAccount({ accountNumber, updateData, id })
    setSelectedSpeiType(type)
    setSpeiModalOpen(true)
  }

  const handleConfirmSpeiChange = (id: string) => {
    if (!selectedSpeiAccount?.updateData) return null

    updateAccountSpei(
      id,
      {
        speiIn: selectedSpeiAccount.updateData.speiIn,
        speiOut: selectedSpeiAccount.updateData.speiOut,
      },
      {
        limit: itemsPerPage,
        page: currentPage,
        q: searchTerm,
        initDate: selectedDates[0],
        endDate: selectedDates[1],
        adminId
      }
    )
    setSpeiModalOpen(false)
  }

  const totalPages = Math.ceil(accountCount / itemsPerPage)

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }

  const roleName = user?.relUserRoleAdmins?.[selectedAdminIndex]?.role?.name || null
  const adminId = shouldSendAdminId(roleName as RoleName | null)
    ? user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.id || null
    : null

  useEffect(() => {
    clearSelected()
    clearAccountDetails()
    fetchAccounts({
      limit: itemsPerPage,
      page: currentPage,
      q: searchTerm,
      initDate: selectedDates[0],
      finalDate: selectedDates[1],
      adminId
    })
  }, [clearSelected, clearAccountDetails, currentPage, fetchAccounts, searchTerm, selectedDates, adminId, itemsPerPage])

  return (
    <>
      <Header title="Cuentas" />
      <Accounts
        accountsData={accounts} // <--- pásalo como prop
        totalPages={totalPages}
        currentPage={currentPage}
        onToggleStatus={handleOpenModal}
        onOpenDeleteAccountModal={handleOpenDeleteModal}
        onOpenSpeiModal={handleOpenSpeiModal}
        onSetCurrentPage={(page: number) => setCurrentPage(page)}
        onSetSearchTerm={(term: string) => setSearchTerm(term)}
        onSetSelectedDates={(dates: string[]) => setSelectedDates(dates)}
        itemsPerPage={itemsPerPage}
        onSetItemsPerPage={handleItemsPerPageChange}
      />
      <ModalActiveAccount
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        accountNumber={selectedAccountNumber}
        isActive={selectedAccountIsActive}
        onConfirm={handleConfirmAction}
      />
      <ModalDeleteAccount
        open={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        accountNumber={accountToDelete}
        onConfirm={handleDeleteAccount}
      />
      {selectedSpeiAccount && (
        <ModalSpei
          open={speiModalOpen}
          onClose={() => setSpeiModalOpen(false)}
          numberAccount={selectedSpeiAccount.accountNumber}
          // isActive={selectedSpeiAccount.isActive}
          isActive={
            selectedSpeiType === 'in'
              ? !selectedSpeiAccount.updateData.speiIn
              : !selectedSpeiAccount.updateData.speiOut
          }
          speiType={selectedSpeiType}
          onPressBtn={() => handleConfirmSpeiChange(selectedSpeiAccount.id)}
        />
      )}

      {loading && <LoaderFull />}
    </>
  )
}

export default Content
