import { useUserStore } from '@/store/user/useUserStore'

export const useUser = () => {
  const {
    users,
    userSelected,
    totalAccountsDock,
    loading,
    error,
    fetchConveniaUsers,
    fetchUserById,
    fetchUserByEmail,
    fetchAllUsers,
    fetchTotalAccountsDock,
    createAppUser,
    createAdminUser,
    createConveniaUser,
    updateUser,
    resetPassword,
    changePassword,
    deleteUser,
    clearSelected,
  } = useUserStore()

  return {
    users,
    userSelected,
    totalAccounts: totalAccountsDock,
    loading,
    error,
    fetchConveniaUsers,
    fetchUserById,
    fetchUserByEmail,
    fetchAllUsers,
    fetchTotalAccountsDock,
    createAppUser,
    createAdminUser,
    createConveniaUser,
    updateUser,
    resetPassword,
    changePassword,
    deleteUser,
    clearSelected,
  }
}
