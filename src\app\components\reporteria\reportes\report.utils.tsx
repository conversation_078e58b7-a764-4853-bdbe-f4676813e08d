import { AdminBasic } from '@/types/admin/types';
import { CardStatus, CardType } from '@/types/reports/issued-cards'

export const STATUS_OPTIONS: { value: CardStatus; label: string }[] = [
  { value: CardStatus.NORMAL, label: 'Normal' },
  { value: CardStatus.BLOCKED, label: 'Bloqueada' },
  { value: CardStatus.CANCELED, label: 'Cancelada' },
]

export const TYPE_OPTIONS: { value: CardType; label: string }[] = [
  { value: CardType.PHYSICAL, label: 'Física' },
  { value: CardType.VIRTUAL, label: 'Virtual' },
]

export const FILTER_PREFIXES = {
  adminId: 'Empresa: ',
  cardType: 'Tipo de Tarjeta: ',
  status: 'Estado: ',
  cardHolderName: 'Titular: ',
  startDate: 'Fecha inicio: ',
  endDate: 'Fecha fin: ',
} as const


/**
 * Obtiene la etiqueta de empresa según adminId y rol.
 */
export function getAdminLabel(
  adminId: string | undefined,
  isClient: boolean,
  admins: AdminBasic[],
  selectedAdminId: string
): string {
  if (!adminId) {
    return 'Todas las empresas'
  }
  if (!isClient) {
    return admins.find(a => a.id === selectedAdminId)?.alias ?? 'Empresa no encontrada'
  }
  return admins.find(a => a.id === adminId)?.alias ?? 'Empresa no encontrada'
}

/** Obtiene la etiqueta de tipo de tarjeta */
export function getTypeLabel(type: string | undefined): string {
  if (!type) return 'Todos los tipos'
  const opt = TYPE_OPTIONS.find(o => o.value === type)
  return opt?.label ?? type
}

/** Obtiene la etiqueta de estado de tarjeta */
export function getStatusLabel(status: string | undefined): string {
  if (!status) return 'Todos los estados'
  const opt = STATUS_OPTIONS.find(o => o.value === status)
  return opt?.label ?? status
}

/** Formatea una fecha a dd/MM/yyyy */
export function formatDate(date: Date): string {
  const d = date
  const day = d.getDate().toString().padStart(2, '0')
  const month = (d.getMonth() + 1).toString().padStart(2, '0')
  const year = d.getFullYear()
  return `${day}/${month}/${year}`
}

/** Formatea un rango de fechas */
export function formatRangeDate(start: Date, end: Date): string {
  return `Fecha inicio: ${formatDate(start)} – Fecha fin: ${formatDate(end)}`
}