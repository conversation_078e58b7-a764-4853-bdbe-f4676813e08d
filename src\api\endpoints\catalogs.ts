import apiClient from '../client'
import type { States, Cities } from '@/types/catalogs/types'

export const getStates = async (): Promise<States[]> => {
  const response = await apiClient.get('/catalogs/states')
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener los estados')
  }
  return response.data
}

export const getCitiesByStateId = async (stateId: string): Promise<Cities[]> => {
  const response = await apiClient.get(`/catalogs/states/${stateId}/cities`)
  const status = response.status
  if (status !== 200) {
    throw new Error('Error al obtener las ciudades del estado')
  }
  return response.data
}
