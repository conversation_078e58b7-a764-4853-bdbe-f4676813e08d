import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

const ModalUploadClientsSuccess: React.FC<BaseModalProps> = ({ open, onClose }) => {
  return (
    <AlertModal
      open={open}
      type="success"
      title="La carga de clientes ha sido exitosa"
      onClose={onClose}
      onPressBtn={onClose}
    />
  )
}

export default ModalUploadClientsSuccess
