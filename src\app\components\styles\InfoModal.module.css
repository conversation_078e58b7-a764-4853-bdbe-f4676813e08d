.content {
  padding: 40px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.closeButton {
  position: absolute;
  top: 22px;
  right: 22px;
  background: none;
  border: none;
  font-family: Inter;
  font-size: 20px;
  cursor: pointer;
  z-index: 9999;
}

.btn {
  margin-top: 32px;
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  max-width: 360px;
  height: 48px;
  width: 100%;
  color: white;
  padding: 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-family: Inter;
  font-size: 16px;
  line-height: 19.36px;
  /* margin-top: 1rem; */
  transition:
    all 0.4s ease,
    background 0.4s ease;
  position: relative;
  z-index: 2;
}

.title {
  margin: 24px 0 12px;

  /* Text */

  width: 360px;

  /* Display sm/Semibold */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-family: Inter;
  font-size: 30px;
  line-height: 38px;
  /* or 127% */
  text-align: center;

  color: #000000;
}
.message {
  /* Supporting text */

  width: 360px;
  height: 48px;

  /* Text md/Regular */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-family: Inter;
  font-size: 16px;
  line-height: 24px;
  /* or 150% */
  text-align: center;

  color: #6f7280;
}
