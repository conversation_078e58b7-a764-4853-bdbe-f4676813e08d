import * as React from 'react'
import { SVGProps } from 'react'
const DownloadIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={31} height={30} fill="none" {...props}>
    <path
      fill="url(#a)"
      d="m15.5 20-6.25-6.25L11 11.937l3.25 3.25V5h2.5v10.188l3.25-3.25 1.75 1.812L15.5 20ZM8 25a2.407 2.407 0 0 1-1.765-.734A2.412 2.412 0 0 1 5.5 22.5v-3.75H8v3.75h15v-3.75h2.5v3.75a2.41 2.41 0 0 1-.734 1.766c-.489.49-1.078.735-1.766.734H8Z"
    />
    <defs>
      <linearGradient
        id="a"
        x1={5.5}
        x2={25.5}
        y1={15.001}
        y2={15.001}
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0.019} stopColor="#DCB992" />
        <stop offset={0.249} stopColor="#DAB890" />
        <stop offset={0.459} stopColor="#D5B289" />
        <stop offset={0.684} stopColor="#CCA87C" />
        <stop offset={0.975} stopColor="#BF9B6B" />
        <stop offset={0.998} stopColor="#AF8B56" />
        <stop offset={0.999} stopColor="#AF8B55" />
        <stop offset={1} stopColor="#E9D6B8" />
      </linearGradient>
    </defs>
  </svg>
)
export default DownloadIcon
