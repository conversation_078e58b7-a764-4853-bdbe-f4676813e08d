'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useRouter } from 'next/navigation'
import { getLastActivity, setLastActivity } from '@/utils/token'

let inactivityTimer: ReturnType<typeof setTimeout> | null = null
let refreshInterval: ReturnType<typeof setInterval> | null = null
let debugInterval: ReturnType<typeof setInterval> | null = null
let isUnloading = false

const INACTIVITY_LIMIT = 5 * 60 * 1000 // 5 minutos
const TOKEN_REFRESH_INTERVAL = 14 * 60 * 1000 // 14 minutos

export const useSessionManager = () => {
  const { user, token, logout, renewSession, hasTriedLogin } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!hasTriedLogin) return
    if (!token || !user) {
      router.replace('/login')
      logout()
      return
    }

    const last = getLastActivity()
    const now = Date.now()
    const diff = now - last

    if (last && diff >= INACTIVITY_LIMIT) {
      console.warn('Sesión expirada por inactividad tras recarga')
      logout()
      router.push('/login')
      return
    }

    // ⏱ Inicia temporizador desde el tiempo restante
    const remaining = INACTIVITY_LIMIT - diff
    if (remaining > 0) {
      inactivityTimer = setTimeout(() => {
        const diffNow = Date.now() - getLastActivity()
        if (diffNow >= INACTIVITY_LIMIT) {
          console.warn('⛔ Sesión cerrada por inactividad tras espera')
          logout()
          router.push('/login')
        }
      }, remaining)
    }

    const resetInactivityTimer = (event?: Event) => {
      if (isUnloading) return

      if (event instanceof KeyboardEvent) {
        const systemKeys = ['Meta', 'Control', 'F5']
        if (systemKeys.includes(event.key)) return
      }

      setLastActivity()

      if (inactivityTimer) clearTimeout(inactivityTimer)
      inactivityTimer = setTimeout(() => {
        const diffNow = Date.now() - getLastActivity()
        if (diffNow >= INACTIVITY_LIMIT) {
          console.warn('Sesión cerrada por inactividad persistente')
          logout()
          router.push('/login')
        }
      }, INACTIVITY_LIMIT)
    }

    // Eventos para reiniciar el temporizador por actividad
    const events = ['keydown', 'scroll', 'touchstart', 'click']
    events.forEach(event => window.addEventListener(event, resetInactivityTimer))

    // Evita logout en proceso de recarga o cierre
    const handleBeforeUnload = () => {
      isUnloading = true
    }
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 🧪 Debug visual opcional - Reducido a cada 10 segundos para mejor rendimiento
    debugInterval = setInterval(() => {
      const last = getLastActivity()
      const now = Date.now()
      const diff = now - last
      const secondsLeft = Math.max(0, Math.floor((INACTIVITY_LIMIT - diff) / 1000))

      // Solo mostrar cada 10 segundos o cuando quedan menos de 60 segundos
      if (secondsLeft % 10 === 0 || secondsLeft < 60) {
        if (secondsLeft > 0) {
          console.info(
            `%c⏳ Tiempo restante antes de logout por inactividad: ${secondsLeft}s`,
            'color: white; background-color: #3B82F6; padding: 4px 8px; border-radius: 4px; font-weight: bold;'
          )
        } else {
          console.warn(
            `%c⚠️ Tiempo agotado. Cierre inminente.`,
            'color: white; background-color: #DC2626; padding: 4px 8px; border-radius: 4px; font-weight: bold;'
          )
        }
      }
    }, 1000)

    refreshInterval = setInterval(() => {
      console.info('🔄 Renovando token automáticamente...')
      renewSession()
    }, TOKEN_REFRESH_INTERVAL)

    return () => {
      events.forEach(event => window.removeEventListener(event, resetInactivityTimer))
      window.removeEventListener('beforeunload', handleBeforeUnload)
      if (inactivityTimer) clearTimeout(inactivityTimer)
      if (refreshInterval) clearInterval(refreshInterval)
      if (debugInterval) clearInterval(debugInterval)
    }
  }, [token, user, hasTriedLogin, logout, router, renewSession])
}
