/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import styles from '../styles/LoginForm.module.css'
import { GoEye, GoEyeClosed } from 'react-icons/go'
import Logo from '@/app/components/Logo'
import Button from '@/app/components/Button'
import ButtonOutlined from '../ButtonOutlined'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { toast } from 'react-toastify'
import { MENU_ROUTES, MENU_VISIBILITY_BY_ROLE } from '@/constants/menu'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'

const LoginForm = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [emailError, setEmailError] = useState('')
  const [passwordError, setPasswordError] = useState('')
  const [formData, setFormData] = useState({ email: '', password: '' })
  const { signIn, user, error, loading } = useAuthStore()
  const router = useRouter()
  const { selectedAdminIndex } = useAdminUiStore()

  const togglePasswordVisibility = () => setShowPassword(prev => !prev)

  const isValidEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return regex.test(email)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    let hasError = false

    if (!isValidEmail(formData.email)) {
      const message = 'Por favor ingresa un correo válido'
      setEmailError(message)
      toast.error(message)
      hasError = true
    } else {
      setEmailError('')
    }

    if (!formData.password) {
      const message = 'La contraseña no puede estar vacía'
      setPasswordError(message)
      toast.error(message)
      hasError = true
    } else {
      setPasswordError('')
    }

    if (hasError) return

    setIsRedirecting(true)
    await signIn(formData.email, formData.password)
  }

  useEffect(() => {
    if (!user) {
      setIsRedirecting(false)
      return
    }

    const roleName = user?.relUserRoleAdmins?.[selectedAdminIndex]?.role?.name
    const defaultRoute = MENU_VISIBILITY_BY_ROLE[roleName]?.[0] || MENU_ROUTES.HOME

    router.push(defaultRoute)
  }, [user, selectedAdminIndex])

  useEffect(() => {
    if (error) {
      setIsRedirecting(false)
      toast.error(error, {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    }
  }, [error])

  return (
    <div className={styles.container}>
      <div className={styles.logoContainer}>
        <Logo />
        <h2 className={styles.welcome}>¡Bienvenido!</h2>
        <div className={styles.separator}>
          <hr />
          <hr />
        </div>
      </div>

      <form className={styles.form} onSubmit={handleSubmit}>
        <div className={styles.inputsContainer}>
          <div className={styles.inputGroup}>
            <label htmlFor="email">Correo electrónico</label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleChange}
              required
            />
            {emailError && <p className={styles.error}>{emailError}</p>}
          </div>
          <div className={styles.inputGroup}>
            <label htmlFor="password">Contraseña</label>
            <div className={styles.passwordWrapper}>
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                placeholder="********"
                value={formData.password}
                onChange={handleChange}
                required
              />
              {formData.password && (
                <button
                  type="button"
                  className={styles.eyeButton}
                  onClick={togglePasswordVisibility}
                  aria-label="Mostrar/Ocultar contraseña"
                >
                  {showPassword ? <GoEye /> : <GoEyeClosed />}
                </button>
              )}
              {passwordError && <p className={styles.error}>{passwordError}</p>}
            </div>
          </div>
        </div>

        <Button
          text={loading || isRedirecting ? 'Cargando...' : 'Iniciar sesión'}
          type="submit"
          disabled={loading || isRedirecting}
        />
      </form>
      <ButtonOutlined
        text="Restablecer contraseña"
        onClick={() => router.push('/reset-password')}
        type="button"
        disabled={false}
      />
    </div>
  )
}

export default LoginForm
