'use client'
// import { useState } from 'react'
import styles from '../../styles/Clients.module.css'
import Pagination from '../../Pagination'
import { ClientsGroupProps } from '@/types/types'
import SearchBar from '@/app/components/SearchBar'
import ClientsGroupTable from '../components/ClientsGroupTable'

interface ClientsGroup extends ClientsGroupProps {
  currentPage: number
  totalPages: number
  onPrevPage: () => void
  onNextPage: () => void
  onNavigatePage: (page: number) => void
}

const ClientsGroup: React.FC<ClientsGroup> = ({
  clientData,
  onDeleteClient,
  onEditClient,
  onViewClient,
  currentPage,
  totalPages,
  onPrevPage,
  onNextPage,
  onNavigatePage,
}) => {
  return (
    <div>
      <div className={styles.header}>
        <div className={styles.newClientContainer}>
          <SearchBar placeholder="Buscar" onSearch={() => null} />
        </div>
      </div>
      <ClientsGroupTable
        clients={clientData}
        onViewClient={onViewClient}
        onEditClient={onEditClient}
        onDeleteClient={onDeleteClient}
      />
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevPage={onPrevPage}
        onNextPage={onNextPage}
        onNavigatePage={onNavigatePage}
      />
    </div>
  )
}

export default ClientsGroup
