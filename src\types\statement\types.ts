// Tipos para el Estado de Cuenta

export interface StatementClientInfo {
  fecha: string
  denominacionSocial: string
  nombreComercial: string
  numeroCuenta: string
  numeroClientePrefijo: string
  rfc: string
  direccionFiscal: string
}

export interface StatementBalance {
  saldoAnterior: number
  depositos: number
  retiros: number
  saldoFinal: number
  promedioSaldosDiariosAbonos: number
  promedioSaldosDiariosCargos: number
}

export interface StatementChargeDetail {
  comisionesCobradas: {
    descripcion: string
    aplica: boolean
  }
  impuestosRetenidos: {
    descripcion: string
    aplica: boolean
  }
  cargosObjetados: {
    descripcion: string
    periodo: string
  }
  operacionesMonedaNacional: {
    descripcion: string
  }
}

export interface StatementMovement {
  fechaOperacion: string
  fechaLiquidacion: string
  concepto: string
  claveRastreo: string
  cargos: number
  abonos: number
  saldos: number
}

export interface StatementData {
  clientInfo: StatementClientInfo
  balance: StatementBalance
  chargeDetails: StatementChargeDetail
  movements: StatementMovement[]
  generatedAt: string
  periodStart: string
  periodEnd: string
}

// Tipos para la respuesta de la API
export interface StatementApiResponse {
  statusCode: number
  message: string
  data: StatementData
}

// Tipos para los parámetros de la solicitud
export interface StatementRequestParams {
  email: string
  startDate: string
  endDate: string
}

// Tipos para el formateo de datos
export interface FormattedStatementData extends StatementData {
  balance: StatementBalance & {
    saldoAnteriorFormatted: string
    depositosFormatted: string
    retirosFormatted: string
    saldoFinalFormatted: string
    promedioSaldosDiariosAbonosFormatted: string
    promedioSaldosDiariosCargosFormatted: string
  }
  movements: (StatementMovement & {
    cargosFormatted: string
    abonosFormatted: string
    saldosFormatted: string
    fechaOperacionFormatted: string
    fechaLiquidacionFormatted: string
  })[]
}
