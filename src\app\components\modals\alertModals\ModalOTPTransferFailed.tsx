import React from 'react'
import AlertModal from '../AlertModal'

type Props = {
  open: boolean
  onClose: () => void
  onPressBtn: () => void
  accountCard?: string
}
const ModalOTPTransferFailed: React.FC<Props> = ({ open, onPressBtn, onClose, accountCard }) => {
  return (
    <AlertModal
      type="error"
      title={"Realizar transferencia de saldo a la cuenta " + (accountCard || '')}
      message="No se pudo procesar la transferencia, revisa los datos y vuelve a intentarlo."
      open={open}
      onClose={onClose}
      onPressBtn={onPressBtn}
    />
  )
}

export default ModalOTPTransferFailed
