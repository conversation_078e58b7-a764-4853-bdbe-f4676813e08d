name: <PERSON><PERSON>_webapp
on:
  push:
    branches: [develop]
  pull_request:
    branches: [develop]

  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'

    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - uses: actions/checkout@v4
      - name: Clean folder
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ***********
          username: ubuntu
          key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'
          script: |
            sudo rm -Rf /var/www/finberryfront
            sudo mkdir /var/www/finberryfront
            sudo chmod -Rf 777 /var/www/finberryfront
      - name: rsync deployments
        uses: burnett01/rsync-deployments@5.1
        with:
          switches: -avzr --delete
          path: ./*
          remote_path: /var/www/finberryfront/
          remote_host: ***********
          remote_user: ubuntu
          remote_key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'

      - name: deploy to server and application start
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ***********
          username: ubuntu
          key: '${{ secrets.SSH_PRIVATE_KEY_DEV }}'
          script: |
            cd /var/www/finberryfront/
            cd /var/www/finberryfront/ && npm install && npm run build              
            cd /var/www/finberryfront && pm2 stop finberryfront && pm2 delete finberryfront && pm2 start npm --name "finberryfront" -- start
