.pagination {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1rem;
  position: relative;
}

.paginationLeft {
  display: flex;
  align-items: center;
}

.itemsPerPageContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.paginationControls {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 0.5rem;
}

.paginationButton {
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-family: Inter;
  font-size: 1.2rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #575a6f8a; /* Estado por defecto */
  transition: color 0.3s ease;
}

.paginationButton:hover:not(:disabled) {
  color: #575a6f; /* Estado hover activo */
}

.paginationButton.disabled,
.paginationButton:disabled {
  cursor: not-allowed;
  color: #575a6f42; /* Estado deshabilitado (26% opacidad) */
}

.pageNumbers {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.pageButton {
  background: none;
  border: none;
  font-family: Inter;
  font-size: 1rem;
  color: #4d506287;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.4s ease;
}

.pageButton:hover {
  color: black;
  cursor: pointer;
  font-weight: bold;
}

.activePage {
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  color: #fff;
  font-weight: bold;
}

.activePage:hover {
  cursor: default;
  color: white !important; /* Asegura que el color no cambie al pasar el mouse */
}

.pageInfo {
  font-weight: 400;
  font-family: Inter;
  font-size: 14px;
  line-height: 20.02px;
  color: #4c4e6487;
  margin-left: auto; /* Mueve la información a la derecha */
  white-space: nowrap;
}
