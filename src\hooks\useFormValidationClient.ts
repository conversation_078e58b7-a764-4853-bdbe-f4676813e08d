/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import { clientValidationRules } from '@/utils/validationRules'

export type FormData = {
  companyName: string
  alias: string
  rfc: string
  responsibleName: string
  state: string
  city: string
  registrationDate: string
  assignedCards: string
  phone: string
  email: string
  street: string
  zipCode: string
  exteriorNumber: string
  neighborhood: string
  passwordClient: string
  confirmPassword: string
  files: {
    act: File | { id: number; type: string; url: string } | null
    constancy: File | { id: number; type: string; url: string } | null
    file: File | { id: number; type: string; url: string } | null
    other: File | { id: number; type: string; url: string } | null
  }
  commissionPercentage?: string
  commissionAmount?: string
  commissionCardFunding?: string
  commissionAmbassador?: string
  extraCompanies: string[]
}

export type Errors = Partial<Omit<FormData, 'files' | 'extraCompanies'>> & {
  files?: Partial<{ [key in keyof FormData['files']]: string }>
  extraCompanies?: (string | undefined)[]
}

const useFormValidationClient = (
  initialState: FormData,
  isUpdate?: boolean,
  isSucursal: boolean = false
) => {
  const [formData, setFormData] = useState<FormData>(initialState)
  const [errors, setErrors] = useState<Errors>({})

  const validate = (fieldsToValidate?: (keyof FormData)[], overrideUpdate?: boolean): boolean => {
    const newErrors: Errors = {}
    const effectiveIsUpdate = overrideUpdate ?? isUpdate

    const fields = fieldsToValidate || (Object.keys(clientValidationRules) as (keyof FormData)[])

    fields.forEach(field => {
      const validationRule = clientValidationRules[field]

      if (typeof validationRule === 'function' && field !== 'files' && field !== 'extraCompanies') {
        const fieldValue = formData[field] ?? ''

        if (validationRule.length === 3) {
          newErrors[field] = (
            validationRule as (a: string, b: string, c: boolean | undefined) => string | undefined
          )(formData.passwordClient, formData.confirmPassword, effectiveIsUpdate)
        } else if (validationRule.length === 2) {
          newErrors[field] = (
            validationRule as (a: string, b: boolean | undefined) => string | undefined
          )(fieldValue, effectiveIsUpdate)
        } else {
          newErrors[field] = (validationRule as any)(fieldValue, isSucursal, effectiveIsUpdate)
        }
      }
    })

    if (fields.includes('extraCompanies')) {
      const companies = formData.extraCompanies || []
      const companyErrors = clientValidationRules.extraCompanies(companies)

      if (companyErrors) {
        newErrors.extraCompanies = companyErrors
      }
    }

    // Validar archivos **solo si se subieron**
    if (fields.includes('files')) {
      const fileErrors: Partial<{ [key in keyof FormData['files']]: string }> = {}

      Object.keys(formData.files).forEach(fileKey => {
        const file = formData.files[fileKey as keyof FormData['files']]

        // Solo validar si el archivo **existe**
        if (file) {
          let error: string | undefined = undefined
          if (file instanceof File) {
            error = clientValidationRules.files[fileKey as keyof FormData['files']](file)
          }
          if (error) {
            fileErrors[fileKey as keyof FormData['files']] = error
          }
        }
      })

      if (Object.keys(fileErrors).length > 0) {
        newErrors.files = fileErrors
      }
    }

    // Filtrar errores eliminando los valores `undefined`
    Object.keys(newErrors).forEach(key => {
      if (!newErrors[key as keyof Errors]) {
        delete newErrors[key as keyof Errors]
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    const validationRule = clientValidationRules[name as keyof FormData]
    if (typeof validationRule === 'function') {
      const fieldValue = value ?? ''

      setErrors(prev => ({
        ...prev,
        [name]:
          validationRule.length === 2 || validationRule.length === 3
            ? (validationRule as (a: string, b: string, c?: boolean) => string | undefined)(
                formData.passwordClient,
                value,
                isUpdate // usa el global aquí también
              )
            : (validationRule as (a: string) => string | undefined)(fieldValue),
      }))
    }
  }

  // Manejo de archivos con validación
  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    fileType: keyof FormData['files']
  ) => {
    const file = e.target.files?.[0] || null

    // Solo validar si el usuario sube un archivo
    let error: string | undefined = undefined
    if (file) {
      error = clientValidationRules.files[fileType](file)
    }

    setErrors(prev => ({
      ...prev,
      files: {
        ...(prev.files || {}),
        [fileType]: error || undefined,
      },
    }))

    setFormData(prev => ({
      ...prev,
      files: { ...prev.files, [fileType]: file },
    }))
  }

  return {
    formData,
    errors,
    setFormData,
    validate,
    handleInputChange,
    handleFileChange,
  }
}

export default useFormValidationClient
