.fileInputs {
  display: flex;
  align-items: center;
  justify-content: start;
  overflow-x: scroll;
  gap: 20px;
}

.inputContainer {
  height: 150px; /* Ajusta esta altura según sea necesario */
}

.fileLabel {
  width: 200px;
  height: 120px;
  border-radius: 4px;
  border: 1px dashed #000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  text-align: center;
  cursor: pointer;
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.fileLabel:hover {
  background-color: #fef7ed;
  border-color: #c19a6b;
}

.iconFile {
  width: 24px;
  height: 24px;
}

.fileInputs p {
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 26.56px;
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
}

.fileInputs input {
  display: none;
}

.uploadedFile {
  background-color: #000;
  width: 200px;
  height: 120px;
  border-radius: 4px;
  border: 1px dashed #000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.uploadedContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 16px;
  overflow: hidden;
}

.uploadedContainer p {
  color: #fff;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: center;
}

.error {
  color: #d92d20;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
}

.deleteButton {
  width: 100%;
  padding: 8px 10px;
  border: none;
  background: none;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: opacity 0.3s ease-in-out;
  text-align: center;

  /* 🎨 Aplica el degradado SOLO al texto */
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.deleteButton:hover {
  opacity: 0.8;
}

@media screen and (min-width: 650px) {
  .fileInputs {
 		justify-content: center;
  }
}
