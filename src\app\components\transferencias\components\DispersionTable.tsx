import { NumericFormat } from 'react-number-format'
import ClarificationTableStyles from '../../styles/ClarificationTable.module.css'
import styles from '../../styles/ClientsTable.module.css'
import DownloadIcon from '../../icons/DownloadIcon'
import { Transactions } from '@/types/types'

type Props = {
  data: Transactions[]
}

const DispersionTable: React.FC<Props> = ({ data }) => {
  return (
    <div className={ClarificationTableStyles.tableWrapper}>
      <table className={styles.table}>
        <thead>
          <tr>
            <th>Número de cuenta</th>
            <th>Concepto</th>
            <th>Tipo de cuenta</th>
            <th>Fecha y hora</th>
            <th>Empresa</th>
            <th>Monto</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          {data.length > 0 ? (
            data.map(transaction => (
              <tr key={transaction.id}>
                <td style={{ fontWeight: '500', color: 'rgba(16, 24, 40, 1)' }}>
                  {transaction?.description?.includes('Comisión Convenia')
                    ? 'COMISIÓN'
                    : transaction?.account}
                </td>
                <td>{transaction.description}</td>
                <td>
                  {transaction?.description?.includes('Comisión Convenia')
                    ? 'Transferencia Convenia'
                    : transaction?.method}
                </td>
                <td>
                  {new Date(transaction.createdAt)
                    .toLocaleString('es-ES', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: false,
                    })
                    .replace(',', '.') + ' hrs'}
                </td>
                <td>{transaction.enterprise}</td>
                <td>
                  <NumericFormat
                    value={transaction.amount}
                    displayType="text"
                    thousandSeparator=","
                    prefix="$"
                    decimalScale={2}
                    fixedDecimalScale
                  />
                </td>

                <td>
                  <div className={styles.actions}>
                    {typeof transaction.externalId === 'string' ? (
                      <button
                        className={styles.actionButton}
                        onClick={() => {
                          const hasExternalId = transaction.externalId
                          if (hasExternalId) {
                            window.location.href = `/transferencias/transferencia-exitosa?id=${transaction.externalId}`
                          }
                        }}
                      >
                        <DownloadIcon />
                      </button>
                    ) : (
                      <span className={styles.noFile}></span>
                    )}
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={7} className={styles.noResults}>
                No se encontraron resultados
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  )
}

export default DispersionTable
