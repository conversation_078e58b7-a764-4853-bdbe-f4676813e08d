import Image from 'next/image'
import { LuTrash2 } from 'react-icons/lu'
import styles from '../../styles/MultiCompanySelector.module.css'
import { AdminBasic } from '@/types/admin/types'

interface Props {
  companies: string[]
  companyOptions: AdminBasic[]
  errors: {
    extraCompanies?: (string | undefined)[]
  }
  handleCompanyChange: (index: number, value: string) => void
  handleRemoveCompany: (index: number) => void
  handleAddCompany: () => void
}

export default function MultiCompanySelectFields({
  companies,
  companyOptions,
  errors,
  handleCompanyChange,
  handleRemoveCompany,
  handleAddCompany,
}: Props) {
  return (
    <div className={styles.contentWrapper}>
      {companies.map((company, index) => (
        <div key={index} className={styles.selectWrapper}>
          <label className={styles.selectLabel}>Elige la empresa que deseas agregar</label>
          <div className={styles.selectContainer}>
            <select
              className={styles.select}
              value={company}
              onChange={e => handleCompanyChange(index, e.target.value)}
            >
              <option value="" disabled>
                Selecciona una empresa
              </option>
              {companyOptions.map(option => (
                <option
                  key={option.id}
                  value={option.id}
                  disabled={companies.includes(option.id) && companies[index] !== option.id}
                >
                  {option.companyName}
                </option>
              ))}
            </select>
            {companies.length > 1 && (
              <button className={styles.deleteBtn} onClick={() => handleRemoveCompany(index)}>
                <LuTrash2 size={24} className={styles.deleteIcon} />
              </button>
            )}
          </div>
          {errors.extraCompanies?.[index] && (
            <span className={styles.error}>{errors.extraCompanies[index]}</span>
          )}
        </div>
      ))}
      <button className={styles.addBtn} onClick={handleAddCompany}>
        Agregar empresa
        <Image
          src="/add-icon.svg"
          alt="Add Icon"
          width={24}
          height={24}
          className={styles.addIcon}
        />
      </button>
    </div>
  )
}
