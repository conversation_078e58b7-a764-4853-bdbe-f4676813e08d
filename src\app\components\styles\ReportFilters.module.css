.formContainer {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  position: relative;
  max-height: 80vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #b4915f transparent;
}

.formContainer::-webkit-scrollbar {
  width: 6px;
}

.formContainer::-webkit-scrollbar-track {
  background: transparent;
}

.formContainer::-webkit-scrollbar-thumb {
  background-color: #b4915f;
  border-radius: 3px;
}

.label {
  font-size: 0.875rem;
  color: #6B7280;
  margin-bottom: 1rem;
  font-weight: normal;
}


.formGroup {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center;
  align-items: end;
  gap: 1rem;
  padding: 2rem 0;
  position: relative;
  min-height: fit-content;
}


.formGroup > div {
  min-width: 180px;
  max-width: 340px;
  flex: 1 1 220px;
}

.formItem {
  min-width: 200px;
  max-width: 300px;
}

.formGroupButtons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

/* Botón de restablecer filtros */
.resetButton {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  align-items: center;
  border: 1px solid #b4915f !important;
  margin-bottom: 1rem !important;
  cursor: pointer;
}

.resetButton:hover {
  color: #1F2937;
}

.resetButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Badges de filtros activos */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background: #ECFDF5;
  border-radius: 9999px;
  font-size: 0.75rem;
  line-height: 1rem;
  color: #065F46;
  font-weight: 500;
}

/* Etiquetas de campos */
.fieldLabel {
  display: block;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #374151;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* Contenedor del formulario */
.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Responsive */

@media (max-width: 900px) {
  .formGroup {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
    padding: 1rem 0;
  }
  .formGroup > div {
    min-width: 100%;
    max-width: 100%;
    flex: 1 1 100%;
  }
  .formContainer {
    padding: 1rem;
  }
  .formGroupButtons {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}
