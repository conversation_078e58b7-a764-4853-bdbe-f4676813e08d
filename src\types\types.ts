import { <PERSON>Name } from '@/constants/roles'
import { AccountDetail, AccountResponse } from './account/types'
import { AdminBasic, AdminClient, AdminDetail } from './admin/types'
import { FindUserWithAdminResponse, UserResponse } from './user/types'

export type HomeClientsTableProps = {
  clients: AdminClient[] // Lista de clientes a mostrar en la tabla
  onEditClient: (client: AdminClient) => void // Callback para editar un cliente
  onDeleteClient: (client: AdminClient) => void // Callback para eliminar un cliente
}

export type ClientAdmin = {
  onOpenModal: () => void
  clientData?: AdminDetail
  isEditing?: boolean
}

// Tipo para las propiedades de la tabla de clientes
export type TableAdminProps = {
  clients: AdminClient[] // Lista de clientes a mostrar en la tabla
  onViewClient?: (client: AdminClient) => void // Callback para visualizar un cliente
  onEditClient: (client: AdminClient) => void // Callback para editar un cliente
  onDeleteClient: (client: AdminClient) => void // Callback para eliminar un cliente
}

export type ClientsProps = {
  onOpenDeleteClientModal: () => void
  onViewClient: (client: AdminClient) => void
}

export type UsersProps = {
  onOpenDeleteUserModal: (adminName: string) => void
}

export type UsersTableProps = {
  users: UserResponse[] // Lista de clientes a mostrar en la tabla
  onEditUser: (user: UserResponse) => void // Callback para editar un cliente
  onDeleteUser: (user: UserResponse) => void // Callback para eliminar un cliente
}

export type UsersEditProps = {
  onOpenModal: () => void
  userData?: UserResponse | FindUserWithAdminResponse
  isEditing?: boolean
}

// Types Dummy para pruebas

// Tipo para los datos de un cliente
export type ClientProps = {
  profile: string
  companyName: string
  balance: string
  email: string
  responsibleName: string
  assignedCards: string
  registrationDate: string
  rfc: string
  groupAccounts?: GroupAccountProps[]
}

export type FullClientProps = ClientProps & {
  alias?: string
  state?: string
  city?: string
  phone?: string
  street?: string
  zipCode?: string
  exteriorNumber?: string
  neighborhood?: string
  password?: string
  confirmPassword?: string
  files?: Record<'act' | 'constancy' | 'file' | 'other', File | null>
  commissionPercentage?: string
  commissionAmount?: string
  commissionCardFunding?: string
  commissionAmbassador?: string
  extraCompanies?: string[]
}

export type GroupAccountProps = {
  alias: string
  responsibleName: string
  profileType: string
  balance: string
  conveniaAccount: string
  rfc?: string
  registrationDate?: string
  extraCompanies?: string[]
  interbankClabe?: string
}

// Tipo para las propiedades de la tarjeta
export type CardInfo = {
  cardNumber: string
  expirationDate: string
  cable: string
  cvv: string
  status: boolean
  id: string
}
export type AccountProps = {
  name: string
  email: string
  phone: string
  registrationDate: string
  accountNumberConvenia: string
  companyAlias: string
}

// Tipo para las propiedades de la tabla de cuentas
export type AccountsProps = AccountProps & {
  accountNumber: string
  accountBalance: string
  speiIn: boolean
  speiOut: boolean
  status: boolean
  membershipNumber: string
  accountNumberTransfer: string
  cards: CardInfo[]
}

export type TransactionProps = {
  establecimiento: string
  monto: string
  fechaOperacion: string
  hora: string
  numeroTransaccion: string
  claveRastreo: string
}

export type ClientsTableProps = {
  clients: ClientProps[] // Lista de clientes a mostrar en la tabla
  onViewClient?: (client: ClientProps) => void // Callback para visualizar un cliente
  onEditClient: (client: ClientProps) => void // Callback para editar un cliente
  onDeleteClient: (client: ClientProps) => void // Callback para eliminar un cliente
}

export type ClientsGroupTableProps = {
  clients: AdminClient[] // Lista de clientes a mostrar en la tabla
  onViewClient?: (client: AdminClient) => void // Callback para visualizar un cliente
  onEditClient: (client: AdminClient) => void // Callback para editar un cliente
  onDeleteClient: (client: AdminClient) => void // Callback para eliminar un cliente
}

export type AccountsTableProps = {
  accounts: AccountResponse[] // Lista de cuentas a mostrar en la tabla
  onViewAccount?: (account: AccountResponse) => void // Callback para visualizar una cuenta
  onActiveAccount?: (account: AccountResponse) => void // Callback para activar una cuenta
  onEditAccount: (account: AccountResponse) => void // Callback para editar una cuenta
  onDeleteAccount: (account: AccountResponse) => void // Callback para eliminar una cuenta
  onOpenSpeiModal?: (
    accountNumber: string,
    updateData: { speiIn: boolean; speiOut: boolean },
    type: 'in' | 'out',
    id: string
  ) => void // Callback para abrir el modal de SPEI
}

export type AccountDetailsProps = {
  //accounts: AccountsProps // Lista de cuentas a mostrar en la tabla
  // onDownload?: (cardNumber: string) => void // Callback para descargar el estado de cuenta
  onToggleCard: (cardId: string, currentStatus: string, cardLast4: string) => void // Callback para activar/desactivar tarjeta
  onDeleteCard: (cardNumber: string, id: string) => void // Callback para eliminar tarjeta
  onCardReassignment: (card: CardInfo) => void // Callback para reasignar tarjeta
  email: string // Correo electrónico del usuario
  aliasEmpresa?: string // Alias de la empresa
  // cardsData: GetCardResponse[] // Datos de las tarjetas
}

export type CardsUsedTableProps = {
  //accounts: AccountsProps // Lista de cuentas a mostrar en la tabla
  // onDownloadStatement: (cardNumber: string) => void // Callback para descargar el estado de cuenta
  onCardReassignment: (card: CardInfo) => void // Callback para reasignar tarjeta
  onDeleteCard: (card: CardInfo) => void // Callback para eliminar tarjeta
  onToggleCard: (cardId: string, currentStatus: string, cardLast4: string) => void // Callback para activar/desactivar tarjeta
}

export type ClientsGroupProps = {
  clientData: AdminClient[]
  onDeleteClient: (client: AdminClient) => void // Callback para eliminar un cliente
  onEditClient: (client: AdminClient) => void // Callback para editar un cliente
  onViewClient: (client: AdminClient) => void // Callback para visualizar un cliente
}

export type AdminsProps = {
  onOpenDeleteClientModal: (adminName: string) => void
}
// Tipo para las propiedades de la paginación
export type PaginationProps = {
  currentPage: number // Página actual
  totalPages: number // Número total de páginas
  onPrevPage: () => void // Función para manejar clic en el botón "anterior"
  onNextPage: () => void // Función para manejar clic en el botón "siguiente"
  onNavigatePage: (page: number) => void // Función para manejar la navegación a una página específica
}

// Tipo para las propiedades del modal
export type ModalProps = {
  children: React.ReactNode
  open: boolean
}

// Tipo para las propiedades del modal de información
export type InfoModalProps = {
  title: string
  message?: string
  titleBtn?: string
  open: boolean
  renderBody?: () => JSX.Element
  onPressPrimaryBtn: () => void
  onClose: () => void
}

export type AlertModalBaseProps = {
  type: 'success' | 'error' | 'warning' // type: Tipo de alerta (success, error, warning)
  title: string
  open: boolean // open: Estado de apertura del modal
  message?: string
  children?: React.ReactNode
  onClose?: () => void // onClose: Función para cerrar el modal
}
// Tipo para las propiedades del modal de alerta
export type AlertModalProps = {
  type: 'success' | 'error' | 'warning' // type: Tipo de alerta (success, error, warning)
  title: string
  message?: string
  open: boolean // open: Estado de apertura del modal
  textBtn?: string
  onClose?: () => void // onClose: Función para cerrar el modal
  onPressBtn?: () => void // onPressBtn: Función para manejar el clic en el botón principal
  renderMessage?: () => JSX.Element // renderMessage: Función que renderiza un mensaje adicional
}

// Tipo para las propiedades del modal
export type BaseModalProps = {
  open: boolean
  onClose: () => void
}

// Tipo para las propiedades del header
export type HeaderProps = {
  title: string
  userName?: string
}

export type SearchBarProps = {
  placeholder?: string // Texto del placeholder
  onSearch: (value: string) => void // Función para manejar el valor ingresado
  className?: string // Clase personalizada opcional
}

export type ClientAdminProps = {
  onOpenModal: () => void
  isLoading?: (state: boolean) => void
  clientData?: FullClientProps
}

export type AccountEditProps = {
  onOpenModal: () => void
  account: AccountDetail
  companies: Partial<AdminBasic>[]
}

export type FileInput = {
  key: 'act' | 'constancy' | 'file' | 'other'
  label: string
  shortLabel?: string
}

export type ContentProps = {
  setOpenModal: (value: boolean) => void
  openModal: boolean
  handleCloseModal: () => void
}

export type ClarificationStatus = 'Pendiente' | 'Resuelto' | 'Abierto'

export type Clarification = {
  usuario: {
    iconPath: string
    nombre: string
    correo: string
  }
  id: number
  type: string
  status: ClarificationStatus
  reportNumber: string
  dateAndTime: string
  observation: string
}

export type Transactions = {
  account: string
  amount: number
  createdAt: string
  description: string
  id: string
  externalId: string
  accountType: string
  enterprise: string
  cep?: string
  method?: string
}

export type TableColumn<T> = {
  key: string
  label: string
  allowedRoles?: RoleName[]
  render: (row: T) => React.ReactNode
}
