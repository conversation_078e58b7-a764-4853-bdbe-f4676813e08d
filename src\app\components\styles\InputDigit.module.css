.wrapper {
  /* Number */

  box-sizing: border-box;

  /* Auto layout */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2px 8px;
  gap: 8px;

  width: 64px;
  height: 64px;
  min-height: 64px;

  background: #ffffff;
  border: 1px solid #c3c6d8;
  border-radius: 18px;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;

  &:focus {
    outline: none;
    border: 2px solid #000000;
    box-shadow:
      0px 1px 2px #8a6a2e,
      0px 0px 0px 4px #af8b55;

    &::placeholder {
      color: #000000;
    }
  }

  /* Headlines/Mediuem */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 28px;
  line-height: 34px;
  /* identical to box height */
  text-align: center;

  &::placeholder {
    color: #c3c6d8;
  }

  &.hasContent {
    position: relative;
    background:
      linear-gradient(#ffffff, #ffffff) padding-box,
      linear-gradient(
          90deg,
          #75532f 0.41%,
          #8b6539 23.03%,
          #9f7a49 39.45%,
          #af8b55 50.56%,
          #dcb992 74.08%,
          #dab890 86.93%,
          #d5b289 91.56%,
          #cca87c 94.86%,
          #bf9b6b 97.51%,
          #af8b56 99.77%,
          #af8b55 99.85%,
          #e9d6b8 100%
        )
        border-box;
    border: 2px solid transparent;
    border-radius: 18px;

    /* Add gradient text effect */
    & input {
      background: linear-gradient(
        90deg,
        #75532f 0.41%,
        #8b6539 23.03%,
        #9f7a49 39.45%,
        #af8b55 50.56%,
        #dcb992 74.08%,
        #dab890 86.93%,
        #d5b289 91.56%,
        #cca87c 94.86%,
        #bf9b6b 97.51%,
        #af8b56 99.77%,
        #af8b55 99.85%,
        #e9d6b8 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }

  &.error {
    border: 2px solid #f04438;
    box-shadow: none;
    /* background: none; */
    /* -webkit-background-clip: none; */
    -webkit-text-fill-color: #f04438;
    color: #f04438;
    text-rendering: geometricPrecision;

    &:focus {
      border: 2px solid #f04438;
      box-shadow: none;
    }
  }
}
