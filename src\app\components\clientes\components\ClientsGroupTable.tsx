'use client'
import { MdOutlineEdit } from 'react-icons/md'
import { LuTrash2, LuEye } from 'react-icons/lu'
import styles from '../../styles/ClientsTable.module.css'
import { ClientsGroupTableProps } from '@/types/types'

const ClientsGroupTable: React.FC<ClientsGroupTableProps> = ({
  clients,
  onViewClient,
  onEditClient,
  onDeleteClient,
}) => {
  
  return (
    <table className={styles.table}>
      <thead>
        <tr>
          <th>Alias</th>
          <th>Responsable</th>
          <th>Tipo de perfil</th>
          <th>Saldo</th>
          <th>Cuenta CONVENIA</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        {clients.length > 0 ? (
          clients.map((client, index) => (
            <tr key={index}>
              <td>
                <strong>{client.alias}</strong>
              </td>
              <td>{client.manager.name}</td>
              <td>
                {client.manager.enterprises.length > 0
                  ? client.manager.enterprises[0].roleName
                  : ''}
              </td>
              <td>
                {typeof client.amount === 'string'
                  ? Number(client.amount).toLocaleString('es-MX', {
                      style: 'currency',
                      currency: 'MXN',
                    })
                  : 'Sin saldo'}
              </td>
              <td>{client.membershipNumber}</td>
              <td>
                <div className={styles.actions}>
                  <button
                    className={styles.actionButton}
                    onClick={() => onViewClient && onViewClient(client)}
                  >
                    <LuEye size={18} />
                  </button>
                  <button
                    className={styles.actionButton}
                    onClick={() => {
                      onEditClient(client)
                    }}
                  >
                    <MdOutlineEdit size={18} />
                  </button>
                  <button className={styles.actionButton} onClick={() => onDeleteClient(client)} disabled>
                    <LuTrash2 size={18} />
                  </button>
                </div>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className={styles.noResults}>
              No se encontraron resultados
            </td>
          </tr>
        )}
      </tbody>
    </table>
  )
}

export default ClientsGroupTable
