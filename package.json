{"name": "finberry-front-angie", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"axios": "^1.8.4", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "libphonenumber-js": "^1.12.9", "lodash.debounce": "^4.0.8", "next": "^15.2.4", "react": "^18.2.0", "react-day-picker": "^9.6.4", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-number-format": "^5.4.4", "react-phone-input-2": "^2.15.1", "react-toastify": "^11.0.5", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^10.1.5", "prettier": "^3.5.3", "typescript": "^5"}}