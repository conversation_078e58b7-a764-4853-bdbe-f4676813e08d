/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import styles from '../styles/RecentClients.module.css'
import HomeClientsTable from './HomeClientsTable'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useAdminStore } from '@/store/admin/useAdminStore'
import { AdminClient } from '@/types/admin/types'
import { useAuthStore } from '@/store/auth/useAuthStore'
import ModalDeleteClient from '@/app/components/modals/alertModals/ModalDeleteClient'
import { toast } from 'react-toastify'
import LoaderFull from '../loader/LoaderFull'
import RecordsSelector from '../RecordsSelector'

const RecentClients: React.FC = () => {
  const router = useRouter()
  const { admins, fetchAdmins, setAdminSelected, fetchAdminById, deleteSelectedAdmin } = useAdminStore()
  const { user, token } = useAuthStore()
  const [openModal, setOpenModal] = useState(false)
  const [loading, setLoading] = useState(true)
  const [limit, setLimit] = useState(5)

  useEffect(() => {
    if (!token || !user) return
    setLoading(true)
    const fetchData = async () => {
      try {
        await fetchAdmins({ limit, page: 1, orderBy: 'createdAt' })
      } catch (error) {
        console.error('Error fetching admins:', error)
        toast.error('Error al cargar los clientes recientes', {
          position: 'top-right',
          autoClose: 3000,
        })
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [user, token, limit])

  const handleEditClient = async (client: AdminClient) => {
    await fetchAdminById(client.id)
    router.push('/clientes/edit-client-admin')
  }

  const handleDeleteClient = (client: AdminClient) => {
    setAdminSelected(client)
    setOpenModal(true)
  }

  const handleConfirmDeleteClient = async () => {
    try {
      setLoading(true)
      await deleteSelectedAdmin()
      await fetchAdmins({ limit, page: 1, orderBy: 'createdAt' })
      setOpenModal(false)
      setLoading(false)
      toast.success('Cliente eliminado con éxito', {
        position: 'top-right',
        autoClose: 3000,
      })
    } catch (err) {
      setLoading(false)
      console.error('Error al eliminar el cliente:', err)
      setOpenModal(false)
      toast.error('Hubo un error al eliminar el cliente', {
        position: 'top-right',
        autoClose: 3000,
      })
    }
  }

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit)
  }

  return (
    <div className={styles.recentClients}>
      <div className={styles.header}>
        <div className={styles.title}>Clientes recientes</div>
      </div>

      <HomeClientsTable
        clients={admins}
        onEditClient={handleEditClient}
        onDeleteClient={handleDeleteClient}
      />
      <RecordsSelector 
        currentLimit={limit}
        onLimitChange={handleLimitChange}
      />
      <ModalDeleteClient
        open={openModal}
        onClose={() => setOpenModal(false)}
        onConfirm= {handleConfirmDeleteClient}
      />
      {loading && <LoaderFull />}
    </div>
  )
}

export default RecentClients
