import { useState } from 'react'
import { aliasValidationRules } from '@/utils/validationRules'

export type AliasFormData = {
  alias: string
  responsibleName: string
  rfc: string
  registrationDate: string
  profileType: string
  balance: string
  extraCompanies?: string[]
}

export type Errors = Partial<Omit<AliasFormData, 'extraCompanies'>> & {
  extraCompanies?: (string | undefined)[]
}

export const useFormValidationAlias = (initialState: AliasFormData) => {
  const [formData, setFormData] = useState<AliasFormData>(initialState)
  const [errors, setErrors] = useState<Errors>({})

  const validate = (fieldsToValidate?: (keyof AliasFormData)[]): boolean => {
    const newErrors: Errors = {}

    const fields =
      fieldsToValidate || (Object.keys(aliasValidationRules) as (keyof AliasFormData)[])

    fields.forEach(field => {
      const validationRule = aliasValidationRules[field]

      if (typeof validationRule === 'function' && field !== 'extraCompanies') {
        const fieldValue = formData[field] ?? ''
        newErrors[field] = (validationRule as (a: string) => string | undefined)(fieldValue)
      }
    })

    if (fields.includes('extraCompanies')) {
      const companies = formData.extraCompanies || []
      const companyErrors = aliasValidationRules.extraCompanies?.(companies)
      if (companyErrors) {
        newErrors.extraCompanies = companyErrors
      }
    }

    // Limpia los errores que estén vacíos o undefined
    Object.keys(newErrors).forEach(key => {
      if (!newErrors[key as keyof Errors]) {
        delete newErrors[key as keyof Errors]
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === 'balance' ? value.replace(/[^0-9.]/g, '') : value,
    }))

    const validationRule = aliasValidationRules[name as keyof AliasFormData]

    if (typeof validationRule === 'function') {
      const fieldValue = value ?? ''
      setErrors(prev => ({
        ...prev,
        [name]: (validationRule as (a: string) => string | undefined)(fieldValue),
      }))
    }
  }

  const resetForm = () => {
    setFormData(initialState)
    setErrors({})
  }

  return {
    formData,
    errors,
    setFormData,
    validate,
    resetForm,
    handleInputChange,
  }
}
