.container {
  display: flex;
  padding: 24px 0 0;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}

.label {
  align-self: stretch;
  color: #475467;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.toggle {
  display: flex;
  align-items: center;
  gap: 43px;
  align-self: stretch;
}

.toggleLabel {
  display: flex;
  width: 125px;
  align-items: center;
}

.labelText {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  flex: 1 0 0;
  overflow: hidden;
  color: #000;
  text-overflow: ellipsis;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

.toggleBtn {
  display: flex;
  padding: 4.8px;
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 6px;
  border: 1px solid var(--500, #acafc2);
  background: white;
  cursor: pointer;
  transition: background 0.2s ease;
}

.toggleBtn.active {
  color: white;
  border: 1px solid;
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
}

.checkIcon {
  color: white;
  width: 22px;
  height: 22px;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  align-self: stretch;
}

.selectWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  align-self: stretch;
}

.selectLabel {
  color: #000;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.selectContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.select {
  display: flex;
  padding: 10px 14px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  border: 1px solid #d0d5dd;
  background: #fff;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  width: 100%;
  color: #000;
}

.select option {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  flex: 1 0 0;
  overflow: hidden;
  color: #000;
  text-overflow: ellipsis;
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

.deleteBtn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  color: #475467;
  width: 24px;
  height: 24px;
}

.deleteIcon {
  color: #475467;
  width: 24px;
  height: 24px;
}

.addBtn {
  display: flex;
  align-items: center;
  border: none;
  gap: 6px;
  background: linear-gradient(
    90deg,
    #75532f 0.41%,
    #8b6539 23.03%,
    #9f7a49 39.45%,
    #af8b55 50.56%,
    #dcb992 74.08%,
    #dab890 86.93%,
    #d5b289 91.56%,
    #cca87c 94.86%,
    #bf9b6b 97.51%,
    #af8b56 99.77%,
    #af8b55 99.85%,
    #e9d6b8 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  cursor: pointer;
}

.addIcon {
  width: 24px;
  height: 24px;
}

.error {
  color: #d92d20;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
}
