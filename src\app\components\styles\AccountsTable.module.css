.table {
  width: 100%;
  border-collapse: collapse;
  font-family: Inter;
  font-size: 0.9rem;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  margin-top: 12px;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eaecf0;
}

.table th {
  background-color: #ffffff;
  font-weight: 500;
  font-family: Inter;
  font-size: 12px;
  line-height: 14.52px;
  color: #475467;
  text-align: center;
  white-space: normal;
  word-wrap: break-word;
}

.table th:nth-child(1) {
  text-align: left;
}

.table th:nth-child(2n) {
  background-color: #f9fafb;
}

.table td {
  vertical-align: middle;
  color: #4a4b55;
  font-weight: 400;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  text-rendering: geometricPrecision;
}

.table td:first-child {
  text-align: left;
  color: #101828;
}

.table tr:hover td {
  background-color: #f9f9f9;
}

.table td strong {
  display: block;
  color: #000000;
  font-weight: 500;
  font-family: Inter;
  font-size: 14px;
  line-height: 20px;
}

.table td span {
  font-family: Inter;
  font-size: 0.85rem;
  color: #4a4b55;
}

.accountCell {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.accountAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: black;
}

.actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
}

.actionButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #555;
  font-family: Inter;
  font-size: 1.2rem;
  transition: color 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.actionButton:disabled {
  opacity: 0.4;
}

.actionButton:disabled, .actionButton:disabled:hover {
  color: #767676;
  cursor: auto;
}

.actionButton:hover {
  color: #b4915f;
}

.noResults {
  text-align: center;
  padding: 16px;
  font-size: 14px;
  font-weight: bold;
  color: #666;
}

.iconColor {
  color: #af8b55;
}

.header {
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 1rem;
}


@media screen and (min-width: 650px) {
  .header {
 		flex-direction: row;
  }
}
