.header {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 1rem;
}

.newClientContainer {
  display: flex;
  gap: 1rem;
  flex-direction: column-reverse;
  justify-content: flex-end;
  align-items: flex-start;
}

.title {
  font-weight: 400;
  font-family: Inter;
  font-size: 24px;
  line-height: 29.05px;
  color: #000;
  margin-top: 32px;
  margin-bottom: 24px;
}

@media screen and (min-width: 650px) {
  .newClientContainer {
  	flex-direction: row;
  }
}
