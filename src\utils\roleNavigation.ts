import { MENU_VISIBILITY_BY_ROLE, MENU_ROUTES } from '@/constants/menu'
import { RoleName } from '@/constants/roles'

/**
 * Obtiene la primera ruta accesible para un rol específico
 * @param roleName - Nombre del rol del usuario
 * @returns La primera ruta permitida para el rol o '/home' como fallback
 */
export const getDefaultRouteForRole = (roleName: RoleName | string | null): string => {
  if (!roleName) {
    return MENU_ROUTES.HOME
  }

  const allowedRoutes = MENU_VISIBILITY_BY_ROLE[roleName]
  
  if (!allowedRoutes || allowedRoutes.length === 0) {
    return MENU_ROUTES.HOME
  }

  return allowedRoutes[0]
}

/**
 * Verifica si un rol tiene acceso a una ruta específica
 * @param roleName - Nombre del rol del usuario
 * @param route - Ruta a verificar
 * @returns true si el rol tiene acceso a la ruta, false en caso contrario
 */
export const hasRouteAccess = (roleName: RoleName | string | null, route: string): boolean => {
  if (!roleName) {
    return false
  }

  const allowedRoutes = MENU_VISIBILITY_BY_ROLE[roleName]
  
  if (!allowedRoutes) {
    return false
  }

  // Verificar si la ruta actual coincide con alguna de las rutas permitidas
  // Usamos startsWith para manejar subrutas (ej: /transferencias/nueva)
  return allowedRoutes.some(allowedRoute => route.startsWith(allowedRoute))
}

/**
 * Obtiene todas las rutas accesibles para un rol específico
 * @param roleName - Nombre del rol del usuario
 * @returns Array de rutas permitidas para el rol
 */
export const getAllowedRoutesForRole = (roleName: RoleName | string | null): string[] => {
  if (!roleName) {
    return []
  }

  return MENU_VISIBILITY_BY_ROLE[roleName] || []
}
