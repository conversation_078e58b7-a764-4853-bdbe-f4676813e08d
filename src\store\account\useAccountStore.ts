/* eslint-disable @typescript-eslint/no-explicit-any */
import * as accountApi from '@/api/endpoints/account'
import { create } from 'zustand'
import {
  AccountData,
  AccountResponse,
  AccountListParams,
  AccountDetail,
  AccountDetails,
} from '@/types/account/types'
import { getAccountNumbers, getUserByEmail } from '@/api/endpoints/user'

type Actions = {
  fetchAccounts: (params: AccountListParams) => Promise<void>
  fetchAccountById: (id: string) => Promise<void>
  updateAccount: (id: string, data: Partial<AccountData>) => Promise<AccountResponse | null>
  updateAccountSpei: (
    id: string,
    data: { speiIn: boolean; speiOut: boolean },
    refetchAccountParams: AccountListParams
  ) => Promise<AccountResponse | null>
  updateAccountStatus: (
    id: string,
    refetchAccountParams: AccountListParams
  ) => Promise<AccountResponse | null>
  deleteAccount: (id: string) => Promise<void>
  deleteSelectedAccount: () => Promise<void>
  clearSelected: () => void
  setAccountSelected: (account: AccountResponse) => void
  fetchAccountDetails: (accountId: string | null) => Promise<void>
  clearAccountDetails: () => void
}

type State = {
  accounts: AccountResponse[]
  accountSelected: AccountDetail | null
  accountClientSelected: AccountResponse | null
  isLoading: boolean
  error: string | null
  accountCount: number
  accountDetails: AccountDetails | null
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const useAccountStore = create<State & Actions>()((set, get) => ({
  accounts: [],
  accountSelected: null,
  accountClientSelected: null,
  accountCount: 0,
  isLoading: false,
  error: null,
  accountDetails: null,

  fetchAccounts: async params => {
    set({ isLoading: true, error: null })
    try {
      const { userAccounts, count } = await accountApi.getAccounts(params)
      set({ accounts: userAccounts, accountCount: count })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al obtener las cuentas' })
    } finally {
      set({ isLoading: false })
    }
  },

  fetchAccountById: async id => {
    const { accountSelected, fetchAccountDetails } = get()

    if (accountSelected?.id === id) {
      if (accountSelected.email) {
        await fetchAccountDetails(accountSelected.email)
      }
      return
    }

    set({ isLoading: true, error: null })

    try {
      const account = await accountApi.getAccountById(id)
      
      set({ 
        accountSelected: {
          ...account,
          createdAt: account.createdAt
        } 
      })

      if (account.email) {
        await fetchAccountDetails(account.email)
      }
    } catch (err: any) {
      const message = err?.response?.data?.message || 'Error al obtener la cuenta'
      set({ error: message })
    } finally {
      set({ isLoading: false })
    }
  },

  updateAccount: async (id, data) => {
    set({ isLoading: true, error: null })
    try {
      const response = await accountApi.updateAccount(id, data)
      return response
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al actualizar la cuenta' })
      return null
    } finally {
      set({ isLoading: false })
    }
  },

  updateAccountSpei: async (id, data, refetchAccountParams) => {
    set({ isLoading: true, error: null })
    try {
      const response = await accountApi.updateAccountSpei(id, data)
      await get().fetchAccounts(refetchAccountParams)
      return response
    } catch (err: any) {
      set({
        error:
          err?.response?.data?.message || 'Error al actualizar la configuración SPEI de la cuenta',
      })
      return null
    } finally {
      set({ isLoading: false })
    }
  },

  updateAccountStatus: async (id, refetchAccountParams) => {
    set({ isLoading: true, error: null })
    try {
      const response = await accountApi.updateAccountStatus(id)
      await get().fetchAccounts(refetchAccountParams)
      return response
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al actualizar el estado de la cuenta' })
      return null
    } finally {
      set({ isLoading: false })
    }
  },

  deleteAccount: async id => {
    set({ isLoading: true, error: null })
    try {
      await accountApi.deleteAccount(id)
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al eliminar la cuenta' })
    } finally {
      set({ isLoading: false })
    }
  },

  deleteSelectedAccount: async () => {
    const { accountClientSelected, accounts, deleteAccount } = get()
    if (!accountClientSelected) return

    try {
      await deleteAccount(accountClientSelected.id)
      set({
        accounts: accounts,
        accountSelected: null,
      })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al eliminar la cuenta' })
    }
  },

  fetchAccountDetails: async email => {
    if (get().accountDetails?.email === email) return

    set({ isLoading: true, error: null })
    try {
      if (!email) {
        set({ error: 'No se proporcionó un ID de cuenta' })
        return
      }

      const [user, numbers] = await Promise.all([getUserByEmail(email), getAccountNumbers(email)])

      set({
        accountDetails: {
          name: user.name,
          email: user.email,
          accountNumberConvenia: numbers.account_id.split('-')[0],
          accountNumberTransfer: numbers.clabe,
          accountNumberConveniaComplete: numbers.account_id,
          available_resource: numbers.available_resource || 0,
          convenia_account: user.convenia_account || '',
        },
      })
    } catch (err: any) {
      console.error('Error al obtener los números de cuenta:', err)
      set({ error: 'Error al obtener los números de cuenta' })
    } finally {
      set({ isLoading: false })
    }
  },

  clearSelected: () => set({ accountSelected: null }),

  setAccountSelected: account => set({ accountClientSelected: account }),

  clearAccountDetails: () => set({ accountDetails: null }),
}))
