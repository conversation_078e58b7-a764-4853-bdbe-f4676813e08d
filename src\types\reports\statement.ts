export interface AdminData {
  companyName: string;
}

export interface UserData {
  name: string;
  clabe: string;
  account: string;
  rfc: string;
  address?: {
    id: string
    state: string
    city: string
    colonia: string
    street: string
    numExt: number
    zipCode: string
  };
}

export interface Transaction {
  id: string;
  transactionDate: Date;
  concept: string;
  amount: number;
  currentAmount: string;
  type: 'IN' | 'OUT';
}

export interface Statement {
  adminData: AdminData;
  userData: UserData;
  startDate: Date;
  endDate: Date;
  lastAmount: string;
  averageAmount: string;
  currentAmount: string;
  amountIn: string;
  amountOut: string;
  transactions: Transaction[];
}

export interface StatementParams {
  userId: string;
  startDate: string;
  endDate: string;
}
