import { AdminClient } from "../admin/types"

export interface UserData {
  name: string
  email: string
  phone: number
  password: string
  created_by: string
  enabled?: boolean
  membership_number?: number
  personDocPath?: string
  admin_data?: string
  address?: string
  role: string
}

export interface UserConveniaData {
  name: string
  lastname?: string
  email: string
  phone: number
  password: string | null
  created_by?: string
  method?: string
  rol: string
  admin_data: string | null
  admin?: string | null
  dialing_code?: string | null
  country_code?: string | null
}

export interface UserRole {
  id: string
  type: string
  name: string
  description: string
}

export interface AdminUserResponse {
  id: string
  name: string 
  email: string
  role: {
    id: string
    name: string
  }
  createdAt: string
}

export interface AdminUsersResponse {
  users: AdminUserResponse[]
  count: number
}

export interface UserResponse {
  id: string
  name: string
  email: string
  phone: number
  createdAt: string
  convenia_account?: string
  roles: UserRole[]
  admin: AdminClientUser[] | null
}

export type AdminClientUser = Omit<AdminClient, 'amount' | 'manager'>

export interface VerifyOTPCodePayload {
  email: string
  code: string
  access: string
}

export interface FindUserWithAdminResponse {
  id: string;
  enabled: boolean;
  name: string;
  email: string;
  phone: number;
  spei_in?: number;
  spei_out?: number;
  target_refound?: number;
  ambassador?: string;
  convenia_account?: string;
  membership_number?: number;
}