'use client'
import { useEffect, useState } from 'react'
import { useCostCommissionsReport } from '@/hooks/reportes'
import { TypeCommission } from '@/types/reports/convenia-movements'
import { getAliasCompanyList } from '@/api/endpoints/admin'
import ReportFilters from '@/app/components/ReportFilters'
import Select from '@/app/components/Select'
import DateFilter from '@/app/components/DateFilter'

const COMMISSION_OPTIONS = [
  { value: TypeCommission.FUNDING, label: 'Account Funding' },
  { value: TypeCommission.IN, label: 'SPEI IN' },
  { value: TypeCommission.OUT, label: 'SPEI OUT' },
]

interface CostoComisionesProps {
  onLoadingChange: (loading: boolean, message?: string) => void
}

const CostoComisiones = ({ onLoadingChange }: CostoComisionesProps) => {
  const [dateClearKey, setDateClearKey] = useState(0)
  const [aliasOptions, setAliasOptions] = useState<{ id: string; alias_company: string }[]>([])
  const [isLoadingAlias, setIsLoadingAlias] = useState(false)
  const [hasLoadedAlias, setHasLoadedAlias] = useState(false)

  const [form, setForm] = useState({
    dateFrom: '',
    dateTo: '',
    typeCommission: TypeCommission.IN,
    aliasCompany: '',
  })

  const [activeFilters, setActiveFilters] = useState<string[]>([
    'Empresa: Todas las empresas',
    `Estado: ${COMMISSION_OPTIONS.find(opt => opt.value === TypeCommission.IN)?.label}`,
  ])

  const { isLoading, loadingMessage, downloadReport } = useCostCommissionsReport()

  /** Maneja cambios y burbujas */
  const handleFilterChange = (name: keyof typeof form, value: string) => {
    setForm(prev => ({ ...prev, [name]: value }))

    setActiveFilters(prev => {
      const prefixes: Record<string, string> = {
        aliasCompany: 'Empresa:',
        typeCommission: 'Estado:',
      }

      const base = prev.filter(f => !f.startsWith(prefixes[name]))

      if (value === '' || value === undefined) {
        if (name === 'aliasCompany') return [...base, 'Empresa: Todas las empresas']
        if (name === 'typeCommission') return [...base, 'Estado: Todos los estados']
        return base
      }

      switch (name) {
        case 'aliasCompany':
          return [...base, `Empresa: ${value}`]
        case 'typeCommission':
          return [
            ...base,
            `Estado: ${
              COMMISSION_OPTIONS.find(opt => opt.value === value)?.label || value
            }`,
          ]
        default:
          return base
      }
    })
  }

  /** Reset de filtros */
  const resetFilters = () => {
    setForm({
      dateFrom: '',
      dateTo: '',
      typeCommission: TypeCommission.IN,
      aliasCompany: '',
    })
    setActiveFilters([
      'Empresa: Todas las empresas',
      `Estado: ${COMMISSION_OPTIONS.find(opt => opt.value === TypeCommission.IN)?.label}`,
    ])
    setDateClearKey(k => k + 1)
  }

  /** Manejo de fechas */
  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      setForm(prev => ({ ...prev, dateFrom: '', dateTo: '' }))
      setActiveFilters(prev => prev.filter(f => !f.startsWith('Fecha')))
      return
    }

    if (dates.length === 1) {
      setForm(prev => ({ ...prev, dateFrom: dates[0], dateTo: dates[0] }))
      const [y, m, d] = dates[0].split('-')
      const formatted = `${d}/${m}/${y}`
      setActiveFilters(prev => [
        ...prev.filter(f => !f.startsWith('Fecha')),
        `Fecha: ${formatted}`,
      ])
    } else if (dates.length === 2) {
      setForm(prev => ({ ...prev, dateFrom: dates[0], dateTo: dates[1] }))
      const formatDate = (dateStr: string) => {
        const [y, m, d] = dateStr.split('-')
        return `${d}/${m}/${y}`
      }
      const formattedStart = formatDate(dates[0])
      const formattedEnd = formatDate(dates[1])

      setActiveFilters(prev => [
        ...prev.filter(f => !f.startsWith('Fecha')),
        `Fecha inicio: ${formattedStart} – Fecha fin: ${formattedEnd}`,
      ])
    }
  }

  /** Carga de alias solo al enfocar select */
  const handleSelectFocus = () => {
    if (hasLoadedAlias || isLoadingAlias) return
    setIsLoadingAlias(true)
    getAliasCompanyList()
      .then(response => {
        setAliasOptions(response)
        setHasLoadedAlias(true)
      })
      .catch(() => {})
      .finally(() => setIsLoadingAlias(false))
  }

  /** Descarga de reporte */
  const handleDownloadReport = async () => {
    await downloadReport(form)
    resetFilters()
  }

  /** Loading del padre */
  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onReset={resetFilters}
      onSubmit={e => e.preventDefault()}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Costos y Comisiones"
      downloadOptions={{
        onlyExcel: true,
        onExcelDownload: handleDownloadReport,
      }}
    >
      {/* Alias de la empresa */}
      <Select
        label="Alias de la empresa"
        name="aliasCompany"
        onChange={e => handleFilterChange('aliasCompany', e.target.value)}
        value={form.aliasCompany}
        onFocus={handleSelectFocus}
      >
        <option value="">Todas las empresas</option>
        {isLoadingAlias && <option value="" disabled>Cargando alias...</option>}
        {!isLoadingAlias && !hasLoadedAlias && <option value="" disabled>Sin alias disponibles</option>}
        {aliasOptions.map(option => (
          <option key={option.id} value={option.alias_company}>
            {option.alias_company}
          </option>
        ))}
      </Select>

      {/* Fechas */}
      <DateFilter
        onDateChange={handleDateChange}
        mode="range"
        clearTrigger={dateClearKey}
        label="Rango de fechas"
        placeholder="Selecciona el rango de fechas"
      />

      {/* Estado de comisión */}
      <Select
        label="Estado de transacción"
        name="typeCommission"
        onChange={e => handleFilterChange('typeCommission', e.target.value)}
        value={form.typeCommission}
      >
        <option value="">Todos los estados</option>
        {COMMISSION_OPTIONS.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Select>
    </ReportFilters>
  )
}

export default CostoComisiones
