'use client'
import type { NextPage } from 'next'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import styles from '../../styles/SuccessResetPassword.module.css'
import Button from '../../Button'

export type CardType = {
  className?: string
}

const SuccessResetPassword: NextPage<CardType> = ({ className = '' }) => {
  const router = useRouter()

  const onClick = () => {
    router.push('/login')
  }

  return (
    <div className={[styles.card, className].join(' ')}>
      <section className={styles.cardContent}>
        <div className={styles.illustrationWrapper}>
          <div className={styles.iconUnlockedContainer}>
            <Image
              className={styles.iconUnlocked}
              loading="lazy"
              width={116}
              height={136}
              alt="Ícono de candado abierto"
              src="/logo-unlocked.svg"
            />
          </div>
          <div className={styles.successMessageContainer}>
            <div className={styles.iconCheckContainer}>
              <Image
                className={styles.iconCheck}
                loading="lazy"
                width={50}
                height={50}
                alt="Ícono de verificación"
                src="/logo-gold-check.svg"
              />
            </div>
            <h1 className={styles.title} style={{ fontWeight: 500 }}>
              Contraseña restablecida
            </h1>
            <div className={styles.description}>
              Tu contraseña se ha restablecido correctamente.
            </div>
          </div>
        </div>
      </section>
      <Button
        text={'Ir a inicio de sesión'}
        type="submit"
        disabled={false}
        fullWidth
        onClick={onClick}
      />
    </div>
  )
}

export default SuccessResetPassword
