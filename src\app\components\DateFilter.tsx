/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { LuCalendar } from 'react-icons/lu'
import { DayPicker, DateRange } from 'react-day-picker'
import 'react-day-picker/dist/style.css'
import styles from './styles/Calendar.module.css'

type SelectionMode = 'single' | 'range'

interface Props {
  onDateChange: (dates: string[]) => void
  clearable?: boolean
  mode?: SelectionMode
  label?: string
  placeholder?: string
  clearTrigger?: any
  defaultDate?: string
}

const DateFilter: React.FC<Props> = ({
  onDateChange,
  mode = 'single',
  label = 'Fechas',
  placeholder,
  clearable = false,
  clearTrigger,
  defaultDate,
}) => {
  const prevClearableRef = useRef<boolean>(clearable)
  const [selectedDates, setSelectedDates] = useState<Date[]>([])
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>()
  const [isOpen, setIsOpen] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkSize = () => setIsMobile(window.innerWidth < 800)
    window.addEventListener('resize', checkSize)
    checkSize()
    return () => window.removeEventListener('resize', checkSize)
  }, [])

  useEffect(() => {
    if (defaultDate) {
      const [year, month, day] = defaultDate.split('-').map(Number)
      const date = new Date(year, month - 1, day)

      if (mode === 'single') {
        setSelectedDates([date])
        onDateChange([formatDateForAPI(date)]) // dispara el callback con la fecha formateada
      } else if (mode === 'range') {
        setSelectedRange({ from: date, to: undefined })
        onDateChange([formatDateForAPI(date)])
      }
    }
  }, [defaultDate])

  const handleToggleCalendar = () => {
    setIsOpen(o => !o)
    if (!isOpen && !isMobile) {
      setTimeout(() => {
        containerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }, 100)
    }
  }

  const clearSelection = useCallback(
    (e?: React.MouseEvent) => {
      e?.preventDefault()
      e?.stopPropagation()
      setSelectedDates([])
      setSelectedRange(undefined)
      onDateChange([])
      setIsOpen(false)
    },
    [onDateChange]
  )

  const createLocalDate = (date: Date) =>
    new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0)

  const formatDateForAPI = (date: Date) => {
    const d = createLocalDate(date)
    const day = String(d.getDate()).padStart(2, '0')
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const year = d.getFullYear()
    return `${year}-${month}-${day}`
  }

  // SINGLE
  const handleSingleSelect = useCallback(
    (date: Date | undefined) => {
      if (!date) return
      const local = createLocalDate(date)
      const isSame = selectedDates[0]?.getTime() === local.getTime()
      const updated = isSame ? [] : [local]
      setSelectedDates(updated)
      onDateChange(updated.map(formatDateForAPI))
    },
    [selectedDates, onDateChange]
  )

  // RANGE –> ahora devuelve [from] si sólo hay inicio, o [from,to] si completas
  const handleRangeSelect = useCallback(
    (range: DateRange | undefined) => {
      setSelectedRange(range)
      if (range?.from) {
        const from = createLocalDate(range.from)
        if (range.to) {
          const to = createLocalDate(range.to)
          onDateChange([from, to].map(formatDateForAPI))
        } else {
          onDateChange([from].map(formatDateForAPI))
        }
      } else {
        onDateChange([])
      }
    },
    [onDateChange]
  )

  const dayPickerConfig = useMemo(
    () => ({
      single: {
        mode: 'single' as const,
        selected: selectedDates[0],
        onSelect: handleSingleSelect,
        modifiersClassNames: { selected: styles.goldPill },
        disabled: { after: new Date() },
      },
      range: {
        mode: 'range' as const,
        selected: selectedRange,
        onSelect: handleRangeSelect,
        modifiersClassNames: {
          selected: styles.goldPill,
          range_start: styles.rangeStart,
          range_end: styles.rangeEnd,
          range_middle: styles.rangeMiddle,
        },
        disabled: { after: new Date() },
      },
    }),
    [selectedDates, selectedRange, handleSingleSelect, handleRangeSelect]
  )

  useEffect(() => {
    const onClickOutside = (e: MouseEvent) => {
      if (!isMobile && containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', onClickOutside)
    return () => document.removeEventListener('mousedown', onClickOutside)
  }, [isMobile])

  useEffect(() => {
    if (clearable && !prevClearableRef.current) clearSelection()
    prevClearableRef.current = clearable
  }, [clearable, clearSelection])

  // Limpiar fechas si cambia clearTrigger
  useEffect(() => {
    if (clearTrigger !== undefined) {
      if (defaultDate) {
        // ✅ Si hay una defaultDate, la cargamos en vez de limpiar todo
        const [year, month, day] = defaultDate.split('-').map(Number)
        const date = new Date(year, month - 1, day)
        setSelectedDates([date])
        onDateChange([formatDateForAPI(date)])
      } else {
        // ✅ Si no hay defaultDate, sí limpiamos
        clearSelection()
      }
    }
  }, [clearTrigger])

  const config = dayPickerConfig[mode]

  const summary = (() => {
    if (mode === 'single' && selectedDates[0]) {
      return selectedDates[0].toLocaleDateString()
    }
    if (mode === 'range' && selectedRange?.from) {
      const from = selectedRange.from.toLocaleDateString()
      const to = selectedRange.to?.toLocaleDateString()
      return to ? `${from} – ${to}` : from
    }
    return ''
  })()

  const CalendarComponent = () => (
    <DayPicker
      {...config}
      className={styles.customCalendar}
      showOutsideDays
      fixedWeeks
      footer={placeholder ? <p className={styles.footer}>{placeholder}</p> : undefined}
    />
  )

  return (
    <div className={styles.dropdownContainer} ref={containerRef}>
      <button
        className={styles.toggleButton}
        onClick={handleToggleCalendar}
        aria-expanded={isOpen}
        aria-haspopup="dialog"
      >
        <LuCalendar size={20} aria-hidden="true" /> {label}
        {summary && <span className={styles.selectedDate}>{summary}</span>}
      </button>

      {isOpen &&
        (isMobile ? (
          <div className={styles.modalBackdrop} onClick={handleToggleCalendar}>
            <div className={styles.modalContent} onClick={e => e.stopPropagation()}>
              <CalendarComponent />
            </div>
          </div>
        ) : (
          <div className={styles.calendarWrapper} role="dialog" aria-modal="true">
            <div className={styles.calendarFrame}>
              <CalendarComponent />
            </div>
          </div>
        ))}
    </div>
  )
}

export default DateFilter
