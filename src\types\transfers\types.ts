/* eslint-disable @typescript-eslint/no-explicit-any */
export interface TransferWithDockData {
  num_clabe: string
  email: string
  amount: number
  description: string
  beneficiaryName?: string
  legalBank?: string
  NameBank?: string
}

export interface TransferDockResponse {
  transaction_id: string
  external_transaction_id: string
  reference: string
  cep: null | string
  commission: number
}

export interface TransferWithDockResponse {
  message: string
  data: TransferDockResponse
  error?: any
  statusCode: number
}

export interface Bank {
  code: string
  name: string
  legalCode: string
  isActive: boolean
}

export interface GetBanksResponse {
  statusCode: number
  message: string
  banks: Bank[]
}

export interface TransferToDockData {
  num_clabe: string
  email: string
  amount: number
  description: string
  beneficiaryBank?: string
  beneficiaryName?: string
}

export interface TransferToDockResponse {
  message: string
  data?: Record<string, any> // o un tipo específico si sabes qué estructura tiene
  error?: any
  statusCode: number
}

export interface TransferFormData {
  cuentaDestino: string
  nombreBeneficiario: string
  importe: string
  conceptoBeneficiario: string
  numeroReferencia: string
  tipoTransferencia: 'SPEI' | 'CONVENIA'
  banco?: string
  numeroCuenta: string
  operationFolio: string
  trackingKey: string
  commission: number
}

export interface TransferOrderResponse {
  id: string;
  reference_dock_id?: string;
  payment_type: string; // o si tienes más valores, cambia por PaymentEnum
  bank?: string;
  bank_name?: string;
  beneficiary_name?: string;
  beneficiary_account?: string;
  player_account?: string;
  reference: number;
  status_dock?: string;
  status_transfer?: string;
  email: string;
  player_account_id: string;
  order_id?: string;
  amount?: number;
  cep?: string | null;
  commission?: string;
  concept?: string;
  created_at: Date;
  updated_at: Date;
}

// Massive Transfer
export type BulkTransferData = {
  num_clabe: string
  email: string
  amount: number
  description: string
  beneficiaryName?: string
  legalBank?: string
  NameBank?: string
}

export type BulkTransferResponse = {
  id: string
  total: number
  createdAt: string
  status: BulkTransfersStatus
  successCount: number
  failureCount: number
}

export type BulkTransferError = {
  id: string
  errorMessage: string
  createdAt: string
  beneficiatyAccount?: string
}

export enum BulkTransfersStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}
