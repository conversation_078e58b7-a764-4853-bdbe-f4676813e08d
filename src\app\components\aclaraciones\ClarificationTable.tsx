/* eslint-disable @next/next/no-img-element */
import React from 'react'
import { LuTrash2 } from 'react-icons/lu'
import styles from '../styles/ClientsTable.module.css'
import ClarificationTableStyles from '../styles/ClarificationTable.module.css'
import { useRouter } from 'next/navigation'
import { Clarification } from '@/types/clarification/types'
import { useClarificationStore } from './store/useClarificationStore'
import { formatClarificationDate } from '@/utils/formatters'

type Props = {
  onDelete: (clarification: Clarification) => void
}
const ClarificationTable: React.FC<Props> = ({ onDelete }) => {
  const { clarifications } = useClarificationStore()
  const router = useRouter()

  return (
    <div className={ClarificationTableStyles.tableWrapper}>
      <p className={ClarificationTableStyles.tableTitle}>Últimas aclaraciones</p>
      <table className={styles.table}>
        <thead>
          <tr>
            <th>Usuario</th>
            <th>Tipo de aclaración</th>
            <th>Estatus</th>
            <th>Número de reporte</th>
            <th>Fecha y hora</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          {clarifications.length > 0 ? (
            clarifications.map((clarification, index) => (
              <tr
                key={index}
                onClick={() =>
                  router.push(`
                /aclaraciones/${clarification.trackingNumber}
                `)
                }
              >
                <td>
                  <div className={ClarificationTableStyles.userContent}>
                    <div className={ClarificationTableStyles.imageContainer}>
                      <img
                        // src={clarification.user.avatar}
                        src="icon-convenia.svg"
                        alt="Avatar"
                        className={ClarificationTableStyles.icon}
                      />
                    </div>
                    <div className={ClarificationTableStyles.userTextContainer}>
                      <strong>{clarification.user.name}</strong>
                      <span>{clarification.user.email}</span>
                    </div>
                  </div>
                </td>
                <td>{clarification.type}</td>
                <td>
                  <div
                    className={
                      ClarificationTableStyles.tag +
                      ' ' +
                      ClarificationTableStyles[clarification.status]
                    }
                  >
                    {clarification.status}
                  </div>
                </td>
                <td>{clarification.trackingNumber}</td>
                <td>
                  {clarification?.createdAt ? formatClarificationDate(clarification.createdAt) : ''}
                </td>
                <td>
                  <div className={styles.actions}>
                    <button
                      className={styles.actionButton}
                      onClick={() => onDelete(clarification)}
                      disabled
                    >
                      <LuTrash2 size={18} />
                    </button>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={6} className={styles.noResults}>
                No se encontraron resultados
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  )
}

export default ClarificationTable
