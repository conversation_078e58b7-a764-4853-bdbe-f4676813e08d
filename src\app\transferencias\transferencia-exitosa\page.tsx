/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client'
import React, { useEffect, useState, useRef } from 'react'
import styles from '../../components/styles/SucessTransfer.module.css'
import { FaArrowRight } from 'react-icons/fa6'
import Button from '@/app/components/Button'
import ButtonOutlined from '@/app/components/ButtonOutlined'
import Image from 'next/image'
import { useRouter, useSearchParams } from 'next/navigation'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { canDownloadReceipts } from '@/permissions/access'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useTransfersStore } from '@/store/transfers/useTransfersStore'
import LoaderFull from '@/app/components/loader/LoaderFull'
import { NumericFormat } from 'react-number-format'
import { TransferOrderResponse } from '@/types/transfers/types'

// Interface for transfer data
interface TransferData {
  date: string
  amount: string
  commission: string
  sourceAccount: string
  recipient: string
  recipientAccount: string
  concept: string
  reference: string
  operationType: 'TERCERO A TERCERO' | 'CONVENIA'
  operationFolio: string
  trackingKey: string
  transferType: 'CONVENIA' | 'SPEI' // Added transfer type
  transferStatus: string // Added transfer status
}

const TransferenciaExitosa = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const id = searchParams.get('id')
  const containerRef = useRef<HTMLDivElement>(null)
  const { getUserRoleName } = useAuthStore()
  const { transferFormData, transferOrderDataById, fetchTransferOrderById } = useTransfersStore()
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [transferData, setTransferData] = useState<TransferData>({
    date: '',
    amount: '',
    commission: '',
    sourceAccount: '',
    recipient: '',
    recipientAccount: '',
    concept: '',
    reference: '',
    operationType: 'CONVENIA',
    operationFolio: '',
    trackingKey: '',
    transferType: 'CONVENIA', // Default value for mockup
    transferStatus: '',
  })
  const [loading, setLoading] = useState(false)
  const [linkBanxico, setLinkBanxico] = useState('')

  const getTransferStatus = (data: TransferOrderResponse) => {
    if (data?.payment_type === 'DOCK_PAY') {
      if (data?.status_dock === 'APPROVED') return 'COMPLETADO'
      if (data?.status_dock === 'DENIED') return 'CANCELADO'
      return 'PENDIENTE'
    } else {
      if (data?.status_dock === 'APPROVED' && data?.status_transfer === 'scattered')
        return 'COMPLETADO'
      if (data?.status_dock === 'DENIED') return 'CANCELADO'
      if (data?.status_dock === 'APPROVED' && data?.status_transfer === 'sent') return 'PENDIENTE'
      if (
        data?.status_dock === 'APPROVED' &&
        (data?.status_transfer === 'returned' ||
          data?.status_transfer === 'canceled' ||
          data?.status_transfer === null)
      )
        return 'CANCELADO'
    }
  }

  // Function to fetch transfer data from backend
  const fetchTransferData = async () => {
    if (!transferFormData && id) {
      try {
        setLoading(true)
        const data = await fetchTransferOrderById(id)

        if (!data) {
          router.replace('/transferencias')
          return
        }

        setLinkBanxico(data?.cep ?? '')
        setTransferData({
          date: new Date(data.created_at).toLocaleString('es-MX', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }),
          amount: `$${Number(data?.amount || 0).toFixed(2)}`,
          commission: `${data?.commission ? `${Number(data.commission).toFixed(2)}` : '0.00'}`, // Assuming no commission for mockup
          sourceAccount: `****${
            data?.payment_type === 'DOCK_PAY'
              ? data?.player_account_id?.slice(-4)
              : data?.player_account?.slice(-4)
          }`, // Placeholder for source account
          recipient: data?.beneficiary_name || '',
          recipientAccount: `****${data?.beneficiary_account?.slice(-4) || ''}`, // Last 4 digits of the account
          concept: data?.concept || 'Sin concepto especificado',
          reference:
            data?.reference !== undefined && data?.reference !== null ? String(data.reference) : '',
          operationType: data?.payment_type === 'DOCK_PAY' ? 'CONVENIA' : 'TERCERO A TERCERO',
          operationFolio: data?.id || 'Folio no especificado',
          trackingKey: data?.reference_dock_id || 'Clave de rastreo no especificada',
          transferType: data?.payment_type === 'DOCK_PAY' ? 'CONVENIA' : 'SPEI', // Default to SPEI if not specified
          transferStatus: getTransferStatus(data) ?? '',
        })
        setLoading(false)
      } catch (error) {
        setLoading(false)
        // console.error('Error fetching transfer data:', error)
      }
    }
  }

  // Call the fetch function when component mounts
  useEffect(() => {
    fetchTransferData()
  }, [])

  const transferIsConvenia =
    transferData.transferType === 'CONVENIA' || transferOrderDataById?.payment_type === 'DOCK_PAY'
  const title =
    transferData.transferStatus !== 'COMPLETADO'
      ? 'Comprobante de la operación'
      : 'Transferencia exitosa'

  const handleDownloadPDF = async () => {
    if (!containerRef.current) return

    try {
      // Crear un nuevo documento PDF
      const pdf = new jsPDF('p', 'mm', 'a4')
      const pdfWidth = pdf.internal.pageSize.getWidth()

      // Configuración mejorada para html2canvas para capturar todo el contenido
      const canvas = await html2canvas(containerRef.current, {
        scale: 2, // Mayor escala para mejor calidad
        useCORS: true, // Permitir imágenes de otros dominios
        logging: false,
        scrollY: -window.scrollY, // Ajustar por el scroll
        windowHeight: document.documentElement.offsetHeight,
        height: containerRef.current.scrollHeight + 100, // Capturar altura completa
        width: 360, // Establecer el ancho exacto del contenedor
        onclone: document => {
          // Asegurarse de que todos los elementos sean visibles en el clon
          const container = document.querySelector('[data-html2canvas-ignore]')
          if (container) {
            container.removeAttribute('data-html2canvas-ignore')
          }
        },
      })

      const imgData = canvas.toDataURL('image/png')

      // Agregar el logo al PDF
      const logoPath = '/logo-finberry.png'

      // Convertir píxeles a milímetros para el PDF
      const pxToMm = 0.264583 // Factor de conversión: 1px = 0.264583mm
      const containerWidthMm = 360 * pxToMm // Ancho del contenedor en mm

      // Dimensiones del logo
      const logoWidthPx = 280
      const logoHeightPx = 124
      const logoWidth = logoWidthPx * pxToMm
      const logoHeight = logoHeightPx * pxToMm

      // Centrar el logo
      const logoX = (pdfWidth - logoWidth) / 2

      // Añadir el logo al PDF
      pdf.addImage(logoPath, 'PNG', logoX, 10, logoWidth, logoHeight)

      // Calcular dimensiones para mantener la proporción
      const contentWidth = Math.min(containerWidthMm, pdfWidth - 20) // No exceder el ancho de la página
      const contentX = (pdfWidth - contentWidth) / 2 // Centrar el contenido
      const contentHeight = (canvas.height * contentWidth) / canvas.width

      // Posición vertical después del logo
      const pxToMmForSpacing = 0.264583 // Factor de conversión: 1px = 0.264583mm
      const spacingAfterLogo = 50 * pxToMmForSpacing // 24px convertidos a mm
      const contentY = logoHeight + spacingAfterLogo // Espacio exacto de 24px después del logo

      // Verificar si el contenido es demasiado grande para una página
      const maxHeightOnFirstPage = pdf.internal.pageSize.getHeight() - contentY - 10 // 10mm de margen inferior

      if (contentHeight <= maxHeightOnFirstPage) {
        // Si cabe en una página, añadirlo normalmente
        pdf.addImage(imgData, 'PNG', contentX, contentY, contentWidth, contentHeight)
      } else {
        // Si no cabe en una página, dividirlo en múltiples páginas
        let heightLeft = contentHeight
        const position = contentY
        let page = 1

        // Añadir la primera parte en la primera página
        pdf.addImage(imgData, 'PNG', contentX, position, contentWidth, contentHeight, '', 'FAST')
        heightLeft -= maxHeightOnFirstPage

        // Añadir el resto en páginas adicionales si es necesario
        while (heightLeft > 0) {
          pdf.addPage()
          page++

          const heightOnThisPage = Math.min(heightLeft, pdf.internal.pageSize.getHeight() - 20) // 20mm de margen total

          pdf.addImage(
            imgData,
            'PNG',
            contentX,
            10 - maxHeightOnFirstPage * (page - 1),
            contentWidth,
            contentHeight,
            '',
            'FAST'
          )

          heightLeft -= heightOnThisPage
        }
      }

      // Descargar el PDF
      pdf.save('comprobante-transferencia.pdf')
    } catch (error) {
      console.error('Error al generar el PDF:', error)
    }
  }

  useEffect(() => {
    if (!transferFormData) return
    // Update transferData with the form data
    setTransferData({
      date: new Date().toLocaleString('es-MX', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }),
      amount: `$${Number(transferFormData.importe).toFixed(2)}`,
      commission: `${Number(transferFormData.commission).toFixed(2)}`, // Assuming no commission for mockup
      sourceAccount: `****${transferFormData.numeroCuenta.slice(-4)}`, // Placeholder for source account
      recipient: transferFormData.nombreBeneficiario,
      recipientAccount: `****${transferFormData.cuentaDestino.slice(-4)}`, // Last 4 digits of the account
      concept: transferFormData.conceptoBeneficiario,
      reference: transferFormData.numeroReferencia,
      operationType:
        transferFormData.tipoTransferencia === 'CONVENIA' ? 'CONVENIA' : 'TERCERO A TERCERO',
      operationFolio: transferFormData.operationFolio, // Placeholder for operation folio
      trackingKey: transferFormData.trackingKey, // Placeholder for tracking key
      transferType: transferFormData.tipoTransferencia, // Added transfer type
      transferStatus: 'COMPLETADO', // Placeholder for transfer status
    })
  }, [transferFormData])

  return (
    <div>
      {loading && <LoaderFull />}
      <h1 className={styles.titleHead}>Transferencias</h1>
      <div className={styles.container} ref={containerRef}>
        <h1 className={styles.title}>{title}</h1>
        {transferData.transferStatus === 'COMPLETADO' ? (
          <div className={styles.successIcon}>
            <Image src="/gold-check.svg" alt="Success" width={50} height={50} />
          </div>
        ) : (
          ''
        )}
        <div className={styles.date}>Estatus: {transferData.transferStatus}</div>
        <div className={styles.date}>{transferData.date}</div>
        <div className={styles.amount}>
          {
            <NumericFormat
              value={transferData.amount}
              displayType="text"
              thousandSeparator=","
              prefix="$"
              decimalScale={2}
              fixedDecimalScale
            />
          }
        </div>
        {/* <div className={styles.commission}>Comisión ${transferData?.commission}</div> */}

        <div className={styles.infoSection}>
          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>Cuenta origen</span>
            <div className={styles.accountInfo}>{transferData.sourceAccount}</div>
          </div>

          <div className={styles.infoRow}>
            {/* <span className={styles.infoLabel}></span> */}
            <FaArrowRight color="#ACAFC2" fontSize={19} />
            {/* <span className={styles.infoLabel}></span> */}
          </div>

          <div className={styles.infoRow}>
            <span className={styles.infoLabel}>{transferData.recipient}</span>
            <div className={styles.accountInfo}>{transferData.recipientAccount}</div>
          </div>
        </div>

        <div className={styles.infoDescription}>
          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Concepto</span>
            <span className={styles.infoValue}>{transferData.concept}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Referencia</span>
            <span className={styles.infoValue}>{transferData.reference}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Tipo de operación</span>
            <span className={styles.infoValue}>{transferData.operationType}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Folio de operación</span>
            <span className={styles.infoValue}>{transferData.operationFolio}</span>
          </div>

          <div className={styles.desRow}>
            <span className={styles.infoDesLabel}>Clave de rastreo</span>
            <span className={styles.infoValue}>{transferData.trackingKey}</span>
          </div>

          {!transferIsConvenia && linkBanxico && (
            <>
              <div className={styles.desRow}>
                <div className={styles.infoDesLabel}>Verifica el estatus de tu operación</div>
                <a href={linkBanxico} className={styles.verifyLink} target="_blank">
                  www.banxico.org.mx
                </a>
              </div>
            </>
          )}
        </div>
      </div>

      <div
        className={styles.buttonsContainer}
        style={{
          marginTop: transferIsConvenia ? '113px' : '32px',
        }}
      >
        <Button text="Ir al inicio" onClick={() => router.replace('/transferencias')} />
        {canDownloadReceipts(getUserRoleName()) && (
          <ButtonOutlined text="Descargar comprobante" onClick={handleDownloadPDF} />
        )}
      </div>
    </div>
  )
}

export default TransferenciaExitosa
