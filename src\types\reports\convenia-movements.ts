export enum PaymentStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  CANCELED = 'CANCELED',
}

export enum TypeCommission {
  IN = 'SPEI IN',
  OUT = 'SPEI OUT',
  FUNDING = 'ACCOUNT FUNDING',
}

export interface ConveniaMovementsFilters {
  startDate?: string;
  endDate?: string;
  status?: PaymentStatus;
  adminId?: string;
}

export interface ConveniaMovement {
  id: string;
  date: string;
  amount: number;
  status: PaymentStatus;
  adminId: string;
  description?: string;
}
