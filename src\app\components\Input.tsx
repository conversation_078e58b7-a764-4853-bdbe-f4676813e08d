import React, { InputHTMLAttributes } from 'react'
import styles from './styles/NewClientAdmin.module.css'
import inputStyles from './styles/Input.module.css'

type Props = InputHTMLAttributes<HTMLInputElement> & {
  isError?: boolean
  errorText?: string
  label?: string
  className?: string
}

const Input: React.FC<Props> = ({ isError, errorText, className, label, ...rest }) => {
  return (
    <div className={`${styles.inputGroup} ${className}`}>
      {label && <label>{label}</label>}
      <input
        className={`${isError ? styles.error : ''} ${inputStyles.input} ${className || ''}`}
        {...rest}
      />
      {isError && errorText && <p className={styles.error}>{errorText}</p>}
    </div>
  )
}

export default Input
