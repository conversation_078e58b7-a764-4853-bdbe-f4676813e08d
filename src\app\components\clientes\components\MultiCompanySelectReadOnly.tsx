// src/app/components/clientes/components/MultiCompanySelectReadOnly.tsx
import styles from '../../styles/MultiCompanySelector.module.css'

interface Props {
  companies: string[]
}

export default function MultiCompanySelectReadOnly({ companies }: Props) {
  return (
    <div className={styles.contentWrapper}>
      {companies.map((company, index) => (
        <div key={index} className={styles.selectWrapper}>
          <label className={styles.selectLabel}>Empresa</label>
          <div className={styles.selectContainer}>
            <input type="text" value={company} readOnly className={styles.select} />
          </div>
        </div>
      ))}
    </div>
  )
}
