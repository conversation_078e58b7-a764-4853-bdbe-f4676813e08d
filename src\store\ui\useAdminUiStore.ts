/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'

interface AdminUiStore {
  selectedAdminIndex: number
  selectedAdmin: any
  setSelectedAdminIndex: (index: number) => void
  setSelectedAdmin: (admin: any) => void
  removeSelectedAdminIndex: () => void
}

const STORAGE_KEY = 'selectedAdminIndex'
const STORAGE_KEY_ADMIN = 'selectedAdmin'

export const useAdminUiStore = create<AdminUiStore>(set => {
  const storedIndex =
    typeof window !== 'undefined' ? parseInt(localStorage.getItem(STORAGE_KEY) || '0', 10) : 0

  const storedAdmin =
    typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem(STORAGE_KEY_ADMIN) || 'null')
      : null

  return {
    selectedAdminIndex: isNaN(storedIndex) ? 0 : storedIndex,
    selectedAdmin: storedAdmin,

    setSelectedAdminIndex: (index: number) => {
      localStorage.setItem(STORAGE_KEY, index.toString())
      set({ selectedAdminIndex: index })
    },

    setSelectedAdmin: (admin: any) => {
      localStorage.setItem(STORAGE_KEY_ADMIN, JSON.stringify(admin))
      set({ selectedAdmin: admin })
    },

    removeSelectedAdminIndex: () => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(STORAGE_KEY)
      }
      set({ selectedAdminIndex: 0 })
    },
  }
})
