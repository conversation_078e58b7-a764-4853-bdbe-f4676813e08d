import InfoModalContentBase from '../../modals/InfoModal/InfoModalContentBase'
import styles from '../../styles/Transaction.module.css'

type Props = {
  onClose: () => void
}

const Success: React.FC<Props> = ({ onClose }) => {
  return (
    <div className={styles.sucessWrapper}>
      <InfoModalContentBase
        title="¡La dispersión de saldo de realizo con éxito!"
        message="Hemos recibido y almacenado los datos correctamente."
        onPressPrimaryBtn={onClose}
        onClose={() => null}
      />
    </div>
  )
}

export default Success
