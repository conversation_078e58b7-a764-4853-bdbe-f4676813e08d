/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import { ContactState, ContactActions } from '@/types/contact/types'
import {
  saveContact,
  getContactsByUser,
  updateContact,
  deleteContact,
} from '@/api/endpoints/contact'

export const useContactStore = create<ContactState & ContactActions>((set, get) => ({
  contacts: [],
  loading: false,
  error: null,
  selectedContact: null,

  setSelectedContact: contact => set({ selectedContact: contact }),

  addContact: async data => {
    set({ loading: true, error: null })
    try {
      const newContact = await saveContact(data)
      set({ contacts: [...get().contacts, newContact] })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al guardar el contacto' })
    } finally {
      set({ loading: false })
    }
  },

  loadContacts: async (userId, email) => {
    set({ loading: true, error: null })
    try {
      const data = await getContacts<PERSON>yUser(userId, email)
      set({ contacts: data })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al cargar contactos' })
    } finally {
      set({ loading: false })
    }
  },

  editContact: async (id, data) => {
    set({ loading: true, error: null })
    try {
      const updated = await updateContact(id, data)
      const updatedContacts = get().contacts.map(c => (c.id === id ? { ...c, ...updated } : c))
      set({ contacts: updatedContacts })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al editar el contacto' })
    } finally {
      set({ loading: false })
    }
  },

  removeContact: async id => {
    set({ loading: true, error: null })
    try {
      await deleteContact(id)
      set({ contacts: get().contacts.filter(c => c.id !== id) })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al eliminar el contacto' })
    } finally {
      set({ loading: false })
    }
  },
}))
