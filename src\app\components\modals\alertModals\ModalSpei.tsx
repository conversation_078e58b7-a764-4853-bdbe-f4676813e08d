import React from 'react'
import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'

interface ModalSpeiProps extends BaseModalProps {
  numberAccount: string
  isActive: boolean
  speiType: 'in' | 'out' // ← NUEVO
  onPressBtn: () => void
}

const ModalSpei: React.FC<ModalSpeiProps> = ({
  numberAccount,
  isActive,
  speiType,
  onPressBtn,
  open,
  onClose,
}) => {
  const actionText = !isActive ? 'desbloquear' : 'bloquear'
  const speiLabel = speiType === 'in' ? 'SPEI IN' : 'SPEI OUT'

  return (
    <AlertModal
      open={open}
      type="warning"
      title={`¿Estás seguro que deseas ${actionText} el ${speiLabel} en la cuenta ${numberAccount}?`}
      onClose={onClose}
      onPressBtn={onPressBtn}
    />
  )
}

export default ModalSpei
