/* eslint-disable react-hooks/exhaustive-deps */
'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useState, useCallback, useRef } from 'react'
import styles from '../styles/Clients.module.css'
import Pagination from '../Pagination'
import Button from '@/app/components/Button'
import SearchBar from '@/app/components/SearchBar'
import UsersManagementTable from './UsersManagementTable'
import { useUserStore } from '@/store/user/useUserStore'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { canCreateUser, shouldSendAdminId } from '@/permissions/access'
import { RoleName } from '@/constants/roles'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import ModalDeleteAdmin from '../modals/alertModals/ModalDeleteAdmin'
import { useUiStore } from '@/store/ui/useUiStore'
import LoaderFull from '../loader/LoaderFull'

const Users: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [openModal, setOpenModal] = useState(false)
  const [selectedAdminName, setSelectedAdminName] = useState<string | null>(null)
  const [itemsPerPage, setItemsPerPage] = useState(5)
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const { isLoading } = useUiStore()

  const router = useRouter()
  const { fetchConveniaUsers, totalAccountsAdmin, deleteUser } = useUserStore()
  const { getUserRoleName } = useAuthStore()
  const { user } = useAuthStore()
  const { selectedAdminIndex } = useAdminUiStore()
  const [loadingDelete, setLoadingDelete] = useState(false)

  const totalPages = Math.ceil(totalAccountsAdmin / itemsPerPage)
  const userRole = getUserRoleName()

  const roleName = user?.relUserRoleAdmins?.[selectedAdminIndex]?.role?.name || null
  const admin = shouldSendAdminId(roleName as RoleName | null)
    ? user?.relUserRoleAdmins?.[selectedAdminIndex]?.admin?.id || null
    : null

  useEffect(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    debounceTimerRef.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 500)

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [searchTerm])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const baseParams = {
          page: currentPage,
          limit: itemsPerPage,
          q: debouncedSearchTerm,
          admin
        }

        const params = debouncedSearchTerm
          ? { ...baseParams, _t: Date.now() }
          : baseParams

        await fetchConveniaUsers(params)
      } catch (error) {
        console.error('Error fetching users:', error)
      }
    }

    fetchData()
  }, [currentPage, debouncedSearchTerm, fetchConveniaUsers, admin, itemsPerPage])

  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }, [])

  const handlePageChange = (direction: 'next' | 'prev') => {
    setCurrentPage(prev => {
      if (direction === 'prev') return Math.max(prev - 1, 1)
      return prev + 1
    })
  }

  const handleNavigateToNewUser = () => {
    router.push('/gestion-usuarios/new-user-admin')
  }

  const handleOpenDeleteClientModal = (adminName: string) => {
    setSelectedAdminName(adminName) // Guardamos el nombre del admin seleccionado
    setOpenModal(true)
  }

  const handleCloseDeleteClientModal = () => {
    setOpenModal(false)
    setSelectedAdminName(null) // Limpiar el nombre cuando se cierra el modal
  }

  const handleDeleteUser = async () => {
    if (!selectedAdminName) return
    try {
      setLoadingDelete(true)
      await deleteUser(selectedAdminName, admin)
      await fetchConveniaUsers({
        page: currentPage,
        limit: itemsPerPage,
        q: debouncedSearchTerm,
        admin
      })
      handleCloseDeleteClientModal()
      setLoadingDelete(false)
    } catch (error) {
      setLoadingDelete(false)
      console.error('Error al eliminar el usuario:', error)
    }
  }

  const handleItemsPerPageChange = (newLimit: number) => {
    setItemsPerPage(newLimit)
    setCurrentPage(1) // Reset to first page when changing items per page
  }

  return (
    <div>
      {canCreateUser(userRole) && (
        <div className={styles.header}>
          <div className={styles.newClientContainer}>
            <Button text="Nuevo usuario" onClick={handleNavigateToNewUser} />
            <SearchBar placeholder="Buscar" onSearch={handleSearch} />
          </div>
        </div>
      )}

      <UsersManagementTable handleDeleteUserModal={handleOpenDeleteClientModal} />

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPrevPage={() => handlePageChange('prev')}
        onNextPage={() => handlePageChange('next')}
        onNavigatePage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={handleItemsPerPageChange}
      />
      {
        (isLoading || loadingDelete) && <LoaderFull />
      }
      <ModalDeleteAdmin
        open={openModal}
        onClose={handleCloseDeleteClientModal}
        name={selectedAdminName || ''}
        onConfirm={handleDeleteUser}
      />
    </div>
  )
}

export default Users
