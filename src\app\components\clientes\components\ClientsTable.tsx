'use client'
import { MdOutlineEdit } from 'react-icons/md'
import { LuTrash2, Lu<PERSON><PERSON>, LuClipboard } from 'react-icons/lu'
import styles from '../../styles/ClientsTable.module.css'
import { TableAdminProps } from '@/types/types'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { showActionsInClientTable } from '@/permissions/access'

const ClientsTable: React.FC<TableAdminProps> = ({
  clients,
  onViewClient,
  onEditClient,
  onDeleteClient,
}) => {
  const { getUserRoleName } = useAuthStore()
  return (
    <table className={styles.table}>
      <thead>
        <tr>
          <th>Empresa</th>
          <th>Nombre del administrador</th>
          <th>RFC</th>
          <th>Saldo</th>
          <th>Cuentas</th>
          <th>CLABE</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        {clients.length > 0 ? (
          clients.map((client, index) => (
            <tr key={index}>
              <td>
                <strong>{client.companyName}</strong>
                <span>{client.manager?.email ?? 'Sin correo'}</span>
              </td>
              <td>{client.manager?.name ?? 'No asignado'}</td>
              <td>{client.rfc}</td>
              <td>
                {typeof client.amount === 'string'
                  ? Number(client.amount).toLocaleString('es-MX', {
                      style: 'currency',
                      currency: 'MXN',
                    })
                  : 'Sin saldo'}
              </td>
              <td>{client.numAsignedCards}</td>
                <td>
                  <span>{client.manager?.clabe ?? 'Sin CLABE'}</span>
                  {client.manager?.clabe && (
                    <button
                      style={{ marginLeft: 8 }}
                      onClick={() => navigator.clipboard.writeText(client.manager.clabe)}
                      title="Copiar CLABE"
                    >
                      <LuClipboard size={18} />
                    </button>
                  )}
                </td>
              <td>
                <div className={styles.actions}>
                  <button
                    className={styles.actionButton}
                    onClick={() => onViewClient && onViewClient(client)}
                  >
                    <LuEye size={18} />
                  </button>
                  {showActionsInClientTable(getUserRoleName()) && (
                    <>
                      <button className={styles.actionButton} onClick={() => onEditClient(client)}>
                        <MdOutlineEdit size={18} />
                      </button>
                      <button
                        className={styles.actionButton}
                        onClick={() => onDeleteClient(client)}
                      >
                        <LuTrash2 size={18} />
                      </button>
                    </>
                  )}
                </div>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className={styles.noResults}>
              No se encontraron resultados
            </td>
          </tr>
        )}
      </tbody>
    </table>
  )
}

export default ClientsTable
