import AlertModal from '../AlertModal'
import { BaseModalProps } from '@/types/types'
import styles from '../../styles/ModalRegisteredRFC.module.css'
import { handleNumberOnlyInput } from '@/utils/inputRestrictions'
import Loader from '../../loader/Loader'

type ModalRegisteredRFCProps = BaseModalProps & {
  onPressBtn: () => void
  value: string
  onChange: (value: string) => void
  error?: string | null
  loading?: boolean
  reason?: string
  onChangeReason?: (value: string) => void
}

const ModalCardReassignment: React.FC<ModalRegisteredRFCProps> = ({
  open,
  onClose,
  onPressBtn,
  value,
  onChange,
  error,
  loading,
  reason,
  onChangeReason,
}) => {
  return (
    <AlertModal
      open={open}
      type="warning"
      title="Reasignación de tarjeta"
      message="La anterior tarjeta del usuario quedará inactiva, a continuación ingresa los 16 dígitos de la nueva tarjeta."
      textBtn={!loading ? 'Aceptar' : ''}
      renderMessage={() => (
        <div className={styles.modalContainer}>
          {!loading && (
            <div className={styles.companyInfo}>
              <div className={styles.inputGroup}>
                <p className={styles.label}>Nueva tarjeta</p>
                <input
                  className={`${styles.input} ${error ? styles.inputError : ''}`}
                  placeholder="16 dígitos de tarjeta"
                  value={value}
                  maxLength={16}
                  onChange={e => onChange(e.target.value)}
                  onKeyDown={handleNumberOnlyInput}
                />
                {error && <p className={styles.error}>{error}</p>}
              </div>
              <div className={styles.inputGroup}>
                <p className={styles.label}>Motivo de reasignación</p>
                <input
                  className={styles.input}
                  placeholder="Agregar motivo"
                  value={reason || ''}
                  onChange={e => onChangeReason?.(e.target.value)}
                  maxLength={200}
                />
              </div>
            </div>
          )}

          {loading && <Loader />}
        </div>
      )}
      onClose={onClose}
      onPressBtn={onPressBtn}
    />
  )
}

export default ModalCardReassignment
