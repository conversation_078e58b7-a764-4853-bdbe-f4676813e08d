/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { useEffect, useMemo, useState } from 'react'
import { BsArrowLeftRight } from 'react-icons/bs'
import { useConveniaMovementsReport } from '@/hooks/reportes/useConveniaMovementsReport'
import ReportFilters from '@/app/components/ReportFilters'
import Select from '@/app/components/Select'
import DateFilter from '@/app/components/DateFilter'

import { ConveniaMovementsFilters, PaymentStatus } from '@/types/reports/convenia-movements'
import { AdminBasic } from '@/types/admin/types'
import { formatRangeDate } from './report.utils'

const STATUS_OPTIONS = [
  { value: PaymentStatus.PENDING, label: 'Pendiente' },
  { value: PaymentStatus.APPROVED, label: 'Aprobado' },
  { value: PaymentStatus.CANCELED, label: 'Cancelado' },
]

interface ConveniaMovementsFiltersParams {
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
  admins: AdminBasic[]
}

function useConveniaMovementsFilters({
  isClient,
  selectedAdminId,
  onAdminChange,
  admins,
}: ConveniaMovementsFiltersParams) {
  const [dateClearKey, setDateClearKey] = useState(0)
  const [filters, setFilters] = useState<ConveniaMovementsFilters>({
    adminId: selectedAdminId || undefined,
    status: undefined,
    startDate: undefined,
    endDate: undefined,
  })

  const activeFilters = useMemo(() => {
    const list: string[] = []

    const adminLabel = filters.adminId
      ? admins.find(a => a.id === filters.adminId)?.alias ?? filters.adminId
      : 'Todas las empresas'
    list.push(`Empresa: ${adminLabel}`)

    if (filters.startDate && filters.endDate) {
      list.push(formatRangeDate(new Date(filters.startDate), new Date(filters.endDate)))
    } else {
      list.push(`Fecha: Todas`)
    }

    const statusLabel = filters.status
      ? STATUS_OPTIONS.find(o => o.value === filters.status)?.label
      : 'Todos los estados'
    list.push(`Estado: ${statusLabel}`)

    return list
  }, [filters, admins])


  const handleFilterChange = (
    name: keyof ConveniaMovementsFilters,
    value: string | Date | undefined
  ) => {
    if (isClient && name === 'adminId') return
    setFilters((prev: any) => ({ ...prev, [name]: value }))
    if (name === 'adminId') {
      onAdminChange(value as string)
    }
  }

  const handleReset = () => {
    // Si es cliente, mantenemos su empresa fija
    if (isClient) {
      onAdminChange(selectedAdminId)
      setFilters({
        adminId: selectedAdminId, // sigue viendo su admin
        status: undefined,
        startDate: undefined,
        endDate: undefined,
      })
    } else {
      // Si NO es cliente, limpiamos la empresa a "Todas"
      onAdminChange('') // <-- limpiamos también en el padre
      setFilters({
        adminId: undefined, // ahora sí se limpia
        status: undefined,
        startDate: undefined,
        endDate: undefined,
      })
    }

    setDateClearKey(k => k + 1)
  }

  // Maneja solo UNA burbuja de fechas
  const handleDateChange = (dates: string[]) => {
    if (dates.length === 0) {
      handleFilterChange('startDate', undefined)
      handleFilterChange('endDate', undefined)
      return
    }

    if (dates.length === 1) {
      handleFilterChange('startDate', dates[0])
      handleFilterChange('endDate', dates[0])
    } else if (dates.length === 2) {
      handleFilterChange('startDate', dates[0])
      handleFilterChange('endDate', dates[1])
    }
  }

  useEffect(() => {
    if (!selectedAdminId || admins.length === 0) return
    setFilters(prev => ({ ...prev, adminId: selectedAdminId || undefined }))
  }, [selectedAdminId, admins])

  return {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleDateChange,
    handleReset,
  }
}

interface ConveniaMovementsReportProps {
  onLoadingChange: (loading: boolean, message?: string) => void
  admins: AdminBasic[]
  isClient: boolean
  selectedAdminId: string
  onAdminChange: (adminId: string) => void
}

const ConveniaMovementsReport = ({
  admins,
  onLoadingChange,
  isClient,
  selectedAdminId,
  onAdminChange,
}: ConveniaMovementsReportProps) => {
  const { isLoading, loadingMessage, downloadReport } = useConveniaMovementsReport()

  const {
    filters,
    activeFilters,
    dateClearKey,
    handleFilterChange,
    handleDateChange,
    handleReset,
  } = useConveniaMovementsFilters({
    isClient, selectedAdminId, onAdminChange, admins
  })

  const handleSubmit = (e: React.FormEvent) => e.preventDefault()

  const handleDownload = async () => {
    const raw = {
      ...filters,
      ...(isClient ? { adminId: selectedAdminId } : {}),
    }
    const params = Object.fromEntries(Object.entries(raw).filter(([, v]) => v != null && v !== ''))
    await downloadReport(params)
  }

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) =>
    handleFilterChange('status', e.target.value as PaymentStatus)

  useEffect(() => {
    onLoadingChange(isLoading, loadingMessage)
  }, [isLoading, loadingMessage, onLoadingChange])

  return (
    <ReportFilters
      onSubmit={handleSubmit}
      onReset={handleReset}
      activeFilters={activeFilters}
      isLoading={isLoading}
      title="Movimientos entre Cuentas CONVENIA"
      icon={<BsArrowLeftRight />}
      downloadOptions={{
        onlyExcel: true,
        onExcelDownload: handleDownload,
      }}
    >
      <Select
        name="adminId"
        label="Empresa"
        value={filters.adminId || ''}
        onChange={e => handleFilterChange('adminId', e.target.value)}
        disabled={isClient}
      >
        <option value="">Todas las empresas</option>
        {admins.map(admin => (
          <option key={admin.id} value={admin.id}>
            {admin.alias || admin.companyName}
          </option>
        ))}
      </Select>

      <DateFilter
        mode="range"
        onDateChange={handleDateChange}
        label="Rango de fechas"
        placeholder="Selecciona el rango de fechas"
        clearTrigger={dateClearKey}
      />

      <Select
        name="status"
        label="Estado del movimiento"
        value={filters.status || ''}
        onChange={handleStatusChange}
      >
        <option value="">Todos los estados</option>
        {STATUS_OPTIONS.map(opt => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </Select>
    </ReportFilters>
  )
}

export default ConveniaMovementsReport
