/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'
import styles from '../../styles/NewUserAdmin.module.css'
import { useRouter } from 'next/navigation'
import { FaArrowLeft } from 'react-icons/fa'
import Button from '@/app/components/Button'
import ContactForm from '../components/ContactForm'
import useFormValidation from '@/hooks/useFormValidationUser'
import { ClientAdminProps } from '@/types/types'
import { useUserStore } from '@/store/user/useUserStore'
import { toast } from 'react-toastify'
import { getUserCreationErrorMessage } from '@/utils/errorHandler'
import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useAdminUiStore } from '@/store/ui/useAdminUiStore'
import { useRoleStore } from '@/store/role/useRoleStore'
import { parsePhoneNumberFromString } from 'libphonenumber-js'
import debounce from 'lodash.debounce'
import { FindUserWithAdminResponse } from '@/types/user/types'
import { assignRoleToConveniaUser } from '@/api/endpoints/user'
import { ROLE_TYPES } from '@/constants/roles'
import { generateRegisterOTPCode, verifyOTPCode, sendWelcomeEmail } from '@/api/endpoints/user'
import ModalOTPRegister from '../../modals/alertModals/ModalOTPRegister'

const NewUserAdmin = ({ onOpenModal, isLoading }: ClientAdminProps) => {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [otpLoading, setOtpLoading] = useState(false)
  const [otpError, setOtpError] = useState(false)
  const [openOTP, setOpenOTP] = useState(false)
  const [otpToken, setOtpToken] = useState<string | null>(null)
  const [pendingPayload, setPendingPayload] = useState<{
    type: 'create' | 'assign'
    data: any
  } | null>(null)

  const [existingUser, setExistingUser] = useState<FindUserWithAdminResponse | null>(null)
  const [emailFound, setEmailFound] = useState(false)

  const { user, hasRole } = useAuthStore()
  const { createConveniaUser, fetchConveniaUserByEmail } = useUserStore()
  const { selectedAdminIndex } = useAdminUiStore()
  const { roles } = useRoleStore()

  const isClientRoleType = hasRole(ROLE_TYPES.CLIENTE)
  const admin = user?.relUserRoleAdmins[selectedAdminIndex]?.admin

  /* ------------------------- formulario ------------------------- */
  const {
    formData,
    errors,
    setFormData,
    handleInputChange,
    validate,
    resetForm,
    clearErrors,
    isFormValid,
  } = useFormValidation(
    {
      name: '',
      email: '',
      phoneAdmin: '',
      profileType: '',
      adminPassword: '',
      confirmPassword: '',
    },
    emailFound
  )

  /* ---------------------- búsqueda de e-mail -------------------- */
  const lookupEmail = debounce(async (email: string) => {
    if (!isClientRoleType) return
    // if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) return

    isLoading?.(true)
    try {
      const user = await fetchConveniaUserByEmail(email)
      if (user) {
        setExistingUser(user)
        setEmailFound(true)
        setFormData(prev => ({
          ...prev,
          name: user.name ?? '',
          phoneAdmin: String(user.phone ?? ''),
          profileType: '',
          adminPassword: '',
          confirmPassword: '',
        }))
        // Limpiar errores de contraseña ya que no se necesitan para asignación de rol
        clearErrors(['adminPassword', 'confirmPassword'])
        toast.info('Usuario encontrado. Puedes asignarle un rol.')
      } else {
        if (existingUser) {
          setFormData(prev => ({ ...prev, name: '', phoneAdmin: '' }))
        }
        setExistingUser(null)
        setEmailFound(false)
      }
    } finally {
      isLoading?.(false)
    }
  }, 400)

  useEffect(() => {
    lookupEmail(formData.email)
    return () => lookupEmail.cancel()
  }, [formData.email, isClientRoleType])

  /* ------------------ envío de código de verificación ------------ */
  const sendOTPCode = async () => {
    try {
      setOtpError(false)
      setOtpLoading(true)
      const { data } = await generateRegisterOTPCode(user?.email!)
      setOtpToken(data.token)
      setOpenOTP(true)
    } catch (err) {
      console.error(err)
      toast.error('No se pudo enviar el código OTP')
    } finally {
      setOtpLoading(false)
    }
  }

  /* -------------------- envío de email de bienvenida -------------- */
  const sendWelcomeEmailToUser = async (userData: {
    email: string
    name: string
    roleId: string
  }) => {
    try {
      // Buscar el nombre del rol desde el ID
      const selectedRole = roles.find(role => role.id === userData.roleId)
      const roleName = selectedRole?.name || 'Usuario'

      // Obtener el nombre de la empresa
      const companyName = admin?.company_name || 'Convenia'

      // Crear la fecha actual en formato legible
      const signupDate = new Date().toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })

      await sendWelcomeEmail({
        email: userData.email,
        name_user: userData.name,
        company_name: companyName,
        role_name: roleName,
        signup_date: signupDate,
      })
    } catch (err) {
      console.error('Error al enviar email de bienvenida:', err)
      // No mostramos error al usuario para no interrumpir el flujo principal
    }
  }

  /* ---------------------- guardar/actualizar --------------------- */
  const handleNext = async () => {
    setIsSubmitting(true)

    const isAssignFlow = !!existingUser && isClientRoleType

    try {
      // Validación específica según el flujo
      if (isAssignFlow) {
        // Para asignación de rol, solo validar que se haya seleccionado un perfil
        if (!formData.profileType.trim()) {
          toast.error('Debes seleccionar un tipo de perfil')
          setIsSubmitting(false)
          return
        }
      } else {
        // Para creación de usuario, validar todo el formulario
        if (!validate()) {
          setIsSubmitting(false)
          return
        }
      }

      let dialing_code: string | undefined
      let country_code: string | undefined

      // Solo parseamos y validamos el teléfono si vamos a CREAR usuario
      if (!isAssignFlow) {
        const rawPhone = formData.phoneAdmin.startsWith('+')
          ? formData.phoneAdmin
          : `+${formData.phoneAdmin}`
        const phoneParsed = parsePhoneNumberFromString(rawPhone)

        if (!phoneParsed || !phoneParsed.isValid()) {
          toast.error('El número de teléfono no es válido')
          setIsSubmitting(false)
          return
        }

        dialing_code = `${phoneParsed.countryCallingCode}`
        country_code = phoneParsed.country
      }

      // Preparamos la acción pendiente según el flujo
      if (isAssignFlow) {
        setPendingPayload({
          type: 'assign',
          data: {
            userId: existingUser!.id,
            roleId: formData.profileType,
            adminId: admin ? admin.id : null,
          },
        })
      } else {
        // Parseamos de nuevo solo para extraer el nationalNumber
        const nationalNumber = parsePhoneNumberFromString(
        formData.phoneAdmin.startsWith('+')
          ? formData.phoneAdmin
          : `+${formData.phoneAdmin}`
      )!.nationalNumber;

        setPendingPayload({
          type: 'create',
          data: {
            name: formData.name.trim(),
            email: formData.email.trim(),
            phone: Number(nationalNumber),
            password: formData.adminPassword,
            created_by: 'dash',
            rol: formData.profileType.toUpperCase(),
            admin_data: admin ? admin.id : null,
            dialing_code,
            country_code,
          },
        });
      }

      // Enviamos el OTP antes de tocar la base
      await sendOTPCode()
    } catch (error) {
      setIsSubmitting(false)
      return
    }
  }

  /* -------------------- verificación del OTP -------------------- */
  const handleOtpContinue = async (code: string) => {
    try {
      setOtpLoading(true)
      const res = await verifyOTPCode({
        email: user?.email!,
        code,
        access: otpToken ?? '',
      })

      if (res.statusCode !== 200) {
        setOtpError(true)
        setIsSubmitting(false)
        return
      }

      /* --- OTP OK: ejecutamos acción pendiente --- */
      if (pendingPayload?.type === 'assign') {
        await assignRoleToConveniaUser(pendingPayload.data.userId, {
          roleId: pendingPayload.data.roleId,
          adminId: pendingPayload.data.adminId,
        })
      } else if (pendingPayload?.type === 'create') {
        await createConveniaUser(pendingPayload.data)

        // Enviar email de bienvenida solo para nuevos usuarios
        await sendWelcomeEmailToUser({
          email: pendingPayload.data.email,
          name: pendingPayload.data.name,
          roleId: formData.profileType, // Usar el ID del rol del formulario
        })
      }

      resetForm()
      setOpenOTP(false)
      onOpenModal()
    } catch (err: any) {
      // errores externos al OTP
      console.error(err)
      setOpenOTP(false)
      setOtpError(false)
      const message = getUserCreationErrorMessage(err)
      toast.error(message, {
        position: 'top-right',
        autoClose: 3000,
        pauseOnHover: true,
        draggable: true,
      })
    } finally {
      setOtpLoading(false)
      setIsSubmitting(false)
      isLoading?.(false)
    }
  }

  // Función para determinar si el botón debe estar habilitado
  const isButtonEnabled = (): boolean => {
    // Si es un usuario existente (asignación de rol), solo validar campos específicos
    if (existingUser && isClientRoleType) {
      const requiredFields = ['profileType']
      const hasEmptyFields = requiredFields.some(key => {
        const value = (formData as any)[key]
        return !value || value.toString().trim() === ''
      })

      if (hasEmptyFields) return false

      // Verificar errores de validación solo en campos relevantes (sin contraseñas)
      const relevantErrors = ['profileType']
      const hasErrors = relevantErrors.some(key => {
        const error = (errors as any)[key]
        return error && error !== ''
      })

      return !hasErrors
    }

    // Para usuarios nuevos, usar la validación completa (incluyendo contraseñas)
    return isFormValid()
  }

  const handleBack = () => router.push('/gestion-usuarios')

  return (
    <div className={styles.container}>
      <button className={styles.backButton} onClick={handleBack}>
        <FaArrowLeft size={16} className={styles.icon} /> Atrás
      </button>

      <form
        onSubmit={e => {
          e.preventDefault()
          handleNext()
        }}
      >
        <ContactForm
          formData={formData}
          onChange={handleInputChange}
          errors={errors}
          emailFound={emailFound}
        />

        <div className={styles.buttonContainer}>
          <Button
            text={isSubmitting ? 'Guardando...' : 'Crear usuario'}
            fullWidth
            disabled={!isButtonEnabled() || isSubmitting}
          />
        </div>
      </form>

      {/* Modal OTP */}
      <ModalOTPRegister
        open={openOTP}
        loading={otpLoading}
        error={otpError}
        onClose={() => {
          setOpenOTP(false)
          setOtpLoading(false)
        }}
        onResendCode={sendOTPCode}
        onContinue={handleOtpContinue}
      />
    </div>
  )
}

export default NewUserAdmin
