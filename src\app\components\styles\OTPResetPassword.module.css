.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
  text-align: center;
}
.codeContainer {
  display: flex;
  justify-content: center;
  gap: 8px;
}
.headingParent {
  align-self: flex-start;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
  margin-bottom: 25px;
}
.heading {
  margin: 0;
  position: relative;
  color: black;
}
.text {
  font-size: 15px;
  color: #000;
  flex-direction: column;
  display: flex;
  align-items: flex-start;
}
.teHemosEnviado {
  margin: 0;
}
.contentDescriptionWrapper {
  text-align: start;
}
.contentDescription {
  color: #000;
  font-size: 15px;
}
.codeWrapper {
  display: flex;
  flex-direction: column;
  margin: 11px auto 32px;
  width: fit-content;
}
.footer {
  display: flex;
  flex-direction: column;
}
.textEmail {
  /* Tittle/Small */
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 13px;
  line-height: 17px;
  /* identical to box height */
  text-align: center;

  color: #4a4b55;
  margin: 45px 0px 15px;
}
.textError {
  margin-top: 8px;

  /* material-theme/body/medium */
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height, or 143% */
  letter-spacing: 0.25px;

  /* Error */
  color: #f04438;
  text-rendering: geometricPrecision;
  text-align: left;
}
.text2 {
  align-self: stretch;
  position: relative;
  font-weight: 500;
  text-align: justify;
  color: #c3c6d8;
  font-size: 11px;
  margin-top: 48px;
}
.contactoconveniamx {
  color: #0221bd;
}
