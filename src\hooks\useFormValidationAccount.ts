import { useState } from 'react'
import { accountValidationRules } from '@/utils/validationRules'

export type FormData = {
  name: string
  email: string
  phone: string
  registrationDate: string
  affiliationNumber: string
  companyName: string
}

export type Errors = Partial<Record<keyof FormData, string>>

export const useFormValidationAccount = (initialData: FormData) => {
  const [formData, setFormData] = useState<FormData>({
    name: initialData?.name || '',
    email: initialData?.email || '',
    phone: initialData?.phone || '',
    registrationDate: initialData?.registrationDate || '',
    affiliationNumber: initialData.affiliationNumber,
    companyName: initialData?.companyName || '',
  })

  const [errors, setErrors] = useState<Errors>({})

  const validateField = (name: keyof FormData, value: string) => {
    const validationFn = accountValidationRules[name]
    if (validationFn) {
      const errorMessage = validationFn(value)
      setErrors(prev => ({ ...prev, [name]: errorMessage }))
      return errorMessage
    }
    return ''
  }

  const validateAllFields = () => {
    const newErrors: Errors = {} as Errors
    ;(Object.keys(formData) as (keyof FormData)[]).forEach(key => {
      const value = formData[key]
      const validate = accountValidationRules[key]
      if (validate) {
        const errorMessage = validate(value)
        if (errorMessage) {
          newErrors[key] = errorMessage
        }
      }
    })
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    const typedName = name as keyof FormData
    setFormData(prev => ({ ...prev, [typedName]: value }))
    validateField(typedName, value)
  }

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      registrationDate: '',
      affiliationNumber: '',
      companyName: '',
    })
    setErrors({})
  }

  return {
    formData,
    errors,
    handleChange,
    validateAllFields,
    resetForm,
    setFormData,
  }
}
