export const convertToFile = (fileObj?: {
  name: string
  size: number
  type: string
  lastModified: number
}) => {
  if (fileObj) {
    return new File([''], fileObj.name, { type: fileObj.type, lastModified: fileObj.lastModified })
  }
  return null
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const convertClientData = (data: any) => {
  if (!data) return undefined

  return {
    ...data,
    files: {
      act: convertToFile(data.files?.act),
      constancy: convertToFile(data.files?.constancy),
      file: convertToFile(data.files?.file),
      other: convertToFile(data.files?.other),
    },
  }
}
