/* eslint-disable react-hooks/exhaustive-deps */
'use client'
import { usePathname, useRouter } from 'next/navigation'
import Sidebar from '../components/Sidebar'
import AppBar from '../components/AppBar'
import styles from './styles/ClientSidebar.module.css'
import { useEffect, useState } from 'react'
import ModalLogout from './modals/alertModals/ModalLogout'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useUiStore } from '@/store/ui/useUiStore'
import LoaderOverlay from './loader/LoaderOverlay'

export default function ClientSidebar({ children }: { children: React.ReactNode }) {
  const { logout } = useAuthStore()
  const router = useRouter()
  const pathname = usePathname()
  const [logoutModalOpen, setLogoutModalOpen] = useState(false)
  const { isLoading, setLoading } = useUiStore()
  const hideSidebar = [
    '/login',
    '/register',
    '/',
    '/reset-password',
    '/reset-password/otp',
    '/reset-password/new-password',
    '/reset-password/success-reset-password',
  ].includes(pathname)


  useEffect(() => {
    setLoading(true)
    const timeout = setTimeout(() => setLoading(false), 500)
    return () => clearTimeout(timeout)
  }, [pathname])

  const handleConfirmLogout = () => {
    setLogoutModalOpen(false)
    setLoading(true)

    setTimeout(() => {
      try {
        logout()
        router.replace('/login') // no es necesario esperar aquí
      } catch (error) {
        console.error('Error during logout:', error)
      }
      // No es necesario setLoading(false)
    }, 200)
  }

  return (
    <div className={hideSidebar ? styles.containerLogin : styles.container}>
      {isLoading ? (
        <div className={styles.loaderOverlay}>
          <LoaderOverlay />
        </div>
      ) : (
        <>
          {!hideSidebar && <Sidebar onLogoutClick={() => setLogoutModalOpen(true)} />}
          {!hideSidebar && <AppBar onLogoutClick={() => setLogoutModalOpen(true)} />}

          <main className={hideSidebar ? styles.mainLogin : styles.main}>
            {children}
          </main>
        </>
      )}
      <ModalLogout
        open={logoutModalOpen}
        onClose={() => setLogoutModalOpen(false)}
        onConfirm={handleConfirmLogout}
      />
    </div>
  )
}
