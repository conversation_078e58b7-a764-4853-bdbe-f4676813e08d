import { useState } from 'react'
import { getCostCommissionsReport } from '@/api/endpoints/reports'
import { useDownload } from '../useDownload'
import { CostCommissionsReportParams, CostCommissionsReportPayload } from '@/types/reports/types'

export const useCostCommissionsReport = () => {
  const [error, setError] = useState<string | null>(null)
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: err => setError(err.message),
    loadingMessage: 'Generando reporte de costos y comisiones...',
  })

  const downloadReport = async (params: CostCommissionsReportParams) => {
    let filename = 'reporte-costos-comisiones'

    if (params.dateFrom) {
      filename += `-${params.dateFrom}`
    }

    if (params.dateTo) {
      filename += `-${params.dateTo}`
    }

    filename += '.xlsx'

    const payload: CostCommissionsReportPayload = {
      ...(params.dateFrom && { date_from: new Date(params.dateFrom) }),
      ...(params.dateTo && { date_to: new Date(params.dateTo) }),
      ...(params.typeCommission && { typeCommission: params.typeCommission }),
      ...(params.aliasCompany && { alias_company: params.aliasCompany }),
    }
    await downloadExcel(() => getCostCommissionsReport(payload), filename)
  }

  return {
    isLoading,
    loadingMessage,
    error,
    downloadReport,
  }
}
