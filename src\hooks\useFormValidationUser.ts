import { formUserValidationRules } from '@/utils/validationRules'
import { useState } from 'react'

export type FormData = {
  name: string
  email: string
  phoneAdmin: string
  profileType: string
  adminPassword: string
  confirmPassword: string
}

export type Errors = Partial<FormData>

const useFormValidationUser = (
  initialState: FormData,
  isUpdate?: boolean,
) => {
  const [formData, setFormData] = useState<FormData>(initialState)
  const [errors, setErrors] = useState<Errors>({})

  const validate = (): boolean => {
    const newErrors: Errors = {}

    Object.keys(formUserValidationRules).forEach(field => {
      const key = field as keyof FormData
      const validationRule = formUserValidationRules[key]

      if (typeof validationRule === 'function') {
        const fieldValue = formData[key] ?? ''

        if (key === 'adminPassword' && isUpdate && !fieldValue.trim()) {
          return // omitir validación en edición si está vacío
        }

        if (key === 'confirmPassword' && isUpdate && !formData.confirmPassword.trim()) {
          return // omitir validación en edición si está vacío
        }

        if (validationRule.length === 2) {
          newErrors[key] = (validationRule as (a: string, b: string) => string | undefined)(
            formData.adminPassword,
            formData.confirmPassword
          )
        } else {
          newErrors[key] = (validationRule as (a: string) => string | undefined)(fieldValue)
        }
      }
    })

    setErrors(newErrors)
    // Retorna true si NO hay errores (formulario válido)
    return !Object.values(newErrors).some(error => error !== undefined && error !== '')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target

    setFormData(prev => {
      const updatedForm = { ...prev, [name]: value }

      const updatedErrors: Errors = { ...errors }

      switch (name) {
        case 'adminPassword':
          updatedErrors.adminPassword = formUserValidationRules.adminPassword(
            updatedForm.adminPassword
          )
          updatedErrors.confirmPassword = formUserValidationRules.confirmPassword(
            updatedForm.adminPassword,
            updatedForm.confirmPassword
          )
          break
        case 'confirmPassword':
          updatedErrors.confirmPassword = formUserValidationRules.confirmPassword(
            updatedForm.adminPassword,
            updatedForm.confirmPassword
          )
          break
        default:
          const rule = formUserValidationRules[name as keyof FormData] as (val: string) => string
          updatedErrors[name as keyof FormData] = rule(value)
          break
      }

      setErrors(updatedErrors)
      return updatedForm
    })
  }

  const resetForm = () => {
    setFormData(initialState)
    setErrors({})
  }

  const clearErrors = (fields: (keyof FormData)[]) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      fields.forEach(field => {
        delete newErrors[field]
      })
      return newErrors
    })
  }

  // Función para verificar si el formulario es válido sin actualizar errores
  const isFormValid = (): boolean => {
    // En modo edición, las contraseñas son opcionales
    const requiredFields = isUpdate
      ? (['name', 'email', 'phoneAdmin', 'profileType'] as (keyof FormData)[])
      : (Object.keys(formData) as (keyof FormData)[])

    // Verificar que los campos requeridos estén completos
    const hasEmptyFields = requiredFields.some(key => {
      const value = formData[key]
      return !value || value.toString().trim() === ''
    })

    if (hasEmptyFields) return false

    // Verificar que no haya errores de validación
    const hasValidationErrors = Object.values(errors).some(error => error !== undefined && error !== '')

    return !hasValidationErrors
  }

  return {
    formData,
    errors,
    setFormData,
    validate,
    handleInputChange,
    resetForm,
    clearErrors,
    isFormValid,
  }
}

export default useFormValidationUser
