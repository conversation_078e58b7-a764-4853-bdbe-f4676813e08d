'use client'
import { useState } from 'react'
import { AmountsReportFilters } from '@/types/reports/amounts'
import { getAmountsReport } from '@/api/endpoints/reports'
import { useDownload } from '../useDownload'

export const useAmountsReport = () => {
  const [error, setError] = useState<string | null>(null)
  const { downloadExcel, isLoading, loadingMessage } = useDownload({
    onError: (err) => setError(err.message),
    loadingMessage: 'Generando reporte de Saldos por Cuenta...'
  })

  const downloadReport = async (params: AmountsReportFilters) => {
    const filename = `reporte-saldos-cuenta-${new Date().toISOString().split('T')[0]}.xlsx`
    await downloadExcel(
      () => getAmountsReport(params),
      filename
    )
  }

  return {
    isLoading,
    loadingMessage,
    error,
    downloadReport
  }
}
