/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
import styles from '../../styles/Transaction.module.css'
import formStyles from '../../styles/NewClientAdmin.module.css'
import But<PERSON> from '../../Button'
import Select from '../../Select'
import GoldSwitch from '../../GoldSwitch'
import Input from '../../Input'
import { handleAlphaNumericWithDashInput, handleNumberOnlyInput } from '@/utils/inputRestrictions'
import { TransactionFormData } from '@/hooks/useFormValidationTransaction'
import { ChangeEvent, useEffect, useState } from 'react'
import InputAutoComplete from '../../InputAutoComplete'
import { NumericFormat } from 'react-number-format'
import { AdminByUserIdClient } from '@/types/admin/types'
import { Bank } from '@/types/transfers/types'
import { useAuthStore } from '@/store/auth/useAuthStore'
import { useContactStore } from '@/store/contact/useContactStore'
import { BANK_TYPES, detectBankName } from '@/utils/detectBank'
import { AccountDetails } from '@/types/account/types'

type Props = {
  formData: TransactionFormData
  errors: { [key: string]: string }
  touched: { [key: string]: boolean }
  isFormValid: boolean
  showSwitch?: boolean
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void
  handleBlur: (name: keyof TransactionFormData) => void
  onToggleSwitch: (value: boolean) => void
  onHandleSubmit: (e: React.FormEvent) => void
  adminsByUserId: AdminByUserIdClient[]
  banks: Bank[] // 👈 nuevo prop
  emailEnterpriseOut: string
  setEmailEnterpriseOut: (email: string) => void
  setBankLegalCode: (legalCode: string) => void
  isConveniaAdmin?: boolean
  amountTransfer?: number | null
  handleSelectCompany: (companyName: string) => void
  accountDetails: AccountDetails | null
}

const TransactionsForm: React.FC<Props> = ({
  formData,
  errors,
  touched,
  isFormValid,
  showSwitch = true,
  handleInputChange,
  handleBlur,
  onToggleSwitch,
  onHandleSubmit,
  adminsByUserId,
  banks,
  setBankLegalCode,
  amountTransfer,
  handleSelectCompany,
  accountDetails,
}) => {
  // Check if the transfer type is SPEI
  const isSPEITransfer = formData.tipoTransferencia === 'SPEI'
  const [isContactSaved, setIsContactSaved] = useState(false)
  const [showSwitchSaveContact, setShowSwitchSaveContact] = useState(showSwitch)
  const { user } = useAuthStore()
  const { contacts, loadContacts } = useContactStore()

  const generateRandomReference = () => {
    return Math.floor(1000000 + Math.random() * 9000000).toString() // entre 1000000 y 9999999
  }

  // Effect para actualizar el tipo de transferencia cuando se llena la cuenta destino
  // reemplazar despues por una llamda a la api para verificar el tipo de transferencia
  // y actualizar el estado de tipoTransferencia
  useEffect(() => {
    const cuenta = formData.cuentaDestino || ''
    const detection = detectBankName(cuenta, banks)
    setBankLegalCode(detection.legalCode || '')

    // Asigna nombre del banco
    if (cuenta.length >= 3 && detection.name !== formData.banco) {
      handleInputChange({
        target: {
          name: 'banco',
          value: detection.name,
        },
      } as React.ChangeEvent<HTMLInputElement>)
      return
    }

    // Si se borró la cuenta
    if (!cuenta && (formData.banco || formData.tipoTransferencia || formData.tipoPago)) {
      handleInputChange({
        target: { name: 'banco', value: '' },
      } as React.ChangeEvent<HTMLInputElement>)
      handleInputChange({
        target: { name: 'tipoTransferencia', value: '' },
      } as React.ChangeEvent<HTMLInputElement>)
      handleInputChange({
        target: { name: 'tipoPago', value: '' },
      } as React.ChangeEvent<HTMLInputElement>)
      return
    }

    // Actualiza tipo de transferencia y pago según banco detectado
    if (formData.banco) {
      const isConvenia =
        detection.type === BANK_TYPES.CONVENIA || detection.type === BANK_TYPES.TRANSFER
      const tipoTransferencia = isConvenia ? 'CONVENIA' : 'SPEI'
      const tipoPago = detection.type === BANK_TYPES.CONVENIA ? 'P2P' : 'TERCERO A TERCERO'

      if (formData.tipoTransferencia !== tipoTransferencia) {
        handleInputChange({
          target: { name: 'tipoTransferencia', value: tipoTransferencia },
        } as React.ChangeEvent<HTMLInputElement>)
        return
      }

      if (formData.tipoPago !== tipoPago) {
        handleInputChange({
          target: { name: 'tipoPago', value: tipoPago },
        } as React.ChangeEvent<HTMLInputElement>)
        return
      }
    }

    // Generar referencia si es SPEI
    if (formData.tipoTransferencia === 'SPEI' && !formData.numeroReferencia) {
      const ref = generateRandomReference()
      handleInputChange({
        target: { name: 'numeroReferencia', value: ref },
      } as React.ChangeEvent<HTMLInputElement>)
    }
  }, [
    formData.cuentaDestino,
    formData.banco,
    formData.tipoTransferencia,
    formData.tipoPago,
    formData.numeroReferencia,
    banks,
    handleInputChange,
  ])

  // Función para manejar cambios en el campo de importe con NumericFormat
  const handleImporteChange = (values: { value: string }) => {
    const event = {
      target: {
        name: 'importe',
        value: values.value,
      },
    } as React.ChangeEvent<HTMLInputElement>

    handleInputChange(event)
  }

  useEffect(() => {
    if (!accountDetails) return

    const cuentaSalida =
      formData.banco === 'CONVENIA' || formData.banco === 'Transfer / Convenia'
        ? accountDetails.accountNumberConveniaComplete
        : accountDetails.accountNumberTransfer

    // Solo actualiza si la cuentaDestino es distinta a la actual
    if (formData.numeroCuenta !== cuentaSalida) {
      handleInputChange({
        target: {
          name: 'numeroCuenta',
          value: cuentaSalida,
        },
      } as React.ChangeEvent<HTMLInputElement>)
    }
  }, [accountDetails, formData.tipoTransferencia])

  useEffect(() => {
    if (user?.id && user?.email) {
      loadContacts(user.id, user.email)
    }
  }, [user])

  useEffect(() => {
    // Validar si la cuenta destino ya existe en contactos
    const existe = contacts.some(contacto => contacto.num_clabe === formData.cuentaDestino)
    if (existe && formData.guardarDestinatario) {
      setIsContactSaved(existe)
      onToggleSwitch(false) // Desactiva el switch si ya existe
      setShowSwitchSaveContact(false) // Mostrar el switch solo si no existe el contacto
      setTimeout(() => {
        setIsContactSaved(false) // Mostrar el switch después de 2 segundos
      }, 5000)
    } else if (!existe && !formData.guardarDestinatario) {
      setIsContactSaved(false)
      setShowSwitchSaveContact(true) // Mostrar el switch si no existe el contacto
    }
  }, [formData.cuentaDestino, contacts, formData.guardarDestinatario, onToggleSwitch])

  return (
    <form onSubmit={onHandleSubmit}>
      <div className={styles.wrapper}>
        <div className={styles.selectWrapper}>
          <Select
            label="Elige desde que empresa quieres transferir *"
            name="empresa"
            value={formData.empresa}
            onChange={e => {
              handleSelectCompany(e.target.value)
              handleInputChange(e)
            }}
            onBlur={() => handleBlur('empresa')}
            isError={(touched.empresa && !!errors.empresa) ?? false}
            errorText={errors.empresa}
          >
            <option value="0">Elegir empresa</option>
            {adminsByUserId.map(admin => (
              <option key={admin.id} value={admin.alias}>
                {admin.alias}
              </option>
            ))}
          </Select>
        </div>

        <div className={formStyles.inputGroup}>
          <InputAutoComplete
            label="Escribe la Cuenta CONVENIA/CLABE interbancaria/Número de tarjeta desde donde deseas realizar la transferencia * "
            type="text"
            name="numeroCuenta"
            placeholder="Escribe aquí"
            value={formData.numeroCuenta}
            onKeyDown={handleAlphaNumericWithDashInput}
            onChange={e => handleInputChange(e)}
            onBlur={() => handleBlur('numeroCuenta')}
            isError={(touched.numeroCuenta && !!errors.numeroCuenta) ?? false}
            errorText={errors.numeroCuenta}
            disabled
            autoComplete="off"
            suggestions={['9211111111234567890', '1234567892130', '929384723q4232']}
            style={{
              cursor: 'not-allowed',
            }}
            onSelectSuggestion={value => {
              const event = {
                target: {
                  name: 'numeroCuenta',
                  value: value,
                },
              } as React.ChangeEvent<HTMLInputElement>
              handleInputChange(event)
            }}
          />
        </div>

        <div className={formStyles.formContainer}>
          <div className={formStyles.inputGroup}>
            <InputAutoComplete
              label="Cuenta destino *"
              type="text"
              name="cuentaDestino"
              placeholder="Número de cuenta"
              value={formData.cuentaDestino}
              onKeyDown={handleNumberOnlyInput}
              onChange={e => handleInputChange(e)}
              onBlur={() => handleBlur('cuentaDestino')}
              maxLength={
                formData.banco === 'CONVENIA'
                  ? formData.cuentaDestino.startsWith('221')
                    ? 11
                    : 16
                  : 18
              } // Limitar a 16 o 18 dígitos
              isError={(touched.cuentaDestino && !!errors.cuentaDestino) ?? false}
              errorText={errors.cuentaDestino}
              autoComplete="off"
              suggestions={['1234567890', '9293847', '1233292']}
              onSelectSuggestion={value => {
                const event = {
                  target: {
                    name: 'cuentaDestino',
                    value: value,
                  },
                } as React.ChangeEvent<HTMLInputElement>

                handleInputChange(event)
              }}
            />
            {isContactSaved ? (
              <p className={formStyles.success}>
                {'Este destinatario ya está registrado en la lista de contactos.'}
              </p>
            ) : null}
          </div>
          <div className={formStyles.inputGroup}>
            <Input
              label="Tipo de transferencia"
              type="text"
              name="tipoTransferencia"
              placeholder="Tipo"
              disabled
              value={formData.tipoTransferencia}
              onChange={e => handleInputChange(e)}
              onBlur={() => handleBlur('tipoTransferencia')}
              isError={(touched.tipoTransferencia && !!errors.tipoTransferencia) ?? false}
              errorText={errors.tipoTransferencia}
              style={{
                cursor: 'not-allowed',
              }}
            />
          </div>
        </div>
        <div className={styles.threeInputsContainer}>
          <div className={formStyles.inputGroup}>
            <Input
              label="Banco"
              type="text"
              name="banco"
              placeholder="Tipo"
              value={formData.banco}
              onChange={e => handleInputChange(e)}
              onBlur={() => handleBlur('banco')}
              isError={(touched.banco && !!errors.banco) ?? false}
              errorText={errors.banco}
              disabled
              style={{
                cursor: 'not-allowed',
              }}
            />
          </div>
          {isSPEITransfer && (
            <div className={formStyles.inputGroup}>
              <Input
                label="Número de referencia"
                type="text"
                name="numeroReferencia"
                placeholder="Referencia"
                value={formData.numeroReferencia || '1234567'}
                disabled
                maxLength={7} // Limitar a 7 dígit
                onKeyDown={handleNumberOnlyInput}
                onChange={e => handleInputChange(e)}
                onBlur={() => handleBlur('numeroReferencia')}
                isError={(touched.numeroReferencia && !!errors.numeroReferencia) ?? false}
                errorText={errors.numeroReferencia}
                style={{
                  cursor: 'not-allowed',
                }}
              />
            </div>
          )}
          <div className={formStyles.inputGroup}>
            <label className={formStyles.label}>Importe *</label>
            <NumericFormat
              name="importe"
              placeholder="$ Importe"
              className={`${formStyles.input} ${styles.montoInput} ${touched.importe && !!errors.importe ? formStyles.inputError : ''}`}
              value={formData.importe}
              onValueChange={handleImporteChange}
              onBlur={() => handleBlur('importe')}
              thousandSeparator=","
              decimalSeparator="."
              prefix="$ "
              decimalScale={2}
              fixedDecimalScale={true}
              allowNegative={false}
            />
            {touched.importe && errors.importe && (
              <p className={formStyles.error}>{errors.importe}</p>
            )}
            {typeof amountTransfer === 'number' && amountTransfer < Number(formData.importe) ? (
              <p className={formStyles.error}>{'Saldo insuficiente'}</p>
            ) : null}
          </div>
        </div>
        <div className={formStyles.formContainer}>
          <div className={formStyles.inputGroup}>
            <Input
              label="Nombre del beneficiario *"
              type="text"
              maxLength={39}
              name="nombreBeneficiario"
              placeholder="Nombre beneficiario"
              value={formData.nombreBeneficiario}
              onInput={(e: ChangeEvent<HTMLInputElement>) => {
                e.target.value = e.target.value.replace(/[^A-Za-záéíóúÁÉÍÓÚñÑ ]/g, '');
              }}
              onChange={e => handleInputChange(e)}
              onBlur={() => handleBlur('nombreBeneficiario')}
              isError={(touched.nombreBeneficiario && !!errors.nombreBeneficiario) ?? false}
              errorText={errors.nombreBeneficiario}
            />
          </div>
          <div className={formStyles.inputGroup}>
            <Input
              label="RFC beneficiario"
              type="text"
              name="rfcBeneficiario"
              placeholder="RFC"
              style={{ textTransform: 'uppercase' }}
              value={formData.rfcBeneficiario}
              onChange={e => handleInputChange(e)}
              onBlur={() => handleBlur('rfcBeneficiario')}
              isError={(touched.rfcBeneficiario && !!errors.rfcBeneficiario) ?? false}
              errorText={errors.rfcBeneficiario}
              maxLength={13}
            />
          </div>
        </div>
        <div className={formStyles.inputGroup}>
          <Input
            label="Correo electrónico del beneficiario"
            type="text"
            name="correo"
            placeholder="Agregar correo"
            value={formData.correo}
            onChange={e => handleInputChange(e)}
            onBlur={() => handleBlur('correo')}
            isError={(touched.correo && !!errors.correo) ?? false}
            errorText={errors.correo}
          />
        </div>
        <div className={formStyles.formContainer}>
          <div className={formStyles.inputGroup}>
            <Input
              label="Concepto destinatario *"
              type="text"
              name="conceptoBeneficiario"
              placeholder="Concepto"
              value={formData.conceptoBeneficiario}
              onInput={(e: ChangeEvent<HTMLInputElement>) => {
                e.target.value = e.target.value.replace(/[^A-Za-záéíóúÁÉÍÓÚñÑ ]/g, '');
              }}
              onChange={e => handleInputChange(e)}
              onBlur={() => handleBlur('conceptoBeneficiario')}
              isError={(touched.conceptoBeneficiario && !!errors.conceptoBeneficiario) ?? false}
              errorText={errors.conceptoBeneficiario}
              maxLength={30}
            />
          </div>
          <div className={styles.selectWrapper}>
            <Input
              label="Tipo de pago"
              type="text"
              name="tipoPago"
              placeholder="Tipo pago"
              disabled
              value={formData.tipoPago}
              onChange={e => handleInputChange(e)}
              onBlur={() => handleBlur('tipoPago')}
              isError={(touched.tipoPago && !!errors.tipoPago) ?? false}
              errorText={errors.tipoPago}
              style={{
                cursor: 'not-allowed',
              }}
            />
          </div>
        </div>
        {showSwitchSaveContact && (
          <div className={styles.switchWrapper}>
            <p className={styles.swhitchLabel}>¿Deseas guardar este destinatario en contactos?</p>
            <GoldSwitch
              value={formData.guardarDestinatario}
              onToggle={() => onToggleSwitch(!formData.guardarDestinatario)}
            />
          </div>
        )}
        <Button
          text="Realizar transferencia"
          fullWidth
          disabled={
            !isFormValid || (amountTransfer != null && amountTransfer < Number(formData.importe))
          }
        // onClick={onPress}
        />
      </div>
    </form>
  )
}
export default TransactionsForm
