import React from 'react'
import classNames from 'classnames'
import styles from './styles/Skeleton.module.css'

type SkeletonProps = {
  width?: string | number
  height?: string | number
  rounded?: boolean
  circle?: boolean
  className?: string
  margin?: string
}

const Skeleton: React.FC<SkeletonProps> = ({
  width,
  height,
  rounded,
  circle,
  className,
  margin
}) => {
  const style: React.CSSProperties = {
    width,
    height,
    minWidth: width,
    minHeight: height,
    margin
  }

  return (
    <div
      className={classNames(
        styles.skeleton,
        rounded && styles.rounded,
        circle && styles.circle,
        className
      )}
      style={style}
    />
  )
}

export default Skeleton
