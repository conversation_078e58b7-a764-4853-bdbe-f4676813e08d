export const handleTextOnlyInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.ctrlKey || e.metaKey) return
  if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/.test(e.key) && e.key !== 'Backspace') {
    e.preventDefault()
  }
}

export const handleNumberOnlyInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.ctrlKey || e.metaKey) return
  if (!/^[0-9]+$/.test(e.key) && e.key !== 'Backspace') {
    e.preventDefault()
  }
}

export const handleAlphaNumericInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.ctrlKey || e.metaKey) return
  if (!/^[a-zA-Z0-9áéíóúÁÉÍÓÚñÑ]+$/.test(e.key) && e.key !== 'Backspace') {
    e.preventDefault()
  }
}

export const handleUppercaseInput = (e: React.ChangeEvent<HTMLInputElement>) => {
  e.target.value = e.target.value.toUpperCase()
}

export const handleAlphaNumericWithDashInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.ctrlKey || e.metaKey) return
  if (!/^[a-zA-Z0-9\-]$/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Tab') {
    e.preventDefault()
  }
}

export const handleDecimalInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.ctrlKey || e.metaKey) return
  const allowedKeys = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete']
  const isNumber = /^\d$/.test(e.key)

  const value = e.currentTarget.value
  const cursorPosition = e.currentTarget.selectionStart || 0

  // Bloquea cualquier carácter que no sea número, punto o tecla permitida
  if (!isNumber && e.key !== '.' && !allowedKeys.includes(e.key)) {
    e.preventDefault()
    return
  }

  // Bloquea más de un punto decimal
  if (e.key === '.' && value.includes('.')) {
    e.preventDefault()
    return
  }

  // Si ya hay un punto y se va a escribir después de él, revisa cuántos decimales hay
  const decimalIndex = value.indexOf('.')
  if (decimalIndex !== -1 && cursorPosition > decimalIndex) {
    const decimals = value.split('.')[1] || ''
    if (decimals.length >= 3 && isNumber) {
      e.preventDefault()
      return
    }
  }
}
