/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import * as userApi from '@/api/endpoints/user'
import {
  UserData,
  UserResponse,
  UserConveniaData,
  FindUserWithAdminResponse,
} from '@/types/user/types'
import { CommissionTransfer } from '@/types/commissions/types'

interface UserState {
  users: UserResponse[]
  userSelected: UserResponse | FindUserWithAdminResponse | null
  loading: boolean
  error: string | null
  totalAccountsDock: number
  totalAccountsAdmin: number
  totalAccountsTransfer: number
  totalCommissionsTransfer: number,
  totalBalance: string,
  commissions: CommissionTransfer[]
  credencialsBank: {
    clabe: string
    convenia_account: string
    member_ship: number
  } | null

  fetchConveniaUsers: (params: {
    page: number
    limit: number
    q: string
    admin?: string | null
    _t?: number
  }) => Promise<void>
  fetchConveniaUserById: (id: string) => Promise<UserResponse | null>
  fetchUserById: (id: string) => Promise<void>
  fetchUserByEmail: (email: string) => Promise<void>
  fetchAllUsers: () => Promise<void>
  fetchTotalAccountsDock: (adminId?: string | null) => Promise<void>
  fetchTotalAccountsTransfer: (adminId?: string | null) => Promise<void>
  fetchConveniaUserByEmail: (email: string) => Promise<FindUserWithAdminResponse | null>
  fetchUserCredencialsBank: (id: string | null) => Promise<void>
  fetchUserCommissionBalance: () => Promise<void>
  fetchUserCommissionTransfers: (params: {
    sort: string
    page: number
    transfer_way: string
  }) => Promise<void>

  createAppUser: (data: UserData) => Promise<void>
  createAdminUser: (data: UserData) => Promise<void>
  createConveniaUser: (data: UserConveniaData) => Promise<void>
  assignRoleToConveniaUser: (id: string, roleId: string, adminId: string) => Promise<void>
  updateUser: (id: string, data: Partial<UserData>) => Promise<void>
  updateConveniaUser: (id: string, data: Partial<UserConveniaData>) => Promise<void>
  resetPassword: (email: string, password: string) => Promise<void>
  changePassword: (userId: string, newPassword: string) => Promise<void>
  deleteUser: (id: string, adminId: string | null) => Promise<void>
  clearSelected: () => void
}

export const useUserStore = create<UserState>()(set => ({
  users: [],
  userSelected: null,
  loading: false,
  error: null,
  totalAccountsDock: 0,
  totalAccountsTransfer: 0,
  totalAccountsAdmin: 0,
  totalCommissionsTransfer: 0,
  credencialsBank: null,
  totalBalance: '',
  commissions: [],

  fetchConveniaUsers: async params => {
    set({ loading: true, error: null })
    try {
      const { users, count } = await userApi.getConveniaUsers(params)
      const sortedUsers = users.sort(
        (a: { createdAt: string | number | Date }, b: { createdAt: string | number | Date }) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      set({ users: sortedUsers, totalAccountsAdmin: count })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al obtener usuarios Convenia' })
    } finally {
      set({ loading: false })
    }
  },

  fetchConveniaUserById: async (id: string): Promise<UserResponse | null> => {
    set({ loading: true, error: null })
    try {
      const user = await userApi.getConveniaUserById(id)
      set({ userSelected: user })
      return user
    } catch (err: any) {
      set({
        error: err?.response?.data?.message || 'Error al obtener usuario Convenia',
        userSelected: null,
      })
      return null
    } finally {
      set({ loading: false })
    }
  },

  fetchUserById: async id => {
    set({ loading: true, error: null })
    try {
      const user = await userApi.getUserById(id)
      set({ userSelected: user })
    } catch (err: any) {
      set({ error: err?.response?.data?.message || 'Error al obtener usuario' })
    } finally {
      set({ loading: false })
    }
  },

  fetchUserByEmail: async email => {
    try {
      const user = await userApi.getUserByEmail(email)
      set({ userSelected: user })
    } catch {
      set({ userSelected: null })
    }
  },

  fetchAllUsers: async () => {
    try {
      const users = await userApi.getAllUsers()
      set({ users })
    } catch {
      set({ users: [] })
    }
  },

  fetchTotalAccountsDock: async (adminId?: string | null) => {
    try {
      const res = await userApi.getTotalAccountsDock(adminId)
      set({ totalAccountsDock: res.count })
    } catch {
      set({ totalAccountsDock: 0 })
    }
  },

  fetchTotalAccountsTransfer: async (adminId?: string | null) => {
    try {
      const res = await userApi.getTotalAccountsTransfer(adminId)
      set({ totalAccountsTransfer: res.count })
    } catch {
      set({ totalAccountsTransfer: 0 })
    }
  },

  fetchConveniaUserByEmail: async email => {
    set({ loading: true, error: null })
    try {
      const user = await userApi.getConveniaUserByEmail(email)
      set({ userSelected: user })
      return user
    } catch (err: any) {
      set({
        error: err?.response?.data?.message || 'Error al obtener usuario con admin',
        userSelected: null,
      })
      return null
    } finally {
      set({ loading: false })
    }
  },

  fetchUserCredencialsBank: async (id: string | null) => {
    set({ loading: true, error: null })
    try {
      const data = await userApi.getUserCredencialsBank(id)
      set({ credencialsBank: data.data })
    } catch {
      set({ loading: false })
    }
  },

  fetchUserCommissionBalance: async () => {
    try {
      const data = await userApi.getCommissionBalance()
      set({ totalBalance: data.balance })
    } catch {
      set({ totalBalance: '' })
    }
  },

  fetchUserCommissionTransfers: async (params) => {
    set({ loading: true, error: null })
    try {
      const { items, pagination: { total_items }} = await userApi.getCommissionTransfers(params)
      set({ commissions: items, totalCommissionsTransfer: total_items })
    } catch (err: any) {
      set({ commissions: [], totalCommissionsTransfer: 0, error: err?.response?.data?.message || 'Error al obtener transferencias de comisiones' })
    } finally {
      set({ loading: false })
    }
  },

  createAppUser: async data => {
    await userApi.createAppUser(data)
  },

  createAdminUser: async data => {
    await userApi.createAdminUser(data)
  },

  createConveniaUser: async data => {
    await userApi.createConveniaUser(data)
  },

  assignRoleToConveniaUser: async (id, roleId, adminId) => {
    await userApi.assignRoleToConveniaUser(id, { roleId, adminId })
  },

  updateUser: async (id, data) => {
    await userApi.updateUser(id, data)
  },

  updateConveniaUser: async (id, data) => {
    await userApi.updateConveniaUser(id, data)
  },

  resetPassword: async (email, password) => {
    await userApi.resetPassword({ email, password })
  },

  changePassword: async (userId, newPassword) => {
    await userApi.changePassword({ userId, newPassword })
  },

  deleteUser: async (id: string, adminId: string | null) => {
    await userApi.deleteUser(id, adminId)
  },

  clearSelected: () => set({ userSelected: null }),
}))
